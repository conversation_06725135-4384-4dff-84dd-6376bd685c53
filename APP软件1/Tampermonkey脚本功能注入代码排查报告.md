# APP软件中Tampermonkey脚本功能注入代码排查报告

## 📋 排查概述

本报告详细排查了APP软件中所有与Tampermonkey脚本功能注入相关的代码文件，包括脚本加载、URL匹配、注入逻辑、脚本执行等核心功能。

## 🗂️ 核心代码文件清单

### 1. 主要注入器文件

#### `APP软件1/xiaomeihua-app/src/kamiyan-injector.js`
**功能**: 核心脚本注入器，负责所有脚本相关功能
- **URL匹配函数**: `matchUrl()` 和 `isUrlMatch()`
- **脚本获取函数**: 
  - `getKamiyanScript()` - 获取卡密验证脚本
  - `getXinkamiScript()` - 获取xinkami.js脚本
  - `getTampermonkeyScript()` - 获取tampermonkey浏览器脚本
  - `getAIKnowledgeScript()` - 获取AI知识库脚本
- **注入脚本生成**: `createInjectionScript()`

#### `APP软件1/xiaomeihua-app/src/main.js`
**功能**: 主进程文件，包含脚本注入的核心逻辑
- **脚本注入函数**: `injectScript()` (第4325-4826行)
- **Tampermonkey扩展管理**: 扩展加载、解压、配置
- **URL匹配检查**: 严格的URL匹配验证逻辑
- **脚本执行控制**: 基于URL匹配结果决定是否注入脚本

### 2. 预加载脚本文件

#### `APP软件1/xiaomeihua-app/src/preload-browser.js`
**功能**: 浏览器环境预加载脚本
- **链接拦截**: 拦截页面中的所有链接点击
- **window.open覆盖**: 重写window.open方法
- **IPC通信**: 与主进程通信的桥梁

#### `APP软件1/src/preload-browser.js`
**功能**: 另一个预加载脚本版本
- **链接拦截**: 类似功能但实现略有不同

### 3. 渲染进程文件

#### `APP软件1/xiaomeihua-app/src/renderer/main.html`
**功能**: 主渲染进程HTML文件
- **WebView脚本注入**: 通过dom-ready事件注入脚本
- **动态脚本加载**: 缓存机制和脚本注入逻辑

## 🔧 脚本资源文件

### 实际脚本文件路径
根据代码分析，以下是脚本文件的查找路径：

1. **kamiyanzheng.js** (卡密验证脚本)
   - `process.resourcesPath/resources/kamiyanzheng.js`
   - `__dirname/../resources/kamiyanzheng.js`
   - `__dirname/../../kamiyanzheng.js`
   - `__dirname/../kamiyanzheng.js`

2. **xinkami.js** (新卡密脚本)
   - `process.resourcesPath/resources/xinkami.js`
   - `__dirname/../resources/xinkami.js`
   - `__dirname/../../xinkami.js`
   - `__dirname/../xinkami.js`

3. **tampermonkey浏览器脚本.js**
   - `process.resourcesPath/resources/tampermonkey浏览器脚本.js`
   - `__dirname/../resources/tampermonkey浏览器脚本.js`
   - `__dirname/../../tampermonkey浏览器脚本.js`
   - `__dirname/../tampermonkey浏览器脚本.js`

4. **tampermonkey_stable.crx** (Tampermonkey扩展)
   - `process.resourcesPath/resources/tampermonkey_stable.crx`
   - `__dirname/../resources/tampermonkey_stable.crx`
   - `__dirname/../tampermonkey_stable.crx`

### 当前资源目录状态
检查发现 `APP软件1/xiaomeihua-app/resources/` 目录仅包含：
- `icon.ico`
- `修复已损坏.command`
- `安装前先打开.txt`

**⚠️ 重要发现**: 缺少核心脚本文件！

## 🎯 URL匹配机制

### URL匹配逻辑流程
1. **获取脚本信息**: 从后台API获取脚本的match_urls配置
2. **URL匹配检查**: 使用`matchUrl()`函数验证当前页面URL
3. **匹配规则支持**:
   - 通配符 `*` 匹配所有页面
   - 域名匹配 `https://example.com/*`
   - 子域名匹配 `https://*.example.com/*`
   - 协议通配符 `*://example.com/*`
   - 端口匹配 `https://example.com:8080/*`

### 匹配算法特点
- **优先级**: 字符串包含匹配 > 正则表达式匹配
- **容错性**: 支持多种URL格式和通配符
- **性能优化**: 短路求值，找到匹配即返回

## 🔄 脚本注入流程

### 注入时机
1. **页面加载完成**: 监听`dom-ready`事件
2. **URL匹配通过**: 严格验证URL匹配规则
3. **脚本资源可用**: 确保脚本文件存在

### 注入步骤
1. **环境检查**: 验证许可证密钥和店铺信息
2. **URL验证**: 使用matchUrl函数检查当前页面
3. **脚本获取**: 从文件系统读取脚本内容
4. **脚本组合**: 将API环境、功能脚本组合
5. **执行注入**: 通过executeJavaScript注入到页面

## 🧪 测试文件

### 测试代码文件
1. **`APP软件1/xiaomeihua-app/test_url_matching.js`**
   - URL匹配功能的完整测试套件
   - 包含性能测试和边界条件测试

2. **`网站后台/test_url_matching.php`**
   - 后端URL匹配验证测试
   - 数据库操作测试

3. **`网站后台/test_url_matching_fix.php`**
   - URL匹配功能修复验证

4. **`网站后台/comprehensive_test.sh`**
   - 系统综合功能测试脚本

## ⚠️ 发现的问题

### 1. 缺少核心脚本文件
- `kamiyanzheng.js` 文件不存在
- `xinkami.js` 文件不存在  
- `tampermonkey浏览器脚本.js` 文件不存在
- `tampermonkey_stable.crx` 扩展文件不存在

### 2. 路径配置问题
- 多个路径查找逻辑，但实际文件缺失
- 开发环境和生产环境路径不一致

### 3. 错误处理
- 脚本文件缺失时的降级处理机制
- 网络请求失败的重试机制

## 🔧 修复建议

### 1. 补充缺失文件
```bash
# 需要在resources目录添加以下文件：
APP软件1/xiaomeihua-app/resources/
├── kamiyanzheng.js
├── xinkami.js  
├── tampermonkey浏览器脚本.js
└── tampermonkey_stable.crx
```

### 2. 路径统一
- 统一脚本文件的查找路径
- 添加文件存在性检查
- 完善错误日志记录

### 3. 测试验证
- 运行现有测试套件
- 验证URL匹配功能
- 测试脚本注入流程

## 📊 代码统计

- **核心文件数量**: 4个主要文件
- **代码总行数**: 约8000+行
- **测试文件数量**: 4个测试文件
- **支持的匹配规则**: 7种URL匹配模式

## 🎯 结论

APP软件的Tampermonkey脚本功能注入系统架构完整，代码逻辑清晰，但存在关键资源文件缺失的问题。需要补充核心脚本文件才能正常工作。

## 📁 完整文件清单

### 主要代码文件
```
APP软件1/xiaomeihua-app/src/
├── kamiyan-injector.js          # 核心注入器 (1084行)
├── main.js                      # 主进程文件 (4826行+)
├── preload-browser.js           # 预加载脚本 (200行+)
└── renderer/main.html           # 渲染进程HTML (6000行+)

APP软件1/src/
└── preload-browser.js           # 另一个预加载脚本版本
```

### 测试文件
```
APP软件1/xiaomeihua-app/
├── test_url_matching.js         # URL匹配测试 (162行)
└── scripts/
    ├── post-package-fix.js      # 打包后修复脚本
    └── ultimate-fix.js          # 终极修复脚本

网站后台/
├── test_url_matching.php       # 后端URL匹配测试 (150行)
├── test_url_matching_fix.php   # URL匹配修复测试
└── comprehensive_test.sh       # 综合测试脚本 (134行)
```

### 资源文件 (缺失)
```
APP软件1/xiaomeihua-app/resources/
├── kamiyanzheng.js             # ❌ 缺失 - 卡密验证脚本
├── xinkami.js                  # ❌ 缺失 - 新卡密脚本
├── tampermonkey浏览器脚本.js    # ❌ 缺失 - 主功能脚本
└── tampermonkey_stable.crx     # ❌ 缺失 - TM扩展文件
```

### 辅助文件
```
APP软件1/xiaomeihua-app/
├── start.js                    # 启动脚本
├── package.json                # 项目配置
└── resources/
    ├── 修复已损坏.command       # macOS修复脚本
    └── 安装前先打开.txt         # 安装说明
```

## 🚨 关键发现

1. **脚本注入系统完整**: 包含完整的URL匹配、脚本加载、注入执行流程
2. **多重容错机制**: 支持多路径查找、降级处理、错误重试
3. **资源文件全部缺失**: 4个核心脚本文件都不存在于resources目录
4. **测试覆盖完整**: 包含前端、后端、集成测试等多层测试

## 🔧 立即行动项

1. **补充脚本文件**: 将缺失的4个核心脚本文件添加到resources目录
2. **验证功能**: 运行test_url_matching.js测试URL匹配功能
3. **测试注入**: 验证脚本注入流程是否正常工作
4. **完善日志**: 增强错误处理和调试日志输出
