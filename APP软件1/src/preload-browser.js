const { ipc<PERSON><PERSON><PERSON> } = require('electron');

function getAbsoluteUrl(url) {
    if (url.startsWith('http')) {
        return url;
    }
    // Handle protocol-relative URLs
    if (url.startsWith('//')) {
        return location.protocol + url;
    }
    // Handle absolute paths
    if (url.startsWith('/')) {
        return location.origin + url;
    }
    // Handle relative paths
    const a = document.createElement('a');
    a.href = url;
    return a.href;
}


// Intercept clicks on links
document.addEventListener('click', (e) => {
    let target = e.target;
    // Find the nearest anchor tag
    while (target && target.tagName !== 'A') {
        target = target.parentElement;
    }

    if (target && target.href) {
        const url = target.href;
        // Check if it's a standard web link and not a javascript: or other protocol link
        if (url.startsWith('http:') || url.startsWith('https:')) {
            e.preventDefault();
            e.stopImmediatePropagation();
            console.log(`[Preload] Intercepted click on: ${url}`);
            ipcRenderer.send('new-window-request', url);
        }
    }
}, true); // Use capture phase to ensure this runs before the page's own handlers


// Override window.open
const originalWindowOpen = window.open;
window.open = (url, target, features) => {
    console.log(`[Preload] window.open called for: ${url}`);
    
    // Convert relative URL to absolute URL if necessary
    const absoluteUrl = getAbsoluteUrl(url);

    ipcRenderer.send('new-window-request', absoluteUrl);
    
    // Return a mock window object to prevent errors in the calling script
    return {
        closed: false,
        close: function() { this.closed = true; },
        focus: function() {},
        postMessage: function() {}
    };
};

console.log('[Preload] preload-browser.js loaded and link interception is active.'); 