﻿# Header, don't edit
NLF v6
# Language ID
1059
# Font and size - dash (-) means default
-
-
# Codepage - dash (-) means ASCII code page
1251
# RTL - anything else than RTL means LTR
-
# Translation by PrydesparBLR [ <EMAIL> ]
# ^Branding
Nullsoft Install System %s
# ^SetupCaption
Усталяванне $(^Name)
# ^UninstallCaption
Выдаленне $(^Name)
# ^LicenseSubCaption
: Ліцэнзійнае пагадненне
# ^ComponentsSubCaption
: Параметры ўсталявання
# ^DirSubCaption
: Папка ўсталявання
# ^InstallingSubCaption
: Капіяванне файлаў
# ^CompletedSubCaption
: Працэдура завершена
# ^UnComponentsSubCaption
: Параметры выдалення
# ^UnDirSubCaption
: Папка выдалення
# ^ConfirmSubCaption
: Пацвярджэнне
# ^UninstallingSubCaption
: Выдаленне файлаў
# ^UnCompletedSubCaption
: Працэдура завершана
# ^BackBtn
< &Назад
# ^NextBtn
&Далей >
# ^AgreeBtn
&Прыняць
# ^AcceptBtn
Я &прымаю ўмовы Ліцэнзійнага пагаднення
# ^DontAcceptBtn
Я н&е прымаю ўмовы Ліцэнзійнага пагаднення
# ^InstallBtn
&Усталяваць
# ^UninstallBtn
Выд&аліць
# ^CancelBtn
Скасаваць
# ^CloseBtn
За&крыць
# ^BrowseBtn
А&гляд ...
# ^ShowDetailsBtn
Падра&бязнасці...
# ^ClickNext
Націсніце кнопку "Далей", каб працягнуць усталяванне праграмы.
# ^ClickInstall
Націсніце кнопку "Усталяваць", каб пачаць працэс ўсталявання праграмы.
# ^ClickUninstall
Націсніце кнопку "Выдаліць", каб пачаць працэс выдалення праграмы.
# ^Name
Імя
# ^Completed
Завершана
# ^LicenseText
Калі ласка, прачытайце ўмовы Ліцэнзійнага пагаднення перад пачаткам усталявання $(^NameDA). Калі Вы прымаеце ўмовы Ліцэнзійнага пагаднення, націсніце кнопку "Прыняць".
# ^LicenseTextCB
Калі ласка, прачытайце ўмовы Ліцэнзійнага пагаднення перад пачаткам усталявання $(^NameDA). Калі Вы прымаеце ўмовы Ліцэнзійнага пагаднення, націсніце на сцяжок ніжэй. $_CLICK
# ^LicenseTextRB
Калі ласка, прачытайце ўмовы Ліцэнзійнага пагаднення перад пачаткам усталявання $(^NameDA). Калі Вы прымаеце ўмовы Ліцэнзійнага пагаднення, выберыце першы варыянт з прапанаваных нiжэй. $_CLICK
# ^UnLicenseText
Калі ласка, прачытайце ўмовы Ліцэнзійнага пагаднення перад пачаткам выдалення $(^NameDA). Калі Вы прымаеце ўмовы Ліцэнзійнага пагаднення, нацiснiце кнопку "Прыняць".
# ^UnLicenseTextCB
Калі ласка, прачытайце ўмовы Ліцэнзійнага пагаднення перад пачаткам выдалення $(^NameDA). Калі Вы прымаеце ўмовы Ліцэнзійнага пагаднення, націсніце на сцяжок ніжэй. $_CLICK
# ^UnLicenseTextRB
Калі ласка, прачытайце ўмовы Ліцэнзійнага пагаднення перад пачаткам выдалення $(^NameDA). Калі Вы прымаеце ўмовы Ліцэнзійнага пагаднення, выберыце першы варыянт з прапанаваных нiжэй. $_CLICK
# ^Custom
Выбарачна
# ^ComponentsText
Выберыце кампаненты праграмы, якiя Вы жадаеце ўсталяваць. $_CLICK
# ^ComponentsSubText1
Выберыце тып усталявання:
# ^ComponentsSubText2_NoInstTypes
Выберыце кампаненты праграмы, каб усталяваць iх:
# ^ComponentsSubText2
або выберыце кампаненты праграмы, каб усталяваць iх па свайму жаданню:
# ^UnComponentsText
Выберыце кампаненты, якiя Вы жадаеце выдалiць, i знiмiце сцяжкі, выбраныя для тых кампанентаў, якiя не трэба выдаляць. $_CLICK
# ^UnComponentsSubText1
Выберыце тып выдалення:
# ^UnComponentsSubText2_NoInstTypes
Выберыце кампаненты для выдалення:
# ^UnComponentsSubText2
або выберыце кампаненты праграмы для выдалення:
# ^DirText
Праграма ўсталюе $(^NameDA) у выбраную папку. Каб усталяваць праграму ў iншай папкі, нацiснiце кнопку "Агляд" i выберыце патрэбную папку. $_CLICK
# ^DirSubText
Папка ўсталявання
# ^DirBrowseText
Выберыце папку для ўсталявання $(^NameDA):
# ^UnDirText
Праграма выдалiць $(^NameDA) з выбранай папкі. Каб выдаліць праграму з iншай папкі, нацiснiце кнопку "Агляд" i выберыце патрэбную папку. $_CLICK
# ^UnDirSubText
""
# ^UnDirBrowseText
Выберыце папку, з якой Вы жадаеце выдалiць $(^NameDA):
# ^SpaceAvailable
"Даступна на дыску: "
# ^SpaceRequired
"Патрэбна месца на дыску: "
# ^UninstallingText
Праграма выдалiць $(^NameDA) з Вашага камп'ютара. $_CLICK
# ^UninstallingSubText
Выдаленне з:
# ^FileError
Немагчыма адкрыць файл для запiсу: \r\n\r\n$0\r\n\r\nНацiснiце кнопку "Перапынiць", каб перапынiць усталяванне;\r\n"Паўтарыць", каб паўтарыць спробу запiсу ў файл;\r\n"Ігнараваць", каб прапусцiць гэта дзеянне.
# ^FileError_NoIgnore
Немагчыма адкрыць файл для запiсу: \r\n\r\n$0\r\n\r\nНацiснiце кнопку "Паўтарыць", каб паўтарыць спробу запiсу ў файл;\r\n"Скасаваць", каб перапынiць усталяванне.
# ^CantWrite
"Немагчыма запiсаць: "
# ^CopyFailed
Памылка пры капіяванні
# ^CopyTo
"Капіяванне ў "
# ^Registering
"Рэгiстрацыя: "
# ^Unregistering
"Выдаленне рэгiстрацыi: "
# ^SymbolNotFound
"Немагчыма знайсці сiмвал: "
# ^CouldNotLoad
"Немагчыма загрузiць: "
# ^CreateFolder
"Стварэнне папкі: "
# ^CreateShortcut
"Стварэнне ярлыка: "
# ^CreatedUninstaller
"Стварэнне праграмы выдалення: "
# ^Delete
"Выдаленне файла: "
# ^DeleteOnReboot
"Выдаленне пасля перазапуску камп'ютара: "
# ^ErrorCreatingShortcut
"Памылка стварэння ярлыка: " 
# ^ErrorCreating
"Памылка стварэння: "
# ^ErrorDecompressing
Немагчыма выцягнуць дадзеныя. Магчыма пашкоджаны дыстрыбутыў.
# ^ErrorRegistering
Немагчыма зарэгістраваць бібліятэку (DLL)
# ^ExecShell
"Выкананне каманды абалонкі: " 
# ^Exec
"Выкананне: "
# ^Extract
"Выманне: "
# ^ErrorWriting
"Выманне: памылка запiсу файла"
# ^InvalidOpcode
дыстрыбутыў пашкоджаны: код памылкi
# ^NoOLE
"Няма OLE для: " 
# ^OutputFolder
"Папка усталявання: "
# ^RemoveFolder
"Выдаленне папкі: "
# ^RenameOnReboot
"Перайменаванне пасля перазапуску камп'ютара: "
# ^Rename
"Перайменаванне: "
# ^Skipped
"Прапушчана: "
# ^CopyDetails
Капіяваць звесткi ў буфер абмена 
# ^LogInstall
Запiсваць у лог працэс усталявання
# byte
Б
# kilo
 К
# mega
 М
# giga
 Г
