﻿# Header, don't edit ;Español - España (Alfabetización Tradicional)
NLF v6
# Language ID
1034
# Font and size - dash (-) means default
-
-
# Codepage - dash (-) means ASCII code page
1252
# RTL - anything else than RTL means LTR
-
# Translation by <PERSON><PERSON><PERSON><PERSON> & Joel
# Review and minor corrections <PERSON> (<EMAIL>) www.winamp-es.com
# ^Branding
Nullsoft Install System %s
# ^SetupCaption
Instalación de $(^Name)
# ^UninstallCaption
Desinstalación de $(^Name)
# ^LicenseSubCaption
: Acuerdo de Licencia
# ^ComponentsSubCaption
: Opciones de Instalación
# ^DirSubCaption
: Directorio de Instalación
# ^InstallingSubCaption
: Instalando
# ^CompletedSubCaption
: Completado
# ^UnComponentsSubCaption
: Opciones de Desinstalación
# ^UnDirSubCaption
: Directorio de Desinstalación
# ^ConfirmSubCaption
: Confirmación
# ^UninstallingSubCaption
: Desinstalando
# ^UnCompletedSubCaption
: Completado
# ^BackBtn
< &Atrás
# ^NextBtn
&Siguiente >
# ^AgreeBtn
A&cepto
# ^AcceptBtn
A&cepto los términos de la licencia
# ^DontAcceptBtn
&No acepto los términos de la licencia
# ^InstallBtn
&Instalar
# ^UninstallBtn
&Desinstalar
# ^CancelBtn
Cancelar
# ^CloseBtn
&Cerrar
# ^BrowseBtn
&Examinar...
# ^ShowDetailsBtn
Ver &detalles
# ^ClickNext
Presione Siguiente para continuar.
# ^ClickInstall
Presione Instalar para comenzar la instalación.
# ^ClickUninstall
Presione Desinstalar para comenzar la desinstalación.
# ^Name
Nombre
# ^Completed
Completado
# ^LicenseText
Por favor, revise el acuerdo de licencia antes de instalar $(^NameDA). Si acepta todos los términos del acuerdo, presione Acepto.
# ^LicenseTextCB
Por favor, revise el acuerdo de licencia antes de instalar $(^NameDA). Si acepta todos los términos del acuerdo, marque abajo la casilla. $_CLICK
# ^LicenseTextRB
Por favor, revise el acuerdo de licencia antes de instalar $(^NameDA). Si acepta todos los términos del acuerdo, seleccione abajo la primera opción. $_CLICK
# ^UnLicenseText
Por favor, revise el acuerdo de licencia antes de desinstalar $(^NameDA). Si acepta todos los términos del acuerdo, presione Acepto.
# ^UnLicenseTextCB
Por favor, revise el acuerdo de licencia antes de desinstalar $(^NameDA). Si acepta todos los términos del acuerdo, marque abajo la casilla. $_CLICK.
# ^UnLicenseTextRB
Por favor, revise el acuerdo de licencia antes de desinstalar $(^NameDA). Si acepta todos los términos del acuerdo, seleccione abajo la primera opción. $_CLICK
# ^Custom
Personalizada
# ^ComponentsText
Marque los componentes que desee instalar y desmarque los componentes que no desee instalar. $_CLICK
# ^ComponentsSubText1
Tipos de instalación:
# ^ComponentsSubText2_NoInstTypes
Seleccione los componentes a instalar:
# ^ComponentsSubText2
O seleccione los componentes opcionales que desee instalar:
# ^UnComponentsText
Marque los componentes que desee desinstalar y desmarque los componentes que no desee desinstalar. $_CLICK
# ^UnComponentsSubText1
Tipos de desinstalación:
# ^UnComponentsSubText2_NoInstTypes
Seleccione los componentes a desinstalar:
# ^UnComponentsSubText2
O seleccione los componentes opcionales que desee desinstalar:
# ^DirText
El programa de instalación instalará $(^NameDA) en el siguiente directorio. Para instalar en un directorio diferente, presione Examinar y seleccione otro directorio. $_CLICK
# ^DirSubText
Directorio de Destino
# ^DirBrowseText
Seleccione el directorio en el que instalará $(^NameDA):
# ^UnDirText
El programa de instalación desinstalará $(^NameDA) del siguiente directorio. Para desinstalar de un directorio diferente, presione Examinar y seleccione otro directorio. $_CLICK
# ^UnDirSubText
""
# ^UnDirBrowseText
Seleccione el directorio desde el cual desinstalará $(^NameDA):
# ^SpaceAvailable
Espacio disponible: 
# ^SpaceRequired
Espacio requerido: 
# ^UninstallingText
$(^NameDA) será desinstalado del siguiente directorio. $_CLICK
# ^UninstallingSubText
Desinstalando desde:
# ^FileError
Error abriendo archivo para escritura: \r\n\t"$0"\r\nPresione abortar para anular la instalación,\r\nreintentar para volver a intentar escribir el archivo, u\r\nomitir para ignorar este archivo
# ^FileError_NoIgnore
Error abriendo archivo para escritura: \r\n\t"$0"\r\nPresione reintentar para volver a intentar escribir el archivo, o\r\ncancelar para anular la instalación
# ^CantWrite
"No pudo escribirse: "
# ^CopyFailed
Falló la copia
# ^CopyTo
"Copiar a "
# ^Registering
"Registrando: "
# ^Unregistering
"Eliminando registro: "
# ^SymbolNotFound
"No pudo encontrarse símbolo: "
# ^CouldNotLoad
"No pudo cargarse: "
# ^CreateFolder
"Creando directorio: "
# ^CreateShortcut
"Creando acceso directo: "
# ^CreatedUninstaller
"Creando desinstalador: "
# ^Delete
"Borrar archivo: "
# ^DeleteOnReboot
"Borrar al reinicio: "
# ^ErrorCreatingShortcut
"Error creando acceso directo: "
# ^ErrorCreating
"Error creando: "
# ^ErrorDecompressing
¡Error descomprimiendo datos! ¿Instalador corrupto?
# ^ErrorRegistering
Error registrando DLL
# ^ExecShell
"Extrayendo  comando: "
# ^Exec
"Extrayendo : "
# ^Extract
"Extraer: "
# ^ErrorWriting
"Extraer: error escribiendo al archivo "
# ^InvalidOpcode
Instalador corrupto: código de operación no válido
# ^NoOLE
"Sin OLE para: "
# ^OutputFolder
"Directorio de salida: "
# ^RemoveFolder
"Eliminar directorio: "
# ^RenameOnReboot
"Renombrar al reinicio: "
# ^Rename
"Renombrar: "
# ^Skipped
"Omitido: "
# ^CopyDetails
Copiar Detalles al Portapapeles
# ^LogInstall
Registrar proceso de instalación 
# ^Byte
B
# ^Kilo
 K
# ^Mega
 M
# ^Giga
 G
