﻿# Header, don't edit
NLF v6
# Language ID
1086
# Font and size - dash (-) means default
-
-
# Codepage - dash (-) means ASCII code page
1252
# RTL - anything else than RTL means LTR
-
# Translation <EMAIL>
# ^Branding
Nullsoft Install System %s
# ^SetupCaption
Setup $(^Name)
# ^UninstallCaption
Uninstall $(^Name)
# ^LicenseSubCaption
: <PERSON>lesenan
# ^ComponentsSubCaption
: Pilihan kemasukan
# ^DirSubCaption
: Folder kemasukan
# ^InstallingSubCaption
: Memasang
# ^CompletedSubCaption
: Selesai
# ^UnComponentsSubCaption
: <PERSON><PERSON><PERSON> membuang
# ^UnDirSubCaption
: Folder Uninstal
# ^ConfirmSubCaption
: Kepastian
# ^UninstallingSubCaption
: Membuang
# ^UnCompletedSubCaption
: Tidak Selesai
# ^BackBtn
< &Ke Belakang
# ^NextBtn
&Ke Depan >
# ^AgreeBtn
Saya &setuju
# ^AcceptBtn
Saya s&etuju dengan Perlesenan
# ^DontAcceptBtn
Saya &tidak setuju dengan Perlesenan
# ^InstallBtn
&Masukkan
# ^UninstallBtn
&Buang
# ^CancelBtn
Batal
# ^CloseBtn
&Tutup
# ^BrowseBtn
S&elusur...
# ^ShowDetailsBtn
Buka &lagi
# ^ClickNext
Klik Ke Depan untuk teruskan.
# ^ClickInstall
Klik Masukkan untuk kemasukkan.
# ^ClickUninstall
Klik Uninstall untuk membuang.
# ^Name
Nama
# ^Completed
Selesai
# ^LicenseText
Sila baca lesen sebelum memasukkan $(^NameDA). Jika anda terima perlesenan, klik Saya setuju.
# ^LicenseTextCB
Sila baca lesen sebelum memasukkan $(^NameDA). Jika terima, beri tanda dicheckbox. $_CLICK
# ^LicenseTextRB
Sila baca lesen sebelum sebelum membuang $(^NameDA). Jika anda terima perlesenan, pilihlah salah satu item dibawah ini. $_CLICK
# ^UnLicenseText
Sila baca lesen sebelum sebelum membuang $(^NameDA). Jika anda terima perlesenan, klik Saya setuju.
# ^UnLicenseTextCB
Sila baca lesen sebelum memasukkan $(^NameDA). Jika terima, beri tanda dicheckbox. $_CLICK
# ^UnLicenseTextRB
Sila baca lesen sebelum sebelum membuang $(^NameDA).Jika anda terima perlesenan, pilihlah salah satu item dibawah ini. $_CLICK
# ^Custom
Custom
# ^ComponentsText
Beri tanda dicheckbox pada komponen yang ingin dimasukkan and hilangkan tanda pada komponen yang tidak perlu dimasukkan. $_CLICK
# ^ComponentsSubText1
Pilih kemasukan:
# ^ComponentsSubText2_NoInstTypes
Pilih komponen-komponen untuk dimasukkan:
# ^ComponentsSubText2
Atau, pilih komponen berikut untuk dimasukkan:
# ^UnComponentsText
Beri tanda dicheckbox pada komponen yang ingin dimasukkan and hilangkan tanda pada komponen yang tidak perlu dimasukkan. $_CLICK
# ^UnComponentsSubText1
Pilih tipe un-kemasukan:
# ^UnComponentsSubText2_NoInstTypes
Pilih komponen-komponen untuk di buang:
# ^UnComponentsSubText2
Atau, pilih komponen berikut untuk di buang:
# ^DirText
Setup akan memasukkan $(^NameDA) pada folder berikut. Untuk memilih folder lainnya, klik Selusur dan pilih folder pilihan anda. $_CLICK
# ^DirSubText
Folder tujuan
# ^DirBrowseText
Pilih folder untuk memasukkan $(^NameDA):
# ^UnDirText
Setup akan membuang $(^NameDA) dari folder berikut. Untuk memilih folder lainnya, klik Selusur dan pilih folder pilihan anda. $_CLICK
# ^UnDirSubText
""
# ^UnDirBrowseText
Pilih folder untuk dibuang $(^NameDA):
# ^SpaceAvailable
"Ruang cakera keras yang ada: "
# ^SpaceRequired
"Ruang cakera keras yang diperlukan: "
# ^UninstallingText
$(^NameDA) akan buang dari folder berikut. $_CLICK
# ^UninstallingSubText
Membuang:
# ^FileError
Tidak dapat menulis pada fail: \r\n\t"$0"\r\nKlik abort untuk membatalkan kemasukan,\r\nretry untuk cuba lagi, atau\r\nignore untuk abaikan fail ini.
# ^FileError_NoIgnore
Tidak dapat menulis pada fail: \r\n\t"$0"\r\nKlik retry untuk cuba lagi, atau\r\ncancel untuk batalkan kemasukan
# ^CantWrite
"Gagal menulis pada: "
# ^CopyFailed
Gagal menyalin
# ^CopyTo
"Menyalin ke "
# ^Registering
"Mendaftarkan modul: "
# ^Unregistering
"Melepaskan modul: "
# ^SymbolNotFound
"Symbol tidak jumpa : "
# ^CouldNotLoad
"Tidak dapat membuka: "
# ^CreateFolder
"Membuat folder: "
# ^CreateShortcut
"Membuat pintasan: "
# ^CreatedUninstaller
"Membuat program unistall: "
# ^Delete
"Memadam fail: "
# ^DeleteOnReboot
"Akan dipadam ketika reboot: "
# ^ErrorCreatingShortcut
"Tidak dapat membuat pintasan: "
# ^ErrorCreating
"Ralat penciptaan: "
# ^ErrorDecompressing
Ralat ketika membuka data! Program Installer rosak
# ^ErrorRegistering
Ralat mendaftarkan modul DLL
# ^ExecShell
"ExecShell: "
# ^Exec
"Menjalankan: "
# ^Extract
"Mengekstrak: "
# ^ErrorWriting
"Ekstrak: ralat ketika menulis ke fail "
# ^InvalidOpcode
Installer rosak: opcode tidak lengkap
# ^NoOLE
"OLE tidak ditemukan: "
# ^OutputFolder
"Folder tujuan: "
# ^RemoveFolder
"Menghapuskan folder: "
# ^RenameOnReboot
"Menamakan semula pada reboot: "
# ^Rename
"Menamakan semula: "
# ^Skipped
"Diabaikan: "
# ^CopyDetails
Salin terperinci ke clipboard
# ^LogInstall
Catat proses kemasukan
# ^Byte
Bait
# ^Kilo
 Kilo
# ^Mega
 Mega
# ^Giga
 Giga
