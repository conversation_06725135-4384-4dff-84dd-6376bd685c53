﻿# Header, don't edit
NLF v6
# Language ID
1031
# Font and size - dash (-) means default
-
-
# Codepage - dash (-) means ASCII code page
1252
# RTL - anything else than RTL means LTR
-
# Translation by <PERSON><PERSON>, changes by <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>
# ^Branding
Nullsoft Install System %s
# ^SetupCaption
Installation von $(^Name)
# ^UninstallCaption
Deinstallation von $(^Name)
# ^LicenseSubCaption
: Lizenzabkommen
# ^ComponentsSubCaption
: Installations-Optionen
# ^DirSubCaption
: Zielverzeichnis
# ^InstallingSubCaption
: Wird installiert
# ^CompletedSubCaption
: Fertig
# ^UnComponentsSubCaption
: Deinstallations-Optionen
# ^UnDirSubCaption
: Quellverzeichnis
# ^ConfirmSubCaption
: Bestätigung
# ^UninstallingSubCaption
: Wird entfernt
# ^UnCompletedSubCaption
: Fertig
# ^BackBtn
< &Zurück
# ^NextBtn
&Weiter >
# ^AgreeBtn
&Annehmen
# ^AcceptBtn
Lizenzabkommen &akzeptieren
# ^DontAcceptBtn
Lizenzabkommen ab&lehnen
# ^InstallBtn
&Installieren
# ^UninstallBtn
&Deinstallieren
# ^CancelBtn
Abbrechen
# ^CloseBtn
&Beenden
# ^BrowseBtn
&Durchsuchen ...
# ^ShowDetailsBtn
&Details anzeigen
# ^ClickNext
Klicken Sie auf Weiter, um fortzufahren.
# ^ClickInstall
Klicken Sie auf Installieren, um die Installation zu starten.
# ^ClickUninstall
Klicken Sie auf Deinstallieren, um die Deinstallation zu starten.
# ^Name
Name
# ^Completed
Fertig
# ^LicenseText
Bitte lesen Sie das Lizenzabkommen, bevor Sie $(^NameDA) installieren. Wenn Sie alle Bedingungen des Abkommens akzeptieren, klicken Sie auf Annehmen.
# ^LicenseTextCB
Bitte lesen Sie das Lizenzabkommen, bevor Sie $(^NameDA) installieren. Wenn Sie alle Bedingungen des Abkommens akzeptieren, aktivieren Sie das Kontrollkästchen. $_CLICK
# ^LicenseTextRB
Bitte lesen Sie das Lizenzabkommen, bevor Sie $(^NameDA) installieren. Wenn Sie alle Bedingungen des Abkommens akzeptieren, wählen Sie die entsprechende Option. $_CLICK
# ^UnLicenseText
Bitte lesen Sie das Lizenzabkommen, bevor Sie $(^NameDA) entfernen. Wenn Sie alle Bedingungen des Abkommens akzeptieren, klicken Sie auf Annehmen.
# ^UnLicenseTextCB
Bitte lesen Sie das Lizenzabkommen, bevor Sie $(^NameDA) entfernen. Wenn Sie alle Bedingungen des Abkommens akzeptieren, aktivieren Sie das Kontrollkästchen. $_CLICK
# ^UnLicenseTextRB
Bitte lesen Sie das Lizenzabkommen, bevor Sie $(^NameDA) entfernen. Wenn Sie alle Bedingungen des Abkommens akzeptieren, wählen Sie die entsprechende Option. $_CLICK
# ^Custom
Benutzerdefiniert
# ^ComponentsText
Wählen Sie die Komponenten aus, die Sie installieren möchten, und wählen Sie diejenigen ab, die Sie nicht installiert werden sollen. $_CLICK
# ^ComponentsSubText1
Installations-Typ bestimmen:
# ^ComponentsSubText2_NoInstTypes
Wählen Sie die Komponenten aus, die Sie installieren möchten:
# ^ComponentsSubText2
oder wählen Sie zusätzliche Komponenten aus, die Sie installieren möchten:
# ^UnComponentsText
Wählen Sie die Komponenten aus, die Sie entfernen möchten, und wählen Sie diejenigen ab, die Sie nicht entfernt werden sollen. $_CLICK
# ^UnComponentsSubText1
Deinstallations-Typ bestimmen:
# ^UnComponentsSubText2_NoInstTypes
Wählen Sie die Komponenten aus, die Sie entfernen möchten:
# ^UnComponentsSubText2
oder wählen Sie zusätzliche Komponenten aus, die Sie entfernen möchten:
# ^DirText
$(^NameDA) wird in das unten angegebene Verzeichnis installiert. Falls Sie in ein anderes Verzeichnis installieren möchten, klicken Sie auf Durchsuchen und wählen Sie ein anderes Verzeichnis aus. $_CLICK
# ^DirSubText
Zielverzeichnis
# ^DirBrowseText
Wählen Sie das Verzeichnis aus, in das Sie $(^NameDA) installieren möchten:
# ^UnDirText
$(^NameDA) wird aus dem unten angegebenen Verzeichnis entfernt. Falls sich $(^NameDA) in einem anderen Verzeichnis befindet, klicken Sie auf Durchsuchen und wählen Sie das richtige Verzeichnis aus. $_CLICK
# ^UnDirSubText
""
# ^UnDirBrowseText
Wählen Sie das Verzeichnis aus, in dem sich $(^NameDA) befindet:
# ^SpaceAvailable
"Verfügbarer Speicher: "
# ^SpaceRequired
"Benötigter Speicher: "
# ^UninstallingText
$(^NameDA) wird aus dem unten angegebenen Verzeichnis entfernt. $_CLICK
# ^UninstallingSubText
Wird entfernt aus:
# ^FileError
Fehler beim Überschreiben der Datei: \r\n\t"$0"\r\nKlicken Sie auf Abbrechen, um abzubrechen,\r\nauf Wiederholen, um den Schreibvorgang erneut zu versuchen,\r\noder auf Ignorieren, um diese Datei zu überspringen.
# ^FileError_NoIgnore
Fehler beim Überschreiben der Datei: \r\n\t"$0"\r\nKlicken Sie auf Wiederholen, um den Schreibvorgang erneut zu versuchen,\r\noder auf Abbrechen, um die Installation zu beenden.
# ^CantWrite
"Fehler beim Schreiben: "
# ^CopyFailed
Kopieren fehlgeschlagen
# ^CopyTo
"Wird kopiert nach "
# ^Registering
"Wird registriert: "
# ^Unregistering
"Wird deregistriert: "
# ^SymbolNotFound
"Symbol ist nicht vorhanden: "
# ^CouldNotLoad
"Fehler beim Laden von "
# ^CreateFolder
"Verzeichnis wird erstellt: "
# ^CreateShortcut
"Verknüpfung wird erstellt: "
# ^CreatedUninstaller
"Deinstallations-Programm wird erstellt: "
# ^Delete
"Datei wird gelöscht: "
# ^DeleteOnReboot
"Datei wird nach Neustart gelöscht: "
# ^ErrorCreatingShortcut
"Fehler beim Erstellen der Verknüpfung: "
# ^ErrorCreating
"Fehler beim Erstellen: "
# ^ErrorDecompressing
Fehler beim Entpacken. Ist das Installations-Programm beschädigt?
# ^ErrorRegistering
Fehler beim Registrieren der DLL
# ^ExecShell
"ExecShell: "
# ^Exec
"Wird gestartet: "
# ^Extract
"Wird entpackt: "
# ^ErrorWriting
"Entpacken: Fehler beim Schreiben der Datei "
# ^InvalidOpcode
Beschädigtes Installations-Programm: ungültiger Befehlscode
# ^NoOLE
"Kein OLE für: "
# ^OutputFolder
"Zielverzeichnis: "
# ^RemoveFolder
"Verzeichnis wird entfernt: "
# ^RenameOnReboot
"Umbenennen nach Neustart: "
# ^Rename
"Umbenennen: "
# ^Skipped
"Übersprungen: "
# ^CopyDetails
Details in die Zwischenablage kopieren
# ^LogInstall
Installationsverlauf protokollieren
# ^Byte
B
# ^Kilo
 K
# ^Mega
 M
# ^Giga
 G
