﻿# Header, don't edit
NLF v6
# Language ID
1041
# Font and size - dash (-) means default
ＭＳ Ｐゴシック
9
# Codepage - dash (-) means ASCII code page
932
# RTL - anything else than RTL means LTR
-
# Translation by <PERSON><PERSON><PERSON><PERSON>, updated by <PERSON><PERSON><PERSON> <ta<PERSON><PERSON><EMAIL>>
# ^Branding
Nullsoft Install System %s
# ^SetupCaption
$(^Name) セットアップ
# ^UninstallCaption
$(^Name) アンインストール
# ^LicenseSubCaption
：ライセンス契約書
# ^ComponentsSubCaption
：インストール オプション
# ^DirSubCaption
：インストール フォルダ
# ^InstallingSubCaption
：インストール
# ^CompletedSubCaption
：完了
# ^UnComponentsSubCaption
: アンインストール オプション
# ^UnDirSubCaption
: アンインストール フォルダ
# ^ComfirmSubCaption
：確認
# ^UninstallingSubCaption
：アンインストール
# ^UnCompletedSubCaption
：完了
# ^BackBtn
< 戻る(&B)
# ^NextBtn
次へ(&N) >
# ^AgreeBtn
同意する(&A)
# ^AcceptBtn
このライセンス契約書に同意します(&A)
# ^DontAcceptBtn
このライセンス契約書には同意できません(&D)
# ^InstallBtn
インストール
# ^UninstallBtn
ｱﾝｲﾝｽﾄｰﾙ(&U)
# ^CancelBtn
キャンセル
# ^CloseBtn
閉じる(&C)
# ^BrowseBtn
参照(&R)...
# ^ShowDetailsBtn
詳細を表示(&D)
# ^ClickNext
続けるには [次へ] をクリックして下さい。
# ^ClickInstall
インストールを始めるには [インストール] をクリックして下さい。
# ^ClickUninstall
アンインストールを始めるには [ｱﾝｲﾝｽﾄｰﾙ] をクリックして下さい。
# ^Name
アプリケーション
# ^Completed
完了
# ^LicenseText
$(^NameDA)をインストールする前に、ライセンス契約書を確認して下さい。契約書の全ての条件に同意するならば、[同意する] ボタンをクリックして下さい。
# ^LicenseTextCB
$(^NameDA)をインストールする前に、ライセンス契約書を確認して下さい。契約書の全ての条件に同意するならば、下のチェックボックスをクリックして下さい。 $_CLICK
# ^LicenseTextRB
$(^NameDA)をインストールする前に、ライセンス契約書を確認して下さい。契約書の全ての条件に同意するならば、下に表示されているオプションのうち、最初のものを選んで下さい。 $_CLICK
# ^UnLicenseText
$(^NameDA)をアンインストールする前に、ライセンス契約書を確認して下さい。契約書の全ての条件に同意するならば、[同意する] ボタンをクリックして下さい。
# ^UnLicenseTextCB
$(^NameDA)をアンインストールする前に、ライセンス契約書を確認して下さい。契約書の全ての条件に同意するならば、下のチェックボックスをクリックして下さい。 $_CLICK
# ^UnLicenseTextRB
$(^NameDA)をアンインストールする前に、ライセンス契約書を確認して下さい。契約書の全ての条件に同意するならば、下に表示されているオプションのうち、最初のものを選んで下さい。 $_CLICK
# ^Custom
カスタム
# ^ComponentsText
インストールしたいコンポーネントにチェックを付けて下さい。不要なものについては、チェックを外して下さい。 $_CLICK
# ^ComponentsSubText1
インストール タイプを選択：
# ^ComponentsSubText2_NoInstTypes
インストール コンポーネントを選択：
# ^ComponentsSubText2
または、インストール オプション コンポーネントを選択：
# ^UnComponentsText
アンインストールしたいコンポーネントにチェックを付けて下さい。そうでないものについては、チェックを外して下さい。 $_CLICK
# ^UnComponentsSubText1
アンインストール タイプを選択：
# ^UnComponentsSubText2_NoInstTypes
アンインストール コンポーネントを選択：
# ^UnComponentsSubText2
または、アンインストール オプション コンポーネントを選択：
# ^DirText
$(^NameDA)を以下のフォルダにインストールします。異なったフォルダにインストールするには、[参照] を押して、別のフォルダを選択してください。 $_CLICK
# ^DirSubText
インストール先 フォルダ
# ^DirBrowseText
$(^NameDA)をインストールするフォルダを選択してください：
# ^UnDirText
$(^NameDA)を以下のフォルダからアンインストールします。異なったフォルダからアンインストールするには、[参照] を押して、別のフォルダを選択してください。 $_CLICK
# ^UnDirSubText
""
# ^UnDirBrowseText
$(^NameDA)をアンインストールするフォルダを選択してください：
# ^SpaceAvailable
利用可能なディスクスペース： 
# ^SpaceRequired
必要なディスクスペース： 
# ^UninstallingText
$(^NameDA)は、以下のフォルダからアンインストールされます。 $_CLICK
# ^UninstallingSubText
アンインストール元：
# ^FileError
初期ファイルの作成エラー：\r\n\t"$0"\r\nインストールを中止するには中止を,\r\n再びこのファイルの作成を試みるには再試行を, また\r\nこのファイルをスキップして続けるには無視を押してください
# ^FileError_NoIgnore
初期ファイルの作成エラー: \r\n\t"$0"\r\n再びこのファイルの作成を試みるには再試行を, また\r\nインストールを中止するにはキャンセルを押して下さい
# ^CantWrite
作成できません：
# ^CopyFailed
コピーは失敗しました
# ^CopyTo
コピーします
# ^Registering
登録中:
# ^Unregistering
登録解除中:
# ^SymbolNotFound
シンボルを見つけることができません：
# ^CouldNotLoad
ロードすることができません：
# ^CreateFolder
フォルダの作成：
# ^CreateShortcut
ショートカットの作成：
# ^CreatedUninstaller
アンインストーラの作成：
# ^Delete
ファイルの削除：
# ^DeleteOnReboot
リブート時に削除：
# ^ErrorCreatingShortcut
ショートカットの作成エラー：
# ^ErrorCreating
作成エラー：
# ^ErrorDecompressing
データの抽出エラー\r\n\r\nインストーラが破損しています。
# ^ErrorRegistering
DLLの登録エラー
# ^ExecShell
拡張子の関連付け実行: 
# ^Execute
実行：
# ^Extract
抽出：
# ^ErrorWriting
抽出：ファイル作成エラー
# ^InvalidOpcode
インストールの不正：無効なopcode
# ^NoOLE
OLEがありません：
# ^OutputFolder
出力先フォルダ：
# ^RemoveFolder
フォルダの削除：
# ^RenameOnReboot
リブート時に名前の変更：
# ^Rename
名前の変更：
# ^Skipped
スキップ：
# ^CopyDetails
クリップボードへ詳細をコピー
# ^LogInstall
インストールプロセスをログヘ記録
# ^Byte
B
# ^Kilo
 K
# ^Mega
 M
# ^Giga
 G
