# 🔧 脚本注入配置管理指南

## 📋 概述

本指南说明如何管理小梅花AI智能客服app中的脚本注入配置，包括添加新页面支持、修改现有配置等。

## 🎯 为什么需要URL白名单？

### ✅ 保留白名单的优势：
1. **安全性**：防止脚本在不需要的页面执行
2. **性能**：减少不必要的脚本注入和执行
3. **稳定性**：避免干扰其他网站的正常功能
4. **可控性**：精确控制脚本执行范围
5. **调试友好**：便于排查问题和维护

### ❌ 删除白名单的风险：
1. **安全风险**：所有页面都可能执行脚本
2. **性能影响**：无用的脚本执行消耗资源
3. **兼容性问题**：可能与其他网站功能冲突
4. **难以维护**：问题排查困难

## 📁 配置文件位置

### 主要配置位置：
- **主配置**：`src/renderer/main.html` (第4855-4869行)
- **配置文件**：`src/config/script-injection-config.js` (可选)

### 当前白名单内容：

#### URL白名单：
```javascript
const scriptInjectionWhitelist = [
  'store.weixin.qq.com',      // 微信小店
  'shop.weixin.qq.com',       // 微信商店
  'weixin.qq.com/shop',       // 微信商店
  'filehelper.weixin.qq.com', // AI智能上架
  'channels.weixin.qq.com',   // 视频号助手
  'ai-knowledge.html',        // AI知识库
  'xiaomeihuakefu.cn'         // 小梅花官网页面
];
```

#### 标题白名单：
```javascript
const titleInjectionWhitelist = [
  'AI智能客服', '店铺客服',
  'AI智能上架', '上架产品', 
  '视频号助手',
  'AI知识库',
  '小梅花'
];
```

## 🔄 如何添加新页面支持

### 方法1：直接修改主配置文件

1. **打开文件**：`src/renderer/main.html`

2. **找到URL白名单**（约第4860行）：
```javascript
const scriptInjectionWhitelist = [
  // 现有配置...
  'your-new-domain.com',  // 添加新域名
];
```

3. **找到标题白名单**（约第6665行）：
```javascript
const titleInjectionWhitelist = [
  // 现有配置...
  '新页面标题关键词',  // 添加新标题
];
```

### 方法2：使用配置文件（推荐）

1. **修改配置文件**：`src/config/script-injection-config.js`
2. **在相应数组中添加新条目**
3. **重新构建app**

## 📝 添加示例

### 示例1：添加新的电商平台支持

```javascript
// 在URL白名单中添加
'taobao.com',           // 淘宝
'tmall.com',            // 天猫
'jd.com',               // 京东

// 在标题白名单中添加
'淘宝店铺', '天猫店铺', '京东店铺'
```

### 示例2：添加本地开发环境

```javascript
// 在URL白名单中添加
'localhost:3000',       // 本地开发服务器
'127.0.0.1:8080',      // 本地测试环境
'dev.example.com',      // 开发环境域名
```

## ⚠️ 安全注意事项

### 添加新域名时请注意：

1. **最小权限原则**：只添加确实需要的域名
2. **精确匹配**：使用具体的域名，避免过于宽泛的规则
3. **定期审查**：定期检查和清理不再需要的条目
4. **测试验证**：添加后进行充分测试

### 不推荐的做法：

```javascript
// ❌ 过于宽泛，不安全
'',                     // 匹配所有URL
'.com',                 // 匹配所有.com域名
'http',                 // 匹配所有http页面

// ✅ 推荐的精确匹配
'specific-domain.com',  // 具体域名
'app.example.com',      // 具体子域名
'example.com/specific-path'  // 具体路径
```

## 🧪 测试流程

添加新配置后的测试步骤：

1. **重新构建app**：`npm run build:all`
2. **启动app**：`npm start`
3. **访问目标页面**：确认脚本正常注入
4. **检查控制台**：查看注入日志
5. **功能测试**：验证脚本功能正常工作
6. **兼容性测试**：确保不影响其他页面

## 🔍 调试技巧

### 查看注入日志：

在浏览器控制台中查找以下日志：
```
[小梅花] 检测到目标页面: [URL]，立即注入连接器...
[小梅花] 检测到标题更新为目标页面: [标题]，注入连接器...
```

### 常见问题排查：

1. **脚本未注入**：检查URL是否在白名单中
2. **脚本注入但不工作**：检查脚本中的@match规则
3. **页面功能异常**：检查是否有脚本冲突

## 📊 性能优化建议

1. **合理控制白名单大小**：避免添加过多不必要的域名
2. **使用精确匹配**：减少字符串匹配的计算开销
3. **定期清理**：移除不再使用的配置项
4. **监控性能**：关注脚本注入对页面加载速度的影响

## 🔄 版本管理

当修改脚本注入配置时：

1. **记录变更**：在git提交中详细说明修改原因
2. **版本标记**：考虑更新app版本号
3. **文档更新**：同步更新相关文档
4. **团队通知**：通知团队成员配置变更

---

## 📞 技术支持

如果在配置过程中遇到问题，请：

1. 检查控制台错误日志
2. 验证配置语法正确性
3. 确认目标页面URL格式
4. 测试在不同环境下的表现

**建议**：保留当前的URL白名单机制，它提供了良好的安全性和可控性。通过集中管理配置，可以在保持安全的同时提高维护效率。
