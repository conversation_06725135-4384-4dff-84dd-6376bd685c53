#!/usr/bin/env node

/**
 * 快速构建脚本 - 修复AI知识库访问问题后重新打包
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 开始快速构建小梅花AI智能客服...');
console.log('📝 本次构建修复了AI知识库访问问题');

// 检查必要文件
const requiredFiles = [
    'src/main.js',
    'package.json',
    'build/icon.icns'
];

console.log('🔍 检查必要文件...');
for (const file of requiredFiles) {
    if (!fs.existsSync(file)) {
        console.error(`❌ 缺少必要文件: ${file}`);
        process.exit(1);
    }
}
console.log('✅ 所有必要文件存在');

// 清理旧的构建文件
console.log('🧹 清理旧的构建文件...');
try {
    if (fs.existsSync('dist')) {
        execSync('rm -rf dist', { stdio: 'inherit' });
    }
    console.log('✅ 清理完成');
} catch (error) {
    console.log('⚠️  清理时出现警告，继续构建...');
}

// 安装依赖（如果需要）
console.log('📦 检查依赖...');
if (!fs.existsSync('node_modules')) {
    console.log('📦 安装依赖...');
    execSync('npm install', { stdio: 'inherit' });
} else {
    console.log('✅ 依赖已存在');
}

// 验证配置
console.log('🔧 验证配置...');
try {
    if (fs.existsSync('scripts/validate-config.js')) {
        execSync('node scripts/validate-config.js', { stdio: 'inherit' });
        console.log('✅ 配置验证通过');
    }
} catch (error) {
    console.log('⚠️  配置验证警告，继续构建...');
}

// 构建应用
console.log('🔨 开始构建应用...');
console.log('📱 构建目标: macOS (arm64 + x64)');

try {
    // 根据系统架构选择构建目标
    const arch = process.arch;
    console.log(`🖥️  检测到系统架构: ${arch}`);
    
    let buildCommand;
    if (arch === 'arm64') {
        console.log('🍎 构建 Apple Silicon 版本...');
        buildCommand = 'npm run build:arm64';
    } else {
        console.log('💻 构建 Intel 版本...');
        buildCommand = 'npm run build:x64';
    }
    
    execSync(buildCommand, { stdio: 'inherit' });
    console.log('✅ 应用构建完成');
    
} catch (error) {
    console.error('❌ 构建失败:', error.message);
    console.log('🔄 尝试使用通用构建命令...');
    
    try {
        execSync('npx electron-builder --mac', { stdio: 'inherit' });
        console.log('✅ 通用构建完成');
    } catch (fallbackError) {
        console.error('❌ 通用构建也失败:', fallbackError.message);
        process.exit(1);
    }
}

// 检查构建结果
console.log('🔍 检查构建结果...');
const distDir = 'dist';
if (fs.existsSync(distDir)) {
    const files = fs.readdirSync(distDir);
    const dmgFiles = files.filter(f => f.endsWith('.dmg'));
    
    if (dmgFiles.length > 0) {
        console.log('🎉 构建成功！');
        console.log('📦 生成的DMG文件:');
        dmgFiles.forEach(file => {
            const filePath = path.join(distDir, file);
            const stats = fs.statSync(filePath);
            const sizeMB = (stats.size / 1024 / 1024).toFixed(2);
            console.log(`   📁 ${file} (${sizeMB} MB)`);
        });
        
        console.log('\n🔧 本次修复内容:');
        console.log('   ✅ 优化了APP环境检测逻辑');
        console.log('   ✅ 修复了User-Agent设置');
        console.log('   ✅ 放宽了安全检查限制');
        console.log('   ✅ 改进了API访问控制');
        console.log('   ✅ 添加了详细的调试日志');
        
        console.log('\n📋 安装说明:');
        console.log('   1. 打开生成的DMG文件');
        console.log('   2. 将应用拖拽到Applications文件夹');
        console.log('   3. 首次运行时允许来自未知开发者的应用');
        console.log('   4. AI知识库功能现在应该可以正常访问');
        
    } else {
        console.log('⚠️  构建完成但未找到DMG文件');
        console.log('📁 dist目录内容:', files);
    }
} else {
    console.log('❌ 未找到dist目录，构建可能失败');
}

console.log('\n🎯 构建完成！');
