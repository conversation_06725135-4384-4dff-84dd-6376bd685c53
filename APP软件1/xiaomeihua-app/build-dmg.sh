#!/bin/bash

# 小梅花AI智能客服 DMG 一键打包脚本
# 使用方法: ./build-dmg.sh [选项]
# 选项:
#   --clean    清理构建缓存
#   --arm64    仅构建 ARM64 架构
#   --x64      仅构建 x64 架构
#   --help     显示帮助信息

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 显示帮助信息
show_help() {
    echo "小梅花AI智能客服 DMG 打包脚本"
    echo ""
    echo "使用方法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  --clean    清理构建缓存后重新构建"
    echo "  --arm64    仅构建 ARM64 架构 (Apple Silicon)"
    echo "  --x64      仅构建 x64 架构 (Intel)"
    echo "  --help     显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0                # 构建所有架构"
    echo "  $0 --clean        # 清理后构建所有架构"
    echo "  $0 --arm64        # 仅构建 ARM64"
    echo "  $0 --x64 --clean  # 清理后仅构建 x64"
}

# 检查系统要求
check_requirements() {
    print_info "检查系统要求..."
    
    # 检查操作系统
    if [[ "$OSTYPE" != "darwin"* ]]; then
        print_error "此脚本只能在 macOS 上运行"
        exit 1
    fi
    
    # 检查 Node.js
    if ! command -v node &> /dev/null; then
        print_error "未找到 Node.js，请先安装 Node.js"
        exit 1
    fi
    
    # 检查 npm
    if ! command -v npm &> /dev/null; then
        print_error "未找到 npm，请先安装 npm"
        exit 1
    fi
    
    # 检查 hdiutil
    if ! command -v hdiutil &> /dev/null; then
        print_error "未找到 hdiutil，请安装 Xcode Command Line Tools"
        exit 1
    fi
    
    print_success "系统要求检查通过"
}

# 检查项目依赖
check_dependencies() {
    print_info "检查项目依赖..."
    
    if [ ! -d "node_modules" ]; then
        print_warning "未找到 node_modules，正在安装依赖..."
        npm install
    fi
    
    print_success "依赖检查完成"
}

# 清理构建缓存
clean_build() {
    print_info "清理构建缓存..."
    
    if [ -d "dist" ]; then
        rm -rf dist
        print_success "已清理 dist 目录"
    fi
    
    if [ -d "release" ]; then
        rm -rf release
        print_success "已清理 release 目录"
    fi
}

# 构建应用
build_app() {
    local arch=$1
    
    print_info "开始构建应用 (架构: $arch)..."
    
    case $arch in
        "arm64")
            npm run build:arm64
            ;;
        "x64")
            npm run build:x64
            ;;
        "all")
            npm run build:all
            ;;
        *)
            print_error "不支持的架构: $arch"
            exit 1
            ;;
    esac
    
    print_success "应用构建完成"
}

# 创建 DMG
create_dmg() {
    print_info "创建 DMG 安装包..."
    
    node scripts/create-dmg.js
    
    print_success "DMG 创建完成"
}

# 显示构建结果
show_results() {
    print_info "构建结果:"
    
    if [ -d "dist" ]; then
        echo ""
        echo "📁 输出目录: $(pwd)/dist"
        echo ""
        
        # 列出 DMG 文件
        dmg_files=$(find dist -name "*.dmg" 2>/dev/null || true)
        if [ -n "$dmg_files" ]; then
            echo "📦 DMG 文件:"
            while IFS= read -r file; do
                if [ -f "$file" ]; then
                    size=$(du -h "$file" | cut -f1)
                    basename_file=$(basename "$file")
                    echo "  • $basename_file ($size)"
                fi
            done <<< "$dmg_files"
        else
            print_warning "未找到 DMG 文件"
        fi
        
        echo ""
    fi
}

# 主函数
main() {
    local clean_flag=false
    local arch="all"
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            --clean)
                clean_flag=true
                shift
                ;;
            --arm64)
                arch="arm64"
                shift
                ;;
            --x64)
                arch="x64"
                shift
                ;;
            --help)
                show_help
                exit 0
                ;;
            *)
                print_error "未知选项: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    echo "🚀 小梅花AI智能客服 DMG 打包开始..."
    echo ""
    
    # 执行构建流程
    check_requirements
    check_dependencies
    
    if [ "$clean_flag" = true ]; then
        clean_build
    fi
    
    build_app "$arch"
    create_dmg
    show_results
    
    print_success "🎉 DMG 打包完成！"
}

# 运行主函数
main "$@"
