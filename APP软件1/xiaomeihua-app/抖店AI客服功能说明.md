# 🛍️ 抖店AI客服功能说明

## 📋 功能概述

小梅花AI智能客服app现已成功增加抖店AI客服功能，为用户提供完整的抖店平台支持。

## ✅ 新增功能

### 🎯 **抖店AI客服二级菜单**
- ✅ 在抖店一级菜单下新增"抖店AI客服"二级菜单项
- ✅ 使用与微信小店AI智能客服相同的logo图标
- ✅ 点击后访问抖店招商页面：`https://fxg.jinritemai.com/login/common?channel=zhaoshang`

### 🔧 **技术实现**

#### 1. **菜单结构优化**
```html
<!-- 抖店二级菜单 -->
<div class="submenu" id="douyin-store-submenu">
  <div class="menu-item submenu-item" data-action="douyin-customer-service">
    <div class="icon-placeholder douyin-customer-service-icon"></div>
    <span>抖店AI客服</span>
  </div>
</div>
```

#### 2. **图标样式配置**
```css
/* 抖店AI客服图标 - 使用与微信小店AI智能客服相同的图标 */
.douyin-customer-service-icon {
  background-image: url('../assets/icons/店铺客服.png');
  background-size: contain;
  width: 24px;
  height: 24px;
  position: relative;
  display: block;
  margin: 0 auto 6px;
}
```

#### 3. **菜单点击处理逻辑**
```javascript
case 'douyin-customer-service':
  // 抖店AI客服功能
  console.log(`[菜单点击] 抖店AI客服菜单被点击，当前店铺ID: ${currentShopId}`);
  
  // 构建抖店AI客服URL
  const douyinCustomerServiceUrl = `https://fxg.jinritemai.com/login/common?channel=zhaoshang&shop_id=${currentShopId || ''}`;

  // 检查是否已存在当前店铺的抖店AI客服标签页
  const existingDouyinTab = tabs.find(t => 
    (t.url && t.url.includes('fxg.jinritemai.com') && t.shopId === currentShopId) || 
    (t.title === '抖店AI客服' && t.shopId === currentShopId)
  );
  
  if (existingDouyinTab) {
    console.log(`当前店铺(${currentShopId})的抖店AI客服标签页已存在，直接激活`);
    activateTab(existingDouyinTab.id);
  } else {
    // 关闭其他店铺的抖店AI客服标签页
    const otherDouyinTabs = tabs.filter(t => 
      t.title === '抖店AI客服' && t.shopId !== currentShopId
    );
    
    otherDouyinTabs.forEach(tab => {
      removeTab(tab.id, true); // 静默移除，不激活其他标签
    });
    
    console.log(`为当前店铺(${currentShopId})创建新的抖店AI客服标签页`);
    createTab('抖店AI客服', douyinCustomerServiceUrl, true, currentShopId);
  }
  break;
```

#### 4. **脚本注入支持**
```javascript
// 脚本注入白名单
const scriptInjectionWhitelist = [
  'store.weixin.qq.com',      // 微信小店
  'shop.weixin.qq.com',       // 微信商店
  'weixin.qq.com/shop',       // 微信商店
  'filehelper.weixin.qq.com', // AI智能上架
  'channels.weixin.qq.com',   // 视频号助手
  'ai-knowledge.html',        // AI知识库
  'xiaomeihuakefu.cn',        // 小梅花官网页面
  'fxg.jinritemai.com'        // 【新增】抖店AI客服
];

// 标题检查白名单
const titleInjectionWhitelist = [
  'AI智能客服', '店铺客服',
  'AI智能上架', '上架产品',
  '视频号助手',
  'AI知识库',
  '小梅花',
  '抖店AI客服'  // 【新增】抖店AI客服
];
```

#### 5. **跨页面执行支持**
```javascript
// 跨页面环境配置
${createCrossPageEnvironment({
  type: functionType,
  shopId: shopId,
  capabilities: ['script-execution', 'auto-page-opener', 'douyin-customer-service']
})}
```

## 🎯 **功能特性**

### 1. **多店铺支持**
- ✅ 每个店铺独立的抖店AI客服标签页
- ✅ 自动传递店铺ID参数到抖店页面
- ✅ 切换店铺时自动管理对应的标签页

### 2. **智能标签页管理**
- ✅ 检查现有标签页，避免重复创建
- ✅ 自动关闭其他店铺的抖店AI客服标签页
- ✅ 保持当前店铺的标签页状态

### 3. **脚本注入能力**
- ✅ 支持在抖店页面注入自定义脚本
- ✅ 完整的跨页面执行功能支持
- ✅ 与其他功能模块的无缝集成

### 4. **用户体验优化**
- ✅ 统一的图标设计语言
- ✅ 一致的操作逻辑
- ✅ 流畅的页面切换体验

## 🚀 **使用方法**

### 基本使用
1. **打开抖店菜单**：
   - 点击左侧菜单栏的"抖店"一级菜单
   - 菜单会展开显示二级选项

2. **访问抖店AI客服**：
   - 点击"抖店AI客服"二级菜单项
   - 系统会自动打开抖店招商页面
   - 页面URL会包含当前店铺的ID参数

3. **多店铺操作**：
   - 切换到不同店铺后，点击抖店AI客服会打开对应店铺的页面
   - 每个店铺的抖店AI客服标签页完全独立

### 高级功能
1. **脚本执行**：
   - 可以在抖店页面执行自定义JavaScript脚本
   - 支持跨页面脚本执行功能
   - 与其他页面进行数据共享和通信

2. **自动化操作**：
   - 利用跨页面执行功能实现自动化操作
   - 支持批量处理和数据同步

## 📊 **技术规格**

### 支持的功能
- ✅ **页面访问**：抖店招商登录页面
- ✅ **脚本注入**：支持自定义脚本执行
- ✅ **跨页面通信**：与其他页面进行数据交换
- ✅ **多店铺管理**：独立的店铺标签页管理
- ✅ **自动化支持**：完整的自动化脚本执行能力

### 兼容性
- ✅ **平台兼容**：支持macOS (Intel & Apple Silicon)
- ✅ **功能集成**：与现有功能模块完全兼容
- ✅ **数据隔离**：不同店铺的数据完全隔离

## 🔧 **配置说明**

### URL配置
- **基础URL**：`https://fxg.jinritemai.com/login/common?channel=zhaoshang`
- **参数传递**：自动添加`shop_id`参数
- **示例**：`https://fxg.jinritemai.com/login/common?channel=zhaoshang&shop_id=wxe82223181f888d3f`

### 图标配置
- **图标文件**：`src/assets/icons/店铺客服.png`
- **尺寸**：24x24像素
- **样式**：与微信小店AI智能客服保持一致

## 🎉 **总结**

抖店AI客服功能的成功添加为小梅花AI智能客服app带来了：

1. **功能完整性**：覆盖微信小店和抖店两大主流电商平台
2. **用户体验一致性**：统一的操作逻辑和视觉设计
3. **技术架构扩展性**：为未来添加更多平台支持奠定基础
4. **自动化能力增强**：支持抖店平台的自动化脚本执行

用户现在可以在同一个app中管理微信小店和抖店的客服业务，享受统一、高效的多平台管理体验！🚀
