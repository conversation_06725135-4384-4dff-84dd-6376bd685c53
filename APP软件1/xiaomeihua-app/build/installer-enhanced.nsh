; 小梅花AI智能客服增强安装脚本
; 自动获取管理员权限，智能选择安装路径

!include "MUI2.nsh"
!include "WinMessages.nsh"
!include "LogicLib.nsh"
!include "FileFunc.nsh"

; 请求管理员权限
RequestExecutionLevel admin

; 常量定义（避免重复定义）
!ifndef PRODUCT_NAME
!define PRODUCT_NAME "小梅花AI智能客服"
!endif
!ifndef PRODUCT_VERSION
!define PRODUCT_VERSION "5.0.5"
!endif
!ifndef PRODUCT_PUBLISHER
!define PRODUCT_PUBLISHER "小梅花AI科技"
!endif
!define PRODUCT_DIR_REGKEY "Software\Microsoft\Windows\CurrentVersion\App Paths\${PRODUCT_NAME}.exe"
!define PRODUCT_UNINST_KEY "Software\Microsoft\Windows\CurrentVersion\Uninstall\${PRODUCT_NAME}"

; 变量定义
Var SMART_INSTALL_DIR
Var HAS_D_DRIVE
Var ADMIN_RIGHTS

; 检查管理员权限函数
Function CheckAdminRights
  ClearErrors
  UserInfo::GetAccountType
  Pop $0
  ${If} $0 != "admin"
    MessageBox MB_ICONSTOP "此安装程序需要管理员权限才能正常运行。$\r$\n请右键点击安装程序并选择以管理员身份运行。"
    Abort
  ${EndIf}
  StrCpy $ADMIN_RIGHTS "true"
FunctionEnd

; 智能选择安装目录函数
Function SmartSelectInstallDir
  ; 检查D盘是否存在
  ${If} ${FileExists} "D:\"
    StrCpy $HAS_D_DRIVE "true"
    StrCpy $SMART_INSTALL_DIR "D:\${PRODUCT_NAME}"
  ${Else}
    StrCpy $HAS_D_DRIVE "false"
    StrCpy $SMART_INSTALL_DIR "C:\Program Files\${PRODUCT_NAME}"
  ${EndIf}
  
  ; 设置默认安装目录
  StrCpy $INSTDIR $SMART_INSTALL_DIR
FunctionEnd

; 创建必要的目录和权限设置
Function SetupDirectoriesAndPermissions
  ; 创建安装目录
  CreateDirectory "$INSTDIR"
  
  ; 设置目录权限 - 给所有用户完全控制权限
  nsExec::ExecToLog 'icacls "$INSTDIR" /grant "Everyone:(OI)(CI)F" /T'
  nsExec::ExecToLog 'icacls "$INSTDIR" /grant "Users:(OI)(CI)F" /T'
  nsExec::ExecToLog 'icacls "$INSTDIR" /grant "Authenticated Users:(OI)(CI)F" /T'
  
  ; 创建用户数据目录
  CreateDirectory "$APPDATA\${PRODUCT_NAME}"
  nsExec::ExecToLog 'icacls "$APPDATA\${PRODUCT_NAME}" /grant "Everyone:(OI)(CI)F" /T'
  
  ; 创建本地应用数据目录
  CreateDirectory "$LOCALAPPDATA\${PRODUCT_NAME}"
  nsExec::ExecToLog 'icacls "$LOCALAPPDATA\${PRODUCT_NAME}" /grant "Everyone:(OI)(CI)F" /T'
  
  ; 创建程序数据目录
  CreateDirectory "$PROGRAMDATA\${PRODUCT_NAME}"
  nsExec::ExecToLog 'icacls "$PROGRAMDATA\${PRODUCT_NAME}" /grant "Everyone:(OI)(CI)F" /T'
FunctionEnd

; 安装前检查函数
Function .onInit
  ; 检查管理员权限
  Call CheckAdminRights
  
  ; 智能选择安装目录
  Call SmartSelectInstallDir
  
  ; 显示安装路径信息
  ${If} $HAS_D_DRIVE == "true"
    MessageBox MB_ICONINFORMATION "检测到D盘存在，将安装到: $SMART_INSTALL_DIR"
  ${Else}
    MessageBox MB_ICONINFORMATION "未检测到D盘，将安装到: $SMART_INSTALL_DIR"
  ${EndIf}
FunctionEnd

; 安装文件前的准备工作
Function .onInstFilesStart
  Call SetupDirectoriesAndPermissions
FunctionEnd

; 安装完成后的设置
Function .onInstSuccess
  ; 写入注册表
  WriteRegStr HKLM "${PRODUCT_DIR_REGKEY}" "" "$INSTDIR\${PRODUCT_NAME}.exe"
  WriteRegStr HKLM "${PRODUCT_UNINST_KEY}" "DisplayName" "${PRODUCT_NAME}"
  WriteRegStr HKLM "${PRODUCT_UNINST_KEY}" "UninstallString" "$INSTDIR\uninst.exe"
  WriteRegStr HKLM "${PRODUCT_UNINST_KEY}" "DisplayIcon" "$INSTDIR\${PRODUCT_NAME}.exe"
  WriteRegStr HKLM "${PRODUCT_UNINST_KEY}" "DisplayVersion" "${PRODUCT_VERSION}"
  WriteRegStr HKLM "${PRODUCT_UNINST_KEY}" "Publisher" "${PRODUCT_PUBLISHER}"
  WriteRegStr HKLM "${PRODUCT_UNINST_KEY}" "InstallLocation" "$INSTDIR"
  WriteRegDWORD HKLM "${PRODUCT_UNINST_KEY}" "NoModify" 1
  WriteRegDWORD HKLM "${PRODUCT_UNINST_KEY}" "NoRepair" 1
  
  ; 设置应用程序权限
  nsExec::ExecToLog 'icacls "$INSTDIR\${PRODUCT_NAME}.exe" /grant "Everyone:(F)" /T'
  
  ; 创建防火墙例外
  nsExec::ExecToLog 'netsh advfirewall firewall add rule name="${PRODUCT_NAME}" dir=in action=allow program="$INSTDIR\${PRODUCT_NAME}.exe"'
  nsExec::ExecToLog 'netsh advfirewall firewall add rule name="${PRODUCT_NAME}" dir=out action=allow program="$INSTDIR\${PRODUCT_NAME}.exe"'
  
  ; 设置环境变量
  WriteRegExpandStr HKLM "SYSTEM\CurrentControlSet\Control\Session Manager\Environment" "XIAOMEIHUA_HOME" "$INSTDIR"
  
  ; 刷新环境变量
  SendMessage ${HWND_BROADCAST} ${WM_WININICHANGE} 0 "STR:Environment" /TIMEOUT=5000
  
  MessageBox MB_ICONINFORMATION "安装完成！$\r$\n安装路径: $INSTDIR$\r$\n已获取完整系统权限，登录保持功能已优化。"
FunctionEnd

; 卸载函数
Function un.onInit
  MessageBox MB_ICONQUESTION|MB_YESNO|MB_DEFBUTTON2 "您确实要完全移除 ${PRODUCT_NAME} 及其所有组件吗？" IDYES +2
  Abort
FunctionEnd

Function un.onUninstSuccess
  ; 清理注册表
  DeleteRegKey HKLM "${PRODUCT_UNINST_KEY}"
  DeleteRegKey HKLM "${PRODUCT_DIR_REGKEY}"
  
  ; 清理环境变量
  DeleteRegValue HKLM "SYSTEM\CurrentControlSet\Control\Session Manager\Environment" "XIAOMEIHUA_HOME"
  
  ; 清理防火墙规则
  nsExec::ExecToLog 'netsh advfirewall firewall delete rule name="${PRODUCT_NAME}"'
  
  ; 刷新环境变量
  SendMessage ${HWND_BROADCAST} ${WM_WININICHANGE} 0 "STR:Environment" /TIMEOUT=5000
  
  MessageBox MB_ICONINFORMATION "${PRODUCT_NAME} 已从您的计算机中移除。"
FunctionEnd

; MUI设置
!define MUI_ABORTWARNING
!define MUI_ICON "icon.ico"
!define MUI_UNICON "icon.ico"

; 欢迎页面
!define MUI_WELCOMEPAGE_TITLE "欢迎安装${PRODUCT_NAME}"
!define MUI_WELCOMEPAGE_TEXT "此向导将引导您完成${PRODUCT_NAME}的安装。$\r$\n$\r$\n安装程序将自动选择最佳安装路径并获取必要的系统权限。$\r$\n$\r$\n点击下一步继续。"

; 目录选择页面
!define MUI_DIRECTORYPAGE_TEXT_TOP "安装程序已智能选择安装目录。如需更改，请选择其他目录。"

; 完成页面
!define MUI_FINISHPAGE_RUN "$INSTDIR\${PRODUCT_NAME}.exe"
!define MUI_FINISHPAGE_RUN_TEXT "立即运行${PRODUCT_NAME}"

; 页面顺序
!insertmacro MUI_PAGE_WELCOME
!insertmacro MUI_PAGE_DIRECTORY
!insertmacro MUI_PAGE_INSTFILES
!insertmacro MUI_PAGE_FINISH

; 卸载页面
!insertmacro MUI_UNPAGE_CONFIRM
!insertmacro MUI_UNPAGE_INSTFILES

; 语言文件
!insertmacro MUI_LANGUAGE "SimpChinese"
