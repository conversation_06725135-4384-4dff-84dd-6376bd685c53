# 🚀 跨页面执行功能使用指南

## 📋 功能概述

小梅花AI智能客服app现已支持强大的跨页面执行功能，包括：

- ✅ **跨页面脚本执行**：在多个页面间执行JavaScript代码
- ✅ **自动页面打开**：脚本可以自动打开指定页面
- ✅ **页面间数据共享**：在不同页面间共享数据
- ✅ **消息广播**：向所有页面或指定页面发送消息
- ✅ **页面管理**：注册、注销和查询页面状态

## 🛠️ API 使用方法

### 1. 跨页面脚本执行

#### 基本用法
```javascript
// 在所有页面执行脚本
await CrossPageAPI.executeScript({
  script: `
    console.log('这段代码在所有页面执行');
    document.title = '已更新标题';
    return '执行完成';
  `
});

// 在指定类型的页面执行脚本
await CrossPageAPI.executeScript({
  script: `
    // 获取页面数据
    const data = document.querySelector('#data-container').textContent;
    return { pageData: data, timestamp: new Date() };
  `,
  targetType: 'customer_service', // 只在客服页面执行
  executeOrder: 'sequential' // 顺序执行
});

// 在包含特定URL的页面执行脚本
await CrossPageAPI.executeScript({
  script: `
    // 填写表单
    document.querySelector('#username').value = '自动填写';
    document.querySelector('#submit-btn').click();
  `,
  targetUrl: 'store.weixin.qq.com',
  waitForPage: true, // 如果页面不存在，等待页面打开
  timeout: 30000
});
```

#### 高级用法
```javascript
// 并行执行多页面脚本
const result = await CrossPageAPI.executeScript({
  script: `
    // 复杂的数据处理脚本
    const processData = async () => {
      const elements = document.querySelectorAll('.product-item');
      const products = [];
      
      for (let element of elements) {
        products.push({
          name: element.querySelector('.name').textContent,
          price: element.querySelector('.price').textContent,
          id: element.dataset.id
        });
      }
      
      return products;
    };
    
    return await processData();
  `,
  targetPages: ['page_123', 'page_456'], // 指定页面ID
  executeOrder: 'parallel', // 并行执行
  timeout: 60000
});

console.log('执行结果:', result);
```

### 2. 自动页面打开

#### 简单页面打开
```javascript
// 打开单个页面
await AutoPageOpener.openAndWait('https://store.weixin.qq.com/shop', {
  title: '微信小店',
  waitForSelector: '.shop-container', // 等待特定元素出现
  executeAfterLoad: `
    console.log('页面加载完成，开始执行脚本');
    // 页面加载后执行的代码
  `,
  timeout: 30000
});

// 批量打开页面
const urls = [
  'https://store.weixin.qq.com/shop',
  'https://channels.weixin.qq.com',
  'https://filehelper.weixin.qq.com'
];

await AutoPageOpener.openMultiple(urls, {
  sequential: true, // 顺序打开
  delay: 2000, // 每个页面间隔2秒
  executeAfterAll: `
    // 所有页面打开后执行
    console.log('所有页面已打开');
  `
});
```

#### 智能页面打开
```javascript
// 根据条件打开页面
const shouldOpenPage = await CrossPageAPI.getSharedData('needNewPage');
if (shouldOpenPage) {
  const newPage = await AutoPageOpener.openAndWait('https://example.com', {
    title: '条件触发页面',
    waitForSelector: '#main-content',
    executeAfterLoad: `
      // 设置页面状态
      await CrossPageAPI.setSharedData('pageOpened', true);
      
      // 通知其他页面
      await CrossPageAPI.broadcast({
        type: 'page-opened',
        url: window.location.href,
        timestamp: new Date()
      });
    `
  });
}
```

### 3. 页面间数据共享

#### 基本数据共享
```javascript
// 设置共享数据
await CrossPageAPI.setSharedData('userInfo', {
  name: '张三',
  id: '12345',
  role: 'admin'
});

// 获取共享数据
const userInfo = await CrossPageAPI.getSharedData('userInfo');
console.log('用户信息:', userInfo);

// 使用数据同步器
await DataSyncer.syncToAll({
  currentStep: 'step2',
  progress: 50,
  lastUpdate: new Date()
});

// 获取最新数据
const latestData = await DataSyncer.getLatest(['currentStep', 'progress']);
```

#### 实时数据同步
```javascript
// 监听数据同步事件
window.addEventListener('dataSyncReceived', (event) => {
  const { data, timestamp, source } = event.detail;
  console.log('收到数据同步:', data);
  console.log('来源页面:', source);
  
  // 更新本地UI
  updateUI(data);
});

// 监听跨页面消息
window.addEventListener('crossPageMessage', (event) => {
  const message = event.detail;
  
  if (message.type === 'user-action') {
    handleUserAction(message.data);
  }
});
```

### 4. 页面管理

#### 页面注册和查询
```javascript
// 获取所有页面列表
const pages = await CrossPageAPI.getPageList();
console.log('当前打开的页面:', pages);

// 查找特定页面
const customerServicePages = pages.filter(p => p.type === 'customer_service');
const weixinPages = pages.filter(p => p.url.includes('weixin.qq.com'));

// 获取当前页面信息
const currentPage = CrossPageAPI.getCurrentPageInfo();
console.log('当前页面:', currentPage);
```

#### 消息通信
```javascript
// 广播消息到所有页面
await CrossPageAPI.broadcast({
  type: 'notification',
  message: '系统维护通知',
  level: 'warning'
});

// 发送消息到特定页面
await CrossPageAPI.sendToPage('page_123', {
  type: 'command',
  action: 'refresh-data'
});
```

## 🎯 实际应用场景

### 场景1：多店铺数据同步
```javascript
// 在主页面收集所有店铺数据
const collectAllShopData = async () => {
  const pages = await CrossPageAPI.getPageList();
  const shopPages = pages.filter(p => p.shopId);
  
  const allData = [];
  
  for (const page of shopPages) {
    const result = await CrossPageAPI.executeScript({
      script: `
        // 收集店铺数据
        const shopData = {
          shopId: window.xiaomeihuaShopInfo.shopId,
          shopName: window.xiaomeihuaShopInfo.shopName,
          orders: document.querySelectorAll('.order-item').length,
          products: document.querySelectorAll('.product-item').length,
          timestamp: new Date()
        };
        return shopData;
      `,
      targetPages: [page.id]
    });
    
    if (result.success) {
      allData.push(result.results[0].result.result);
    }
  }
  
  return allData;
};

// 使用
const shopData = await collectAllShopData();
console.log('所有店铺数据:', shopData);
```

### 场景2：自动化工作流
```javascript
// 自动化客服工作流
const automateCustomerService = async () => {
  // 1. 打开客服页面
  await AutoPageOpener.openAndWait('https://store.weixin.qq.com/customer-service', {
    title: '客服页面',
    waitForSelector: '.chat-container'
  });
  
  // 2. 等待2秒
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  // 3. 执行客服脚本
  await CrossPageAPI.executeScript({
    script: `
      // 自动回复逻辑
      const autoReply = () => {
        const messages = document.querySelectorAll('.new-message');
        messages.forEach(msg => {
          const text = msg.textContent;
          if (text.includes('价格')) {
            // 自动回复价格信息
            sendReply('您好，我们的产品价格请查看商品详情页');
          }
        });
      };
      
      // 每5秒检查一次新消息
      setInterval(autoReply, 5000);
      
      return '自动回复已启动';
    `,
    targetUrl: 'customer-service'
  });
  
  console.log('客服自动化已启动');
};
```

### 场景3：批量商品上架
```javascript
// 批量商品上架流程
const batchUploadProducts = async (products) => {
  // 1. 打开上架页面
  await AutoPageOpener.openAndWait('https://filehelper.weixin.qq.com/upload', {
    title: '商品上架',
    waitForSelector: '.upload-form'
  });
  
  // 2. 逐个上架商品
  for (const product of products) {
    await CrossPageAPI.executeScript({
      script: `
        // 填写商品信息
        document.querySelector('#product-name').value = '${product.name}';
        document.querySelector('#product-price').value = '${product.price}';
        document.querySelector('#product-desc').value = '${product.description}';
        
        // 上传图片（如果有）
        if ('${product.image}') {
          // 图片上传逻辑
        }
        
        // 提交表单
        document.querySelector('#submit-btn').click();
        
        // 等待提交完成
        await new Promise(resolve => setTimeout(resolve, 3000));
        
        return '商品上架完成: ${product.name}';
      `,
      targetUrl: 'upload',
      timeout: 30000
    });
    
    // 商品间隔
    await new Promise(resolve => setTimeout(resolve, 2000));
  }
  
  console.log('批量上架完成');
};
```

## 🔧 辅助函数

### 简化API使用
```javascript
// 使用简化的辅助函数
await ScriptHelpers.openPage('https://example.com');
await ScriptHelpers.executeOnPage('example.com', 'console.log("Hello")');
await ScriptHelpers.shareData('key', 'value');
const data = await ScriptHelpers.getData('key');
await ScriptHelpers.waitForElement('#submit-btn');
await ScriptHelpers.goto('https://newpage.com', true); // 新标签页打开
```

## ⚠️ 注意事项

1. **安全性**：只在可信页面执行脚本
2. **性能**：避免在大量页面同时执行复杂脚本
3. **错误处理**：始终使用try-catch处理异步操作
4. **超时设置**：为长时间操作设置合理的超时时间
5. **资源清理**：及时清理不需要的页面和数据

## 🚀 最佳实践

1. **模块化脚本**：将复杂逻辑拆分为小的函数
2. **状态管理**：使用共享数据管理应用状态
3. **错误恢复**：实现自动重试和错误恢复机制
4. **日志记录**：记录关键操作的执行日志
5. **用户反馈**：提供操作进度和结果反馈

通过这些功能，您可以创建强大的自动化脚本，实现跨页面的复杂业务逻辑！
