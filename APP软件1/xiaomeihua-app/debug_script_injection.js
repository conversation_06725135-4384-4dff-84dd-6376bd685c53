#!/usr/bin/env node

/**
 * 调试脚本注入问题
 * 帮助诊断为什么脚本没有在AI智能客服页面加载
 */

console.log('🔍 开始调试脚本注入问题...\n');

// 模拟可能的URL
const testUrls = [
  'https://store.weixin.qq.com/shop/kf',
  'https://store.weixin.qq.com/shop/kf?shop_id=wxe82223181f888d3f',
  'https://store.weixin.qq.com/shop/home',
  'https://store.weixin.qq.com/shop/order/list',
  'https://xiaomeihuakefu.cn/ai-knowledge.html',
  'https://filehelper.weixin.qq.com/',
  'https://channels.weixin.qq.com/',
  'https://shop.weixin.qq.com/shop/kf',
  'https://weixin.qq.com/shop/kf'
];

// 模拟触发条件检查
function checkTriggerConditions(url) {
  console.log(`\n🧪 测试URL: ${url}`);
  
  const conditions = [
    { name: 'store.weixin.qq.com', check: url.includes('store.weixin.qq.com') },
    { name: 'shop.weixin.qq.com', check: url.includes('shop.weixin.qq.com') },
    { name: 'weixin.qq.com/shop', check: url.includes('weixin.qq.com/shop') },
    { name: 'filehelper.weixin.qq.com', check: url.includes('filehelper.weixin.qq.com') },
    { name: 'channels.weixin.qq.com', check: url.includes('channels.weixin.qq.com') }
  ];
  
  let shouldTrigger = false;
  conditions.forEach(condition => {
    if (condition.check) {
      console.log(`  ✅ 匹配条件: ${condition.name}`);
      shouldTrigger = true;
    } else {
      console.log(`  ❌ 不匹配: ${condition.name}`);
    }
  });
  
  if (shouldTrigger) {
    console.log(`  🎯 结果: 应该触发脚本注入`);
  } else {
    console.log(`  🚫 结果: 不会触发脚本注入`);
  }
  
  return shouldTrigger;
}

// 测试所有URL
console.log('📋 测试所有可能的URL触发条件:');
console.log('='.repeat(60));

testUrls.forEach(url => {
  checkTriggerConditions(url);
});

console.log('\n' + '='.repeat(60));
console.log('📊 调试建议:');
console.log('1. 请在APP中按F12打开开发者工具');
console.log('2. 在控制台输入: window.location.href');
console.log('3. 查看当前页面的确切URL');
console.log('4. 检查是否有 "[小梅花] 检测到目标页面" 的日志');
console.log('5. 如果没有此日志，说明触发条件不满足');

console.log('\n🔧 可能的问题:');
console.log('- 当前页面URL不在触发条件范围内');
console.log('- JavaScript执行错误导致脚本中断');
console.log('- injectKamiConnector函数不存在或有错误');
console.log('- 网络请求失败导致脚本获取失败');

console.log('\n📝 下一步调试:');
console.log('1. 确认当前页面URL');
console.log('2. 检查控制台是否有JavaScript错误');
console.log('3. 查看网络请求是否成功');
console.log('4. 验证API返回的脚本内容和match_urls');
