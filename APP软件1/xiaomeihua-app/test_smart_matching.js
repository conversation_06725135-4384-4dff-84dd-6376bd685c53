#!/usr/bin/env node

/**
 * 测试智能URL匹配修复
 * 验证向后兼容逻辑是否正确工作
 */

console.log('🧪 开始测试智能URL匹配修复...\n');

// 模拟测试场景
const testScenarios = [
  {
    name: '✅ 有@match规则 + 匹配URL - 微信商城客服页面',
    pageUrl: 'https://store.weixin.qq.com/shop/kf?shopId=123',
    matchUrls: ['https://store.weixin.qq.com/shop/kf*', 'https://store.weixin.qq.com/shop/home*'],
    expectedResult: true,
    expectedReason: '@match规则匹配'
  },
  {
    name: '❌ 有@match规则 + 不匹配URL - AI管理后台',
    pageUrl: 'https://ai-admin.example.com/dashboard',
    matchUrls: ['https://store.weixin.qq.com/shop/kf*', 'https://store.weixin.qq.com/shop/home*'],
    expectedResult: false,
    expectedReason: '@match规则不匹配'
  },
  {
    name: '✅ 无@match规则 + AI智能客服页面 - 向后兼容',
    pageUrl: 'https://store.weixin.qq.com/shop/kf',
    matchUrls: [],
    expectedResult: true,
    expectedReason: '向后兼容：AI智能客服页面'
  },
  {
    name: '❌ 无@match规则 + 非微信页面 - 向后兼容限制',
    pageUrl: 'https://ai-admin.example.com/dashboard',
    matchUrls: [],
    expectedResult: false,
    expectedReason: '向后兼容：非AI智能客服页面'
  },
  {
    name: '❌ 无@match规则 + AI智能上架页面 - 向后兼容限制',
    pageUrl: 'https://filehelper.weixin.qq.com/',
    matchUrls: [],
    expectedResult: false,
    expectedReason: '向后兼容：非AI智能客服页面'
  },
  {
    name: '❌ 无@match规则 + 视频号助手页面 - 向后兼容限制',
    pageUrl: 'https://channels.weixin.qq.com/',
    matchUrls: [],
    expectedResult: false,
    expectedReason: '向后兼容：非AI智能客服页面'
  },
  {
    name: '✅ 有@match规则 + AI知识库页面',
    pageUrl: 'https://xiaomeihuakefu.cn/ai-knowledge.html?param=test',
    matchUrls: ['https://xiaomeihuakefu.cn/ai-knowledge.html*'],
    expectedResult: true,
    expectedReason: '@match规则匹配'
  }
];

// 简化的URL匹配函数
function isUrlMatch(url, pattern) {
  if (!url || !pattern) return false;
  
  // 简单字符串包含匹配
  const cleanPattern = pattern.replace(/\*/g, '');
  if (cleanPattern && url.includes(cleanPattern)) {
    return true;
  }
  
  return false;
}

// 模拟智能匹配逻辑
function simulateSmartMatching(pageUrl, matchUrls) {
  console.log(`📍 当前URL: ${pageUrl}`);
  console.log(`📋 匹配规则: ${JSON.stringify(matchUrls)}`);
  
  if (matchUrls && matchUrls.length > 0) {
    // 有@match规则：严格按照规则执行
    console.log('📋 脚本有@match规则，进行严格匹配');
    
    let urlMatches = false;
    for (const pattern of matchUrls) {
      if (isUrlMatch(pageUrl, pattern)) {
        console.log(`✅ URL匹配成功: ${pattern}`);
        urlMatches = true;
        break;
      }
    }
    
    if (!urlMatches) {
      console.log('❌ 当前URL不匹配任何@match规则，拒绝执行脚本');
      return { result: false, reason: '@match规则不匹配' };
    }
    
    console.log('✅ @match规则验证通过，继续执行脚本');
    return { result: true, reason: '@match规则匹配' };
  } else {
    // 无@match规则：向后兼容，仅在AI智能客服页面执行
    console.log('⚠️ 脚本中没有@match规则，使用向后兼容模式');

    // 【精确限制】只在AI智能客服页面执行无@match规则的脚本
    const isCustomerServicePage = pageUrl.includes('store.weixin.qq.com/shop/kf');

    if (isCustomerServicePage) {
      console.log('✅ 向后兼容：在AI智能客服页面执行无@match规则的脚本');
      return { result: true, reason: '向后兼容：AI智能客服页面' };
    } else {
      console.log('❌ 向后兼容：仅AI智能客服页面可执行无@match规则的脚本');
      return { result: false, reason: '向后兼容：非AI智能客服页面' };
    }
  }
}

// 执行测试
let passedTests = 0;
let totalTests = testScenarios.length;

testScenarios.forEach((scenario, index) => {
  console.log(`\n🧪 测试 ${index + 1}: ${scenario.name}`);
  console.log('=' .repeat(80));
  
  const result = simulateSmartMatching(scenario.pageUrl, scenario.matchUrls);
  const testPassed = result.result === scenario.expectedResult && result.reason === scenario.expectedReason;
  
  if (testPassed) {
    console.log('✅ 测试通过');
    passedTests++;
  } else {
    console.log('❌ 测试失败');
    console.log(`   期望结果: ${scenario.expectedResult ? '允许执行' : '拒绝执行'} (${scenario.expectedReason})`);
    console.log(`   实际结果: ${result.result ? '允许执行' : '拒绝执行'} (${result.reason})`);
  }
});

// 输出测试结果
console.log('\n' + '='.repeat(80));
console.log('📊 测试结果汇总:');
console.log(`✅ 通过: ${passedTests}/${totalTests}`);
console.log(`❌ 失败: ${totalTests - passedTests}/${totalTests}`);

if (passedTests === totalTests) {
  console.log('\n🎉 所有测试通过！精确URL匹配修复成功！');
  console.log('✅ 有@match规则的脚本：严格按照规则执行');
  console.log('✅ 无@match规则的脚本：仅在AI智能客服页面执行（精确向后兼容）');
  console.log('✅ AI智能上架和视频号助手页面不会执行无@match规则的脚本');
  console.log('✅ 既解决了原问题，又提供了精确的控制');
  process.exit(0);
} else {
  console.log('\n⚠️ 部分测试失败，需要进一步检查修复代码');
  process.exit(1);
}
