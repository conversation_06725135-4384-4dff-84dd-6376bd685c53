/**
 * 跨页面功能测试脚本
 * 在浏览器控制台中运行这些代码来测试跨页面功能
 */

// 测试1: 基本跨页面脚本执行
async function testBasicCrossPageExecution() {
  console.log('🧪 测试1: 基本跨页面脚本执行');
  
  try {
    const result = await CrossPageAPI.executeScript({
      script: `
        console.log('这是跨页面执行的脚本');
        document.title = '跨页面测试 - ' + new Date().toLocaleTimeString();
        return {
          url: window.location.href,
          title: document.title,
          timestamp: new Date().toISOString()
        };
      `
    });
    
    console.log('✅ 跨页面脚本执行成功:', result);
  } catch (error) {
    console.error('❌ 跨页面脚本执行失败:', error);
  }
}

// 测试2: 自动打开页面
async function testAutoPageOpener() {
  console.log('🧪 测试2: 自动打开页面');
  
  try {
    const result = await AutoPageOpener.openAndWait('https://xiaomeihuakefu.cn', {
      title: '测试页面',
      waitForSelector: 'body',
      executeAfterLoad: `
        console.log('页面加载完成，执行测试脚本');
        document.body.style.border = '5px solid red';
        return '页面标记完成';
      `,
      timeout: 30000
    });
    
    console.log('✅ 自动打开页面成功:', result);
  } catch (error) {
    console.error('❌ 自动打开页面失败:', error);
  }
}

// 测试3: 数据共享
async function testDataSharing() {
  console.log('🧪 测试3: 数据共享');
  
  try {
    // 设置共享数据
    await CrossPageAPI.setSharedData('testData', {
      message: 'Hello from cross-page!',
      timestamp: new Date(),
      counter: Math.floor(Math.random() * 1000)
    });
    
    // 获取共享数据
    const data = await CrossPageAPI.getSharedData('testData');
    console.log('✅ 数据共享测试成功:', data);
    
    // 同步数据到所有页面
    await DataSyncer.syncToAll({
      syncTest: true,
      syncTime: new Date(),
      message: '这是同步测试数据'
    });
    
    console.log('✅ 数据同步完成');
  } catch (error) {
    console.error('❌ 数据共享测试失败:', error);
  }
}

// 测试4: 页面管理
async function testPageManagement() {
  console.log('🧪 测试4: 页面管理');
  
  try {
    // 获取页面列表
    const pages = await CrossPageAPI.getPageList();
    console.log('📋 当前页面列表:', pages);
    
    // 获取当前页面信息
    const currentPage = CrossPageAPI.getCurrentPageInfo();
    console.log('📄 当前页面信息:', currentPage);
    
    // 广播测试消息
    await CrossPageAPI.broadcast({
      type: 'test-message',
      message: '这是广播测试消息',
      timestamp: new Date()
    });
    
    console.log('✅ 页面管理测试完成');
  } catch (error) {
    console.error('❌ 页面管理测试失败:', error);
  }
}

// 测试5: 复杂工作流
async function testComplexWorkflow() {
  console.log('🧪 测试5: 复杂工作流');
  
  try {
    // 1. 收集当前页面数据
    const pageData = await CrossPageAPI.executeScript({
      script: `
        return {
          url: window.location.href,
          title: document.title,
          links: Array.from(document.querySelectorAll('a')).length,
          images: Array.from(document.querySelectorAll('img')).length,
          forms: Array.from(document.querySelectorAll('form')).length
        };
      `
    });
    
    console.log('📊 页面数据收集:', pageData);
    
    // 2. 保存数据到共享存储
    if (pageData.success && pageData.results.length > 0) {
      await CrossPageAPI.setSharedData('pageAnalysis', pageData.results[0].result.result);
    }
    
    // 3. 通知其他页面
    await CrossPageAPI.broadcast({
      type: 'workflow-complete',
      step: 'data-collection',
      data: pageData
    });
    
    console.log('✅ 复杂工作流测试完成');
  } catch (error) {
    console.error('❌ 复杂工作流测试失败:', error);
  }
}

// 测试6: 简化API测试
async function testSimplifiedAPI() {
  console.log('🧪 测试6: 简化API测试');
  
  try {
    // 使用简化的辅助函数
    await ScriptHelpers.shareData('simpleTest', 'Hello World');
    const data = await ScriptHelpers.getData('simpleTest');
    console.log('📦 简化数据API测试:', data);
    
    // 等待元素测试
    try {
      const element = await ScriptHelpers.waitForElement('body', 5000);
      console.log('🎯 元素等待测试成功:', element.tagName);
    } catch (error) {
      console.log('⏰ 元素等待超时（正常）');
    }
    
    console.log('✅ 简化API测试完成');
  } catch (error) {
    console.error('❌ 简化API测试失败:', error);
  }
}

// 运行所有测试
async function runAllTests() {
  console.log('🚀 开始运行跨页面功能测试...');
  
  await testBasicCrossPageExecution();
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  await testDataSharing();
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  await testPageManagement();
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  await testComplexWorkflow();
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  await testSimplifiedAPI();
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  // 注意：自动打开页面测试可能会打开新页面，所以放在最后
  // await testAutoPageOpener();
  
  console.log('🎉 所有测试完成！');
}

// 监听跨页面消息的测试
function setupMessageListeners() {
  console.log('📡 设置跨页面消息监听器...');
  
  // 监听跨页面消息
  window.addEventListener('crossPageMessage', (event) => {
    console.log('📨 收到跨页面消息:', event.detail);
  });
  
  // 监听数据同步
  window.addEventListener('dataSyncReceived', (event) => {
    console.log('🔄 收到数据同步:', event.detail);
  });
  
  // 监听页面注册完成
  window.addEventListener('pageRegistered', (event) => {
    console.log('📝 页面注册完成:', event.detail);
  });
  
  // 监听跨页面环境就绪
  window.addEventListener('crossPageEnvironmentReady', (event) => {
    console.log('✅ 跨页面环境就绪:', event.detail);
  });
}

// 自动设置监听器
if (typeof window !== 'undefined') {
  setupMessageListeners();
}

// 导出测试函数（如果在Node.js环境中）
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    testBasicCrossPageExecution,
    testAutoPageOpener,
    testDataSharing,
    testPageManagement,
    testComplexWorkflow,
    testSimplifiedAPI,
    runAllTests,
    setupMessageListeners
  };
}

// 使用说明
console.log(`
🎯 跨页面功能测试脚本已加载

使用方法：
1. 在浏览器控制台运行 runAllTests() 来执行所有测试
2. 或者单独运行特定测试函数：
   - testBasicCrossPageExecution()
   - testDataSharing()
   - testPageManagement()
   - testComplexWorkflow()
   - testSimplifiedAPI()
   - testAutoPageOpener() // 注意：会打开新页面

3. 消息监听器已自动设置，会在控制台显示跨页面消息

示例：
runAllTests(); // 运行所有测试
`);
