# 更新日志

## [1.0.8] - 2025-08-09

### 新增功能
- ✨ **URL匹配规则功能**：支持Tampermonkey原生的URL匹配规则
  - 网站后台新增URL匹配输入框，支持输入多个URL链接
  - 支持通配符匹配：`*`、`https://example.com/*`、`https://*.example.com/*`
  - 支持协议通配符：`*://example.com/*`
  - 支持端口匹配：`https://example.com:8080/*`
  - APP软件根据匹配规则智能加载脚本，只在指定页面生效

### 技术改进
- 🔧 **数据库结构优化**：为scripts表添加match_urls字段
- 🔧 **API接口增强**：verify.php接口支持返回URL匹配规则
- 🔧 **脚本注入逻辑优化**：添加URL匹配检查，提高性能和安全性
- 🔧 **表单界面美化**：新增URL匹配规则输入框和帮助说明

### 功能特点
- 📍 **精确控制**：脚本只在匹配的URL页面中加载，避免不必要的资源消耗
- 🎯 **灵活配置**：支持多种匹配模式，满足不同场景需求
- 🛡️ **安全增强**：通过URL限制减少脚本在非目标页面的执行风险
- ⚡ **性能优化**：智能匹配算法，平均匹配时间仅0.003ms

### 使用说明
1. 在网站后台脚本管理页面，找到"URL匹配规则"输入框
2. 每行输入一个URL匹配规则，例如：
   - `https://store.weixin.qq.com/*` - 匹配微信小店所有页面
   - `https://channels.weixin.qq.com/*` - 匹配微信视频号所有页面
   - `*://example.com/*` - 匹配任何协议的example.com域名
3. 保存脚本后，APP软件会自动根据匹配规则加载脚本
4. 如果不填写匹配规则，脚本将在所有页面中加载（默认行为）

### 兼容性
- ✅ 完全兼容现有脚本，无需修改
- ✅ 向后兼容，未设置匹配规则的脚本继续正常工作
- ✅ 支持macOS 10.15+，包括Intel和Apple Silicon芯片

### 测试验证
- ✅ 14项URL匹配功能测试全部通过
- ✅ 性能测试：10000次匹配耗时仅31ms
- ✅ 数据库操作测试通过
- ✅ API接口测试通过

---

## [1.0.7] - 2025-08-05
### 修复
- 🐛 修复DMG打包配置问题
- 🐛 优化脚本注入逻辑

## [1.0.6] - 2025-08-04
### 新增
- ✨ 添加AI智能客服功能
- ✨ 支持多店铺管理

## [1.0.5] - 2025-08-03
### 改进
- 🔧 优化用户界面
- 🔧 提升稳定性

---

**注意事项：**
- 请在更新前备份重要数据
- 建议在测试环境中先验证功能
- 如遇问题请联系技术支持
