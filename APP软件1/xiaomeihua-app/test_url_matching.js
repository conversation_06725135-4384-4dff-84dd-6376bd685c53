/**
 * APP端URL匹配功能测试
 * 测试kamiyan-injector.js中的matchUrl函数
 */

const { matchUrl } = require('./src/kamiyan-injector');

// 测试用例
const testCases = [
    // 基本匹配测试
    {
        url: 'https://example.com/page',
        patterns: ['https://example.com/*'],
        expected: true,
        description: '基本通配符匹配'
    },
    {
        url: 'https://example.com/page/subpage',
        patterns: ['https://example.com/*'],
        expected: true,
        description: '深层路径匹配'
    },
    {
        url: 'https://sub.example.com/page',
        patterns: ['https://*.example.com/*'],
        expected: true,
        description: '子域名通配符匹配'
    },
    {
        url: 'http://example.com/page',
        patterns: ['*://example.com/*'],
        expected: true,
        description: '协议通配符匹配'
    },
    {
        url: 'https://example.com:8080/page',
        patterns: ['https://example.com:8080/*'],
        expected: true,
        description: '端口匹配'
    },
    
    // 不匹配的测试
    {
        url: 'https://other.com/page',
        patterns: ['https://example.com/*'],
        expected: false,
        description: '不同域名不匹配'
    },
    {
        url: 'https://example.com/page',
        patterns: ['https://other.com/*'],
        expected: false,
        description: '域名不匹配'
    },
    
    // 多规则测试
    {
        url: 'https://example.com/page',
        patterns: ['https://other.com/*', 'https://example.com/*'],
        expected: true,
        description: '多规则中有一个匹配'
    },
    {
        url: 'https://test.com/page',
        patterns: ['https://example.com/*', 'https://other.com/*'],
        expected: false,
        description: '多规则都不匹配'
    },
    
    // 通配符测试
    {
        url: 'https://any-site.com/any-page',
        patterns: ['*'],
        expected: true,
        description: '全局通配符匹配'
    },
    
    // 空规则测试
    {
        url: 'https://example.com/page',
        patterns: [],
        expected: true,
        description: '空规则默认匹配所有'
    },
    {
        url: 'https://example.com/page',
        patterns: null,
        expected: true,
        description: 'null规则默认匹配所有'
    },
    
    // 微信商城相关测试
    {
        url: 'https://store.weixin.qq.com/shop/home',
        patterns: ['https://store.weixin.qq.com/*'],
        expected: true,
        description: '微信商城匹配'
    },
    {
        url: 'https://channels.weixin.qq.com/platform',
        patterns: ['https://channels.weixin.qq.com/*'],
        expected: true,
        description: '微信视频号匹配'
    }
];

console.log('🧪 开始URL匹配功能测试...\n');

let passCount = 0;
let totalCount = testCases.length;

testCases.forEach((testCase, index) => {
    const { url, patterns, expected, description } = testCase;
    
    try {
        const actual = matchUrl(url, patterns);
        const passed = actual === expected;
        
        if (passed) {
            passCount++;
            console.log(`✅ 测试 ${index + 1}: ${description}`);
        } else {
            console.log(`❌ 测试 ${index + 1}: ${description}`);
            console.log(`   URL: ${url}`);
            console.log(`   规则: ${JSON.stringify(patterns)}`);
            console.log(`   期望: ${expected}, 实际: ${actual}`);
        }
    } catch (error) {
        console.log(`💥 测试 ${index + 1}: ${description} - 发生错误`);
        console.log(`   错误: ${error.message}`);
    }
});

console.log(`\n📊 测试结果: ${passCount}/${totalCount} 通过`);

if (passCount === totalCount) {
    console.log('🎉 所有测试通过！URL匹配功能正常工作。');
} else {
    console.log('⚠️  部分测试失败，请检查URL匹配逻辑。');
}

// 性能测试
console.log('\n⚡ 性能测试...');
const performanceTestUrl = 'https://example.com/test/page';
const performanceTestPatterns = [
    'https://other1.com/*',
    'https://other2.com/*', 
    'https://other3.com/*',
    'https://example.com/*',  // 匹配的规则
    'https://other4.com/*'
];

const startTime = Date.now();
for (let i = 0; i < 10000; i++) {
    matchUrl(performanceTestUrl, performanceTestPatterns);
}
const endTime = Date.now();

console.log(`10000次匹配耗时: ${endTime - startTime}ms`);
console.log(`平均每次匹配耗时: ${(endTime - startTime) / 10000}ms`);

console.log('\n✨ 测试完成！');
