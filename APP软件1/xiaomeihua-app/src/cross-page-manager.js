/**
 * 跨页面执行管理器
 * 负责管理页面间的脚本执行、数据共享和自动页面打开功能
 */

const { ipcMain } = require('electron');
const path = require('path');
const fs = require('fs');

class CrossPageManager {
  constructor() {
    this.pageRegistry = new Map(); // 页面注册表
    this.sharedData = new Map(); // 跨页面共享数据
    this.pendingTasks = new Map(); // 待执行任务队列
    this.autoOpenQueue = []; // 自动打开页面队列
    this.scriptExecutionHistory = []; // 脚本执行历史
    
    this.setupIpcHandlers();
    console.log('🔄 跨页面执行管理器已初始化');
  }

  /**
   * 设置IPC处理器
   */
  setupIpcHandlers() {
    // 页面注册
    ipcMain.handle('register-page', (event, pageInfo) => {
      return this.registerPage(event.sender, pageInfo);
    });

    // 页面注销
    ipcMain.handle('unregister-page', (event, pageId) => {
      return this.unregisterPage(pageId);
    });

    // 跨页面执行脚本
    ipcMain.handle('execute-cross-page-script', (event, scriptData) => {
      return this.executeCrossPageScript(scriptData);
    });

    // 设置共享数据
    ipcMain.handle('set-shared-data', (event, key, value) => {
      return this.setSharedData(key, value);
    });

    // 获取共享数据
    ipcMain.handle('get-shared-data', (event, key) => {
      return this.getSharedData(key);
    });

    // 自动打开页面
    ipcMain.handle('auto-open-page', (event, pageConfig) => {
      return this.autoOpenPage(pageConfig);
    });

    // 获取页面列表
    ipcMain.handle('get-page-list', () => {
      return this.getPageList();
    });

    // 广播消息到所有页面
    ipcMain.handle('broadcast-message', (event, message) => {
      return this.broadcastMessage(message);
    });

    // 发送消息到指定页面
    ipcMain.handle('send-to-page', (event, pageId, message) => {
      return this.sendToPage(pageId, message);
    });
  }

  /**
   * 注册页面
   */
  registerPage(webContents, pageInfo) {
    const pageId = `page_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const pageData = {
      id: pageId,
      webContents: webContents,
      url: pageInfo.url || '',
      title: pageInfo.title || '',
      type: pageInfo.type || 'unknown',
      shopId: pageInfo.shopId || '',
      capabilities: pageInfo.capabilities || [],
      registeredAt: new Date(),
      lastActivity: new Date()
    };

    this.pageRegistry.set(pageId, pageData);
    
    // 监听页面关闭
    webContents.on('destroyed', () => {
      this.unregisterPage(pageId);
    });

    console.log(`📝 页面已注册: ${pageId} (${pageData.title})`);
    
    // 检查是否有待执行的任务
    this.checkPendingTasks(pageId);
    
    return { success: true, pageId: pageId };
  }

  /**
   * 注销页面
   */
  unregisterPage(pageId) {
    if (this.pageRegistry.has(pageId)) {
      const pageData = this.pageRegistry.get(pageId);
      this.pageRegistry.delete(pageId);
      console.log(`🗑️ 页面已注销: ${pageId} (${pageData.title})`);
      return { success: true };
    }
    return { success: false, error: 'Page not found' };
  }

  /**
   * 跨页面执行脚本
   */
  async executeCrossPageScript(scriptData) {
    const {
      script,
      targetPages = [],
      targetType = '',
      targetUrl = '',
      waitForPage = false,
      timeout = 30000,
      executeOrder = 'parallel' // 'parallel' 或 'sequential'
    } = scriptData;

    console.log(`🚀 开始跨页面脚本执行: 目标页面数 ${targetPages.length}`);

    // 查找目标页面
    const targetPageList = this.findTargetPages(targetPages, targetType, targetUrl);
    
    if (targetPageList.length === 0 && !waitForPage) {
      return {
        success: false,
        error: 'No target pages found',
        results: []
      };
    }

    // 如果需要等待页面，添加到待执行队列
    if (targetPageList.length === 0 && waitForPage) {
      const taskId = this.addPendingTask(scriptData);
      return {
        success: true,
        message: 'Script queued for execution when target page becomes available',
        taskId: taskId
      };
    }

    // 执行脚本
    const results = [];
    
    if (executeOrder === 'sequential') {
      // 顺序执行
      for (const page of targetPageList) {
        try {
          const result = await this.executeScriptOnPage(page, script);
          results.push({
            pageId: page.id,
            success: true,
            result: result
          });
        } catch (error) {
          results.push({
            pageId: page.id,
            success: false,
            error: error.message
          });
        }
      }
    } else {
      // 并行执行
      const promises = targetPageList.map(async (page) => {
        try {
          const result = await this.executeScriptOnPage(page, script);
          return {
            pageId: page.id,
            success: true,
            result: result
          };
        } catch (error) {
          return {
            pageId: page.id,
            success: false,
            error: error.message
          };
        }
      });

      const parallelResults = await Promise.allSettled(promises);
      results.push(...parallelResults.map(r => r.value || r.reason));
    }

    // 记录执行历史
    this.scriptExecutionHistory.push({
      timestamp: new Date(),
      script: script.substring(0, 100) + '...', // 只记录前100个字符
      targetPages: targetPageList.map(p => ({ id: p.id, title: p.title })),
      results: results
    });

    return {
      success: true,
      results: results,
      executedPages: targetPageList.length
    };
  }

  /**
   * 在指定页面执行脚本
   */
  async executeScriptOnPage(page, script) {
    if (!page.webContents || page.webContents.isDestroyed()) {
      throw new Error('Page webContents is destroyed');
    }

    // 更新页面活动时间
    page.lastActivity = new Date();

    // 执行脚本
    const result = await page.webContents.executeJavaScript(`
      (async function() {
        try {
          // 设置跨页面执行环境
          window.crossPageExecution = true;
          window.pageId = '${page.id}';
          
          // 执行用户脚本
          const result = await (async function() {
            ${script}
          })();
          
          return {
            success: true,
            result: result,
            pageInfo: {
              url: window.location.href,
              title: document.title,
              timestamp: new Date().toISOString()
            }
          };
        } catch (error) {
          return {
            success: false,
            error: error.message,
            stack: error.stack
          };
        }
      })();
    `);

    return result;
  }

  /**
   * 查找目标页面
   */
  findTargetPages(targetPages, targetType, targetUrl) {
    const pages = Array.from(this.pageRegistry.values());
    
    if (targetPages.length > 0) {
      // 按页面ID查找
      return pages.filter(page => targetPages.includes(page.id));
    }
    
    if (targetType) {
      // 按页面类型查找
      return pages.filter(page => page.type === targetType);
    }
    
    if (targetUrl) {
      // 按URL模式查找
      return pages.filter(page => page.url.includes(targetUrl));
    }
    
    return pages; // 返回所有页面
  }

  /**
   * 添加待执行任务
   */
  addPendingTask(scriptData) {
    const taskId = `task_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    this.pendingTasks.set(taskId, {
      id: taskId,
      scriptData: scriptData,
      createdAt: new Date()
    });
    
    // 设置超时清理
    setTimeout(() => {
      this.pendingTasks.delete(taskId);
    }, scriptData.timeout || 30000);
    
    return taskId;
  }

  /**
   * 检查待执行任务
   */
  checkPendingTasks(pageId) {
    const page = this.pageRegistry.get(pageId);
    if (!page) return;

    const tasksToExecute = [];
    
    for (const [taskId, task] of this.pendingTasks.entries()) {
      const { targetType, targetUrl } = task.scriptData;
      
      let shouldExecute = false;
      
      if (targetType && page.type === targetType) {
        shouldExecute = true;
      }
      
      if (targetUrl && page.url.includes(targetUrl)) {
        shouldExecute = true;
      }
      
      if (shouldExecute) {
        tasksToExecute.push({ taskId, task });
      }
    }

    // 执行匹配的任务
    tasksToExecute.forEach(({ taskId, task }) => {
      this.pendingTasks.delete(taskId);
      this.executeCrossPageScript(task.scriptData);
    });
  }

  /**
   * 自动打开页面
   */
  async autoOpenPage(pageConfig) {
    const {
      url,
      title = '自动打开页面',
      shopId = '',
      waitForLoad = true,
      executeAfterLoad = '',
      delay = 0
    } = pageConfig;

    console.log(`🔗 自动打开页面: ${url}`);

    // 延迟执行
    if (delay > 0) {
      await new Promise(resolve => setTimeout(resolve, delay));
    }

    // 添加到自动打开队列
    this.autoOpenQueue.push({
      url,
      title,
      shopId,
      waitForLoad,
      executeAfterLoad,
      timestamp: new Date()
    });

    // 通知主窗口创建新标签页
    const mainWindow = require('./main').getMainWindow();
    if (mainWindow && !mainWindow.isDestroyed()) {
      mainWindow.webContents.send('create-tab', {
        url: url,
        title: title,
        shopId: shopId,
        autoOpened: true
      });

      return { success: true, message: 'Page opening initiated' };
    }

    return { success: false, error: 'Main window not available' };
  }

  /**
   * 设置共享数据
   */
  setSharedData(key, value) {
    this.sharedData.set(key, {
      value: value,
      timestamp: new Date(),
      type: typeof value
    });
    
    // 广播数据更新事件
    this.broadcastMessage({
      type: 'shared-data-updated',
      key: key,
      value: value
    });
    
    return { success: true };
  }

  /**
   * 获取共享数据
   */
  getSharedData(key) {
    const data = this.sharedData.get(key);
    return data ? data.value : undefined;
  }

  /**
   * 获取页面列表
   */
  getPageList() {
    return Array.from(this.pageRegistry.values()).map(page => ({
      id: page.id,
      url: page.url,
      title: page.title,
      type: page.type,
      shopId: page.shopId,
      capabilities: page.capabilities,
      registeredAt: page.registeredAt,
      lastActivity: page.lastActivity
    }));
  }

  /**
   * 广播消息到所有页面
   */
  broadcastMessage(message) {
    let sentCount = 0;
    
    for (const page of this.pageRegistry.values()) {
      if (!page.webContents.isDestroyed()) {
        try {
          page.webContents.send('cross-page-message', message);
          sentCount++;
        } catch (error) {
          console.error(`发送消息到页面 ${page.id} 失败:`, error);
        }
      }
    }
    
    return { success: true, sentCount: sentCount };
  }

  /**
   * 发送消息到指定页面
   */
  sendToPage(pageId, message) {
    const page = this.pageRegistry.get(pageId);
    
    if (!page) {
      return { success: false, error: 'Page not found' };
    }
    
    if (page.webContents.isDestroyed()) {
      return { success: false, error: 'Page webContents is destroyed' };
    }
    
    try {
      page.webContents.send('cross-page-message', message);
      return { success: true };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  /**
   * 清理过期数据
   */
  cleanup() {
    const now = new Date();
    const maxAge = 24 * 60 * 60 * 1000; // 24小时

    // 清理执行历史
    this.scriptExecutionHistory = this.scriptExecutionHistory.filter(
      record => (now - record.timestamp) < maxAge
    );

    // 清理过期的待执行任务
    for (const [taskId, task] of this.pendingTasks.entries()) {
      if ((now - task.createdAt) > (task.scriptData.timeout || 30000)) {
        this.pendingTasks.delete(taskId);
      }
    }

    // 清理自动打开队列
    this.autoOpenQueue = this.autoOpenQueue.filter(
      item => (now - item.timestamp) < maxAge
    );
  }

  /**
   * 获取统计信息
   */
  getStats() {
    return {
      registeredPages: this.pageRegistry.size,
      sharedDataItems: this.sharedData.size,
      pendingTasks: this.pendingTasks.size,
      autoOpenQueue: this.autoOpenQueue.length,
      executionHistory: this.scriptExecutionHistory.length
    };
  }
}

// 创建全局实例
let crossPageManager = null;

function getCrossPageManager() {
  if (!crossPageManager) {
    crossPageManager = new CrossPageManager();
    
    // 定期清理
    setInterval(() => {
      crossPageManager.cleanup();
    }, 60 * 60 * 1000); // 每小时清理一次
  }
  return crossPageManager;
}

module.exports = {
  CrossPageManager,
  getCrossPageManager
};
