/**
 * 架构检测器模块
 * 用于检测和验证当前系统的架构信息
 */

const { app } = require('electron');
const os = require('os');

class ArchitectureDetector {
  constructor() {
    this.platformInfo = null;
    this.systemInfo = null;
    this.init();
  }

  /**
   * 初始化检测器
   */
  init() {
    this.detectPlatformInfo();
    this.detectSystemInfo();
  }

  /**
   * 检测平台信息（与app-updater.js保持一致）
   */
  detectPlatformInfo() {
    const platform = process.platform;
    const arch = process.arch;
    
    if (platform === 'win32') {
      this.platformInfo = {
        platform: 'windows',
        architecture: 'x64',
        apiUrl: 'https://xiaomeihuakefu.cn/api/app_update_windows.php',
        platformKey: 'windows'
      };
    } else if (platform === 'darwin') {
      if (arch === 'arm64') {
        // M系列芯片
        this.platformInfo = {
          platform: 'macos',
          architecture: 'm1',
          apiUrl: 'https://xiaomeihuakefu.cn/api/app_update_macos_m1.php',
          platformKey: 'macos_m1'
        };
      } else {
        // Intel芯片
        this.platformInfo = {
          platform: 'macos',
          architecture: 'intel', 
          apiUrl: 'https://xiaomeihuakefu.cn/api/app_update_macos_intel.php',
          platformKey: 'macos_intel'
        };
      }
    } else {
      this.platformInfo = {
        platform: 'unknown',
        architecture: 'unknown',
        apiUrl: null,
        platformKey: 'unknown'
      };
    }
  }

  /**
   * 检测系统信息
   */
  detectSystemInfo() {
    this.systemInfo = {
      platform: process.platform,
      arch: process.arch,
      osType: os.type(),
      osRelease: os.release(),
      osVersion: os.version ? os.version() : 'Unknown',
      cpus: os.cpus(),
      totalMemory: os.totalmem(),
      freeMemory: os.freemem(),
      hostname: os.hostname(),
      userInfo: os.userInfo()
    };
  }

  /**
   * 获取平台信息
   */
  getPlatformInfo() {
    return this.platformInfo;
  }

  /**
   * 获取系统信息
   */
  getSystemInfo() {
    return this.systemInfo;
  }

  /**
   * 获取架构描述
   */
  getArchitectureDescription() {
    const platform = this.platformInfo;
    
    if (platform.platform === 'macos') {
      if (platform.architecture === 'm1') {
        return 'Apple Silicon (M系列芯片)';
      } else if (platform.architecture === 'intel') {
        return 'Intel 处理器';
      }
    } else if (platform.platform === 'windows') {
      return 'Windows x64';
    }
    
    return '未知架构';
  }

  /**
   * 获取更新API描述
   */
  getUpdateApiDescription() {
    const platform = this.platformInfo;
    
    if (platform.platform === 'macos') {
      if (platform.architecture === 'm1') {
        return 'M芯片专用更新API';
      } else if (platform.architecture === 'intel') {
        return 'Intel芯片专用更新API';
      }
    } else if (platform.platform === 'windows') {
      return 'Windows通用更新API';
    }
    
    return '未知API';
  }

  /**
   * 检查架构兼容性
   */
  checkCompatibility() {
    const platform = this.platformInfo;
    const issues = [];
    const recommendations = [];

    // 检查是否支持更新
    if (!platform.apiUrl) {
      issues.push('当前平台不支持自动更新');
    }

    // macOS特定检查
    if (platform.platform === 'macos') {
      const osVersion = this.systemInfo.osRelease;
      const majorVersion = parseInt(osVersion.split('.')[0]);
      
      if (platform.architecture === 'm1') {
        if (majorVersion < 20) { // macOS 11.0 对应 Darwin 20.x
          issues.push('M芯片Mac需要macOS 11.0或更高版本');
        }
        recommendations.push('建议使用M芯片优化版本以获得最佳性能');
      } else if (platform.architecture === 'intel') {
        if (majorVersion >= 21) { // macOS 12.0+ 
          recommendations.push('您的Mac可能支持M芯片版本，建议检查是否有更好的兼容性');
        }
      }
    }

    return {
      isCompatible: issues.length === 0,
      issues: issues,
      recommendations: recommendations
    };
  }

  /**
   * 生成诊断报告
   */
  generateDiagnosticReport() {
    const platform = this.platformInfo;
    const system = this.systemInfo;
    const compatibility = this.checkCompatibility();
    
    return {
      timestamp: new Date().toISOString(),
      platform: {
        detected: platform,
        description: this.getArchitectureDescription(),
        apiDescription: this.getUpdateApiDescription()
      },
      system: {
        platform: system.platform,
        arch: system.arch,
        osType: system.osType,
        osRelease: system.osRelease,
        osVersion: system.osVersion,
        cpuCount: system.cpus.length,
        cpuModel: system.cpus[0]?.model || 'Unknown',
        totalMemoryGB: Math.round(system.totalMemory / 1024 / 1024 / 1024),
        hostname: system.hostname
      },
      compatibility: compatibility,
      updateInfo: {
        apiEndpoint: platform.apiUrl,
        platformKey: platform.platformKey,
        expectedDownloadType: platform.platform === 'macos' ? 'DMG' : 'EXE'
      }
    };
  }

  /**
   * 打印诊断信息到控制台
   */
  printDiagnostics() {
    const report = this.generateDiagnosticReport();
    
    console.log('🔍 === 架构检测诊断报告 ===');
    console.log(`时间: ${report.timestamp}`);
    console.log('');
    
    console.log('📱 平台信息:');
    console.log(`  检测结果: ${report.platform.description}`);
    console.log(`  平台: ${report.platform.detected.platform}`);
    console.log(`  架构: ${report.platform.detected.architecture}`);
    console.log(`  API端点: ${report.platform.detected.apiUrl}`);
    console.log(`  平台键: ${report.platform.detected.platformKey}`);
    console.log('');
    
    console.log('🖥️  系统信息:');
    console.log(`  操作系统: ${report.system.osType} ${report.system.osRelease}`);
    console.log(`  处理器: ${report.system.cpuModel} (${report.system.cpuCount}核)`);
    console.log(`  内存: ${report.system.totalMemoryGB}GB`);
    console.log(`  主机名: ${report.system.hostname}`);
    console.log('');
    
    console.log('✅ 兼容性检查:');
    console.log(`  兼容状态: ${report.compatibility.isCompatible ? '✅ 兼容' : '❌ 不兼容'}`);
    
    if (report.compatibility.issues.length > 0) {
      console.log('  问题:');
      report.compatibility.issues.forEach(issue => {
        console.log(`    ❌ ${issue}`);
      });
    }
    
    if (report.compatibility.recommendations.length > 0) {
      console.log('  建议:');
      report.compatibility.recommendations.forEach(rec => {
        console.log(`    💡 ${rec}`);
      });
    }
    
    console.log('');
    console.log('🔄 更新信息:');
    console.log(`  API描述: ${report.platform.apiDescription}`);
    console.log(`  下载类型: ${report.updateInfo.expectedDownloadType}`);
    
    return report;
  }
}

// 创建单例实例
const architectureDetector = new ArchitectureDetector();

module.exports = architectureDetector;
