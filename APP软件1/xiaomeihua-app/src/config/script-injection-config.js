/**
 * 脚本注入配置文件
 * 集中管理所有需要注入脚本的页面和标题
 */

// URL白名单 - 支持脚本注入的域名和页面
const SCRIPT_INJECTION_WHITELIST = [
  // 微信相关页面
  'store.weixin.qq.com',      // 微信小店
  'shop.weixin.qq.com',       // 微信商店
  'weixin.qq.com/shop',       // 微信商店
  'filehelper.weixin.qq.com', // AI智能上架
  'channels.weixin.qq.com',   // 视频号助手
  
  // 小梅花相关页面
  'ai-knowledge.html',        // AI知识库
  'xiaomeihuakefu.cn',        // 小梅花官网
  'localhost',                // 本地开发环境
  '127.0.0.1',               // 本地开发环境
  
  // 可以在这里添加更多需要支持脚本注入的域名
  // 'example.com',           // 示例：其他需要支持的网站
];

// 标题关键词白名单 - 支持脚本注入的页面标题关键词
const TITLE_INJECTION_WHITELIST = [
  // AI功能相关
  'AI智能客服', 
  '店铺客服',
  'AI智能上架', 
  '上架产品',
  'AI知识库',
  
  // 平台相关
  '视频号助手',
  '小梅花',
  
  // 可以在这里添加更多需要支持脚本注入的标题关键词
  // '客服助手',             // 示例：其他相关标题
];

// 导出配置
if (typeof module !== 'undefined' && module.exports) {
  // Node.js环境
  module.exports = {
    SCRIPT_INJECTION_WHITELIST,
    TITLE_INJECTION_WHITELIST
  };
} else {
  // 浏览器环境
  window.ScriptInjectionConfig = {
    SCRIPT_INJECTION_WHITELIST,
    TITLE_INJECTION_WHITELIST
  };
}

/**
 * 使用说明：
 * 
 * 1. 添加新的URL支持：
 *    在 SCRIPT_INJECTION_WHITELIST 数组中添加新的域名或页面路径
 * 
 * 2. 添加新的标题支持：
 *    在 TITLE_INJECTION_WHITELIST 数组中添加新的标题关键词
 * 
 * 3. 检查函数示例：
 *    const shouldInject = SCRIPT_INJECTION_WHITELIST.some(domain => url.includes(domain));
 *    const shouldInjectByTitle = TITLE_INJECTION_WHITELIST.some(keyword => title.includes(keyword));
 * 
 * 4. 安全注意事项：
 *    - 只添加确实需要脚本注入的页面
 *    - 避免使用过于宽泛的匹配规则
 *    - 定期审查白名单，移除不再需要的条目
 */
