/**
 * Windows平台专用登录状态管理器
 * 确保100%可靠的登录保持功能
 */

const { app } = require('electron');
const path = require('path');
const fs = require('fs');
const os = require('os');

class WindowsLoginManager {
  constructor() {
    this.isWindows = process.platform === 'win32';
    this.storagePaths = [];
    this.backupPaths = [];
    this.initialized = false;
  }

  /**
   * 初始化Windows登录管理器
   */
  async initialize() {
    if (!this.isWindows || this.initialized) return;

    console.log('🔧 初始化Windows登录状态管理器...');

    try {
      // 设置多个存储路径确保可靠性
      await this.setupStoragePaths();
      
      // 创建所有必要的目录
      await this.createDirectories();
      
      // 设置目录权限
      await this.setupPermissions();
      
      // 测试写入权限
      await this.testWritePermissions();
      
      this.initialized = true;
      console.log('✅ Windows登录状态管理器初始化完成');
    } catch (error) {
      console.error('❌ Windows登录状态管理器初始化失败:', error);
      throw error;
    }
  }

  /**
   * 设置多个存储路径
   */
  async setupStoragePaths() {
    const userDataPath = app.getPath('userData');
    const appDataPath = app.getPath('appData');
    const localAppDataPath = path.join(os.homedir(), 'AppData', 'Local');
    const programDataPath = process.env.PROGRAMDATA || 'C:\\ProgramData';

    // 主存储路径
    this.storagePaths = [
      path.join(userDataPath, 'persistent-cookies'),
      path.join(appDataPath, '小梅花AI智能客服', 'login-data'),
      path.join(localAppDataPath, '小梅花AI智能客服', 'session-data'),
      path.join(programDataPath, '小梅花AI智能客服', 'backup-data')
    ];

    // 获取应用程序安装目录
    const appInstallPath = this.getAppInstallPath();

    // 备份路径 - 优化：移除桌面备份，改为安装目录备份
    this.backupPaths = [
      path.join(appInstallPath, '.xiaomeihua-backup'),  // 安装目录下的备份文件夹
      path.join(os.homedir(), 'Documents', '.xiaomeihua-backup'),
      path.join(os.tmpdir(), 'xiaomeihua-temp-backup'),
      path.join('C:\\', 'xiaomeihua-emergency-backup')
    ];

    console.log('📁 Windows存储路径设置完成:', this.storagePaths.length, '个主路径,', this.backupPaths.length, '个备份路径');
  }

  /**
   * 获取应用程序安装目录
   */
  getAppInstallPath() {
    try {
      // 尝试多种方式获取安装目录
      const isDev = !app.isPackaged;

      if (isDev) {
        // 开发环境：使用项目根目录
        return path.dirname(app.getAppPath());
      } else {
        // 生产环境：尝试获取实际安装目录
        const possiblePaths = [
          path.dirname(process.execPath),  // 可执行文件所在目录
          path.dirname(app.getAppPath()),  // 应用程序路径的父目录
          process.env.PORTABLE_EXECUTABLE_DIR,  // 便携版目录
          'C:\\Program Files\\小梅花AI智能客服',  // 默认安装目录
          'D:\\小梅花AI智能客服'  // D盘安装目录
        ];

        // 查找第一个存在的目录
        for (const installPath of possiblePaths) {
          if (installPath && fs.existsSync(installPath)) {
            console.log(`✅ 找到安装目录: ${installPath}`);
            return installPath;
          }
        }

        // 如果都找不到，使用可执行文件目录
        const fallbackPath = path.dirname(process.execPath);
        console.log(`⚠️ 使用备用安装目录: ${fallbackPath}`);
        return fallbackPath;
      }
    } catch (error) {
      console.warn('⚠️ 获取安装目录失败，使用默认路径:', error.message);
      return 'C:\\Program Files\\小梅花AI智能客服';
    }
  }

  /**
   * 创建所有必要的目录
   */
  async createDirectories() {
    const allPaths = [...this.storagePaths, ...this.backupPaths];
    
    for (const dirPath of allPaths) {
      try {
        if (!fs.existsSync(dirPath)) {
          fs.mkdirSync(dirPath, { recursive: true });
          console.log(`✅ 创建目录: ${dirPath}`);
        }
      } catch (error) {
        console.warn(`⚠️ 创建目录失败: ${dirPath}`, error.message);
      }
    }
  }

  /**
   * 设置目录权限
   */
  async setupPermissions() {
    const { execSync } = require('child_process');
    const username = os.userInfo().username;
    const allPaths = [...this.storagePaths, ...this.backupPaths];

    for (const dirPath of allPaths) {
      try {
        if (fs.existsSync(dirPath)) {
          // 给当前用户完全控制权限
          execSync(`icacls "${dirPath}" /grant "${username}:(OI)(CI)F" /T`, { stdio: 'ignore' });
          // 给所有用户读写权限
          execSync(`icacls "${dirPath}" /grant "Everyone:(OI)(CI)M" /T`, { stdio: 'ignore' });
          console.log(`✅ 权限设置完成: ${dirPath}`);
        }
      } catch (error) {
        console.warn(`⚠️ 权限设置失败: ${dirPath}`, error.message);
      }
    }
  }

  /**
   * 测试写入权限
   */
  async testWritePermissions() {
    const testResults = [];
    
    for (const dirPath of this.storagePaths) {
      try {
        const testFile = path.join(dirPath, 'write-test.tmp');
        fs.writeFileSync(testFile, 'test-' + Date.now());
        fs.unlinkSync(testFile);
        testResults.push({ path: dirPath, writable: true });
        console.log(`✅ 写入测试通过: ${dirPath}`);
      } catch (error) {
        testResults.push({ path: dirPath, writable: false, error: error.message });
        console.warn(`❌ 写入测试失败: ${dirPath}`, error.message);
      }
    }

    const writablePaths = testResults.filter(r => r.writable).length;
    if (writablePaths === 0) {
      throw new Error('所有存储路径都无法写入，登录保持功能将无法正常工作');
    }

    console.log(`✅ 写入权限测试完成: ${writablePaths}/${this.storagePaths.length} 个路径可写入`);
  }

  /**
   * 保存登录状态到多个位置
   */
  async saveLoginState(loginData) {
    if (!this.isWindows || !this.initialized) return false;

    console.log('💾 Windows平台保存登录状态...');
    
    const stateData = {
      timestamp: Date.now(),
      platform: 'win32',
      version: app.getVersion(),
      hasLogin: true,
      loginData: loginData,
      saveTime: new Date().toISOString(),
      paths: this.storagePaths,
      backupPaths: this.backupPaths
    };

    let successCount = 0;
    const errors = [];

    // 保存到所有主路径
    for (const storagePath of this.storagePaths) {
      try {
        const loginStateFile = path.join(storagePath, 'login-state.json');
        const cookiesFile = path.join(storagePath, 'cookies.json');
        
        // 保存登录状态
        fs.writeFileSync(loginStateFile, JSON.stringify(stateData, null, 2));
        
        // 保存Cookie数据
        if (loginData.cookies) {
          fs.writeFileSync(cookiesFile, JSON.stringify(loginData.cookies, null, 2));
        }
        
        // 强制同步到磁盘
        const fd = fs.openSync(loginStateFile, 'r');
        fs.fsyncSync(fd);
        fs.closeSync(fd);
        
        successCount++;
        console.log(`✅ 登录状态保存成功: ${storagePath}`);
      } catch (error) {
        errors.push({ path: storagePath, error: error.message });
        console.error(`❌ 登录状态保存失败: ${storagePath}`, error.message);
      }
    }

    // 保存到备份路径
    for (const backupPath of this.backupPaths) {
      try {
        const backupFile = path.join(backupPath, 'login-backup.json');
        fs.writeFileSync(backupFile, JSON.stringify(stateData, null, 2));
        console.log(`✅ 备份保存成功: ${backupPath}`);
      } catch (error) {
        console.warn(`⚠️ 备份保存失败: ${backupPath}`, error.message);
      }
    }

    console.log(`💾 Windows登录状态保存完成: ${successCount}/${this.storagePaths.length} 个主路径成功`);
    return successCount > 0;
  }

  /**
   * 从多个位置恢复登录状态
   */
  async restoreLoginState() {
    if (!this.isWindows || !this.initialized) return null;

    console.log('🔄 Windows平台恢复登录状态...');

    // 尝试从主路径恢复
    for (const storagePath of this.storagePaths) {
      try {
        const loginStateFile = path.join(storagePath, 'login-state.json');
        const cookiesFile = path.join(storagePath, 'cookies.json');
        
        if (fs.existsSync(loginStateFile)) {
          const stateData = JSON.parse(fs.readFileSync(loginStateFile, 'utf-8'));
          
          // 检查状态有效性（24小时内）
          const now = Date.now();
          const stateAge = now - stateData.timestamp;
          const maxAge = 24 * 60 * 60 * 1000; // 24小时
          
          if (stateAge < maxAge && stateData.hasLogin) {
            // 尝试读取Cookie数据
            if (fs.existsSync(cookiesFile)) {
              try {
                stateData.cookies = JSON.parse(fs.readFileSync(cookiesFile, 'utf-8'));
              } catch (cookieError) {
                console.warn('Cookie文件读取失败:', cookieError.message);
              }
            }
            
            console.log(`✅ 登录状态恢复成功: ${storagePath}`);
            return stateData;
          } else {
            console.log(`⚠️ 登录状态已过期: ${storagePath}`);
          }
        }
      } catch (error) {
        console.warn(`⚠️ 登录状态恢复失败: ${storagePath}`, error.message);
      }
    }

    // 尝试从备份路径恢复
    for (const backupPath of this.backupPaths) {
      try {
        const backupFile = path.join(backupPath, 'login-backup.json');
        
        if (fs.existsSync(backupFile)) {
          const stateData = JSON.parse(fs.readFileSync(backupFile, 'utf-8'));
          
          const now = Date.now();
          const stateAge = now - stateData.timestamp;
          const maxAge = 24 * 60 * 60 * 1000;
          
          if (stateAge < maxAge && stateData.hasLogin) {
            console.log(`✅ 从备份恢复登录状态: ${backupPath}`);
            return stateData;
          }
        }
      } catch (error) {
        console.warn(`⚠️ 备份恢复失败: ${backupPath}`, error.message);
      }
    }

    console.log('❌ 未找到有效的登录状态');
    return null;
  }

  /**
   * 清除所有登录状态
   */
  async clearLoginState() {
    if (!this.isWindows) return;

    console.log('🗑️ Windows平台清除登录状态...');

    const allPaths = [...this.storagePaths, ...this.backupPaths];
    
    for (const dirPath of allPaths) {
      try {
        const files = ['login-state.json', 'cookies.json', 'login-backup.json'];
        
        for (const file of files) {
          const filePath = path.join(dirPath, file);
          if (fs.existsSync(filePath)) {
            fs.unlinkSync(filePath);
            console.log(`✅ 删除文件: ${filePath}`);
          }
        }
      } catch (error) {
        console.warn(`⚠️ 清除失败: ${dirPath}`, error.message);
      }
    }

    console.log('✅ Windows登录状态清除完成');
  }

  /**
   * 获取登录状态统计信息
   */
  getStatusInfo() {
    if (!this.isWindows) return null;

    const info = {
      platform: 'win32',
      initialized: this.initialized,
      storagePaths: this.storagePaths.length,
      backupPaths: this.backupPaths.length,
      availablePaths: 0,
      lastCheck: new Date().toISOString()
    };

    // 检查可用路径
    for (const storagePath of this.storagePaths) {
      try {
        if (fs.existsSync(storagePath)) {
          const testFile = path.join(storagePath, 'status-test.tmp');
          fs.writeFileSync(testFile, 'test');
          fs.unlinkSync(testFile);
          info.availablePaths++;
        }
      } catch (error) {
        // 路径不可用
      }
    }

    return info;
  }
}

module.exports = WindowsLoginManager;
