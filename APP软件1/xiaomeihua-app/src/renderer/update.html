<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>小梅花AI智能客服<版本升级></title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
            background: transparent; /* 完全透明背景 */
            color: #333;
            overflow: hidden;
            height: 100vh; /* 视口高度 */
            width: 100vw; /* 视口宽度 */
            display: flex;
            flex-direction: column;
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            position: relative;
        }

        /* Windows平台专用：完全透明背景 */
        body.windows-platform {
            background: transparent !important; /* 完全透明背景 */
            width: 100vw !important; /* 视口宽度 */
            height: 100vh !important; /* 视口高度 */
            min-width: 500px; /* 最小宽度 */
            min-height: 480px; /* 最小高度 */
            max-width: none; /* 移除最大宽度限制 */
            max-height: none; /* 移除最大高度限制 */
            margin: 0 !important; /* 移除边距 */
            padding: 0 !important; /* 移除内边距 */
            overflow: hidden; /* 防止滚动条 */
        }

        .update-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            padding: 30px 20px 20px 20px; /* 统一的内边距 */
            background: white;
            margin: 0;
            width: 100%; /* 填满整个宽度 */
            height: 100%; /* 填满整个高度 */
            min-height: 100vh; /* 最小高度为视口高度 */
            justify-content: space-between;
            border-radius: 10px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            transition: opacity 0.2s ease-in-out;
            position: relative;
            overflow: hidden; /* 防止内容溢出 */
            box-sizing: border-box; /* 包含padding在内的尺寸计算 */
        }

        .update-main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .app-logo {
            width: 160px; /* 继续放大logo */
            height: 160px; /* 继续放大logo */
            margin: 25px auto 25px; /* 增加上下边距 */
            background: url('../assets/logo.png') center/contain no-repeat;
            border-radius: 32px; /* 相应调整圆角 */
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.25); /* 增强阴影 */
        }

        .update-title {
            text-align: center;
            font-size: 36px; /* 继续放大标题字体 */
            font-weight: 700; /* 增加字重 */
            color: #2c3e50;
            margin-bottom: 30px; /* 增加底部边距 */
            letter-spacing: 1.2px; /* 增加字间距 */
        }



        .divider {
            height: 1px;
            background: linear-gradient(90deg, transparent, #e0e6ed, transparent);
            margin-bottom: 15px; /* 减少底部边距 */
        }

        .update-content {
            flex: 1;
            background: #f8f9fa;
            border-radius: 18px; /* 更大圆角 */
            padding: 50px; /* 继续增加内边距 */
            margin-bottom: 50px; /* 继续增加底部边距 */
            font-size: 24px; /* 继续放大字体 */
            line-height: 2.2; /* 增加行高 */
            color: #555;
            overflow-y: auto;
            max-height: 1600px; /* 向下扩大2个大小，从800px增加到1600px */
            min-height: 880px; /* 最小高度也扩大2个大小，从440px增加到880px */
            border: 3px solid #e9ecef; /* 更粗边框 */
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.12); /* 增强阴影 */
        }

        .update-content h3 {
            color: #2c3e50;
            margin-bottom: 25px;
            font-size: 30px; /* 比更新说明文字(24px)稍大一点 */
            font-weight: 700;
        }

        /* macOS平台：覆盖update-content h3的样式 */
        .macos-platform .update-content h3 {
            font-size: 18px !important; /* 调整为18px */
            font-weight: 600 !important;
            margin-bottom: 0px !important;
            margin-top: 0px !important;
        }

        .update-content ul {
            padding-left: 20px;
        }

        .update-content li {
            margin-bottom: 5px;
        }

        .update-actions {
            display: flex;
            justify-content: center;
            gap: 15px;
        }

        /* 居中单个按钮的样式 */
        .centered-btn {
            max-width: 400px; /* 继续增加最大宽度 */
            width: 400px; /* 继续增加宽度 */
        }

        .update-btn {
            flex: 1;
            height: 80px; /* 继续增加按钮高度 */
            border: none;
            border-radius: 18px; /* 更大圆角 */
            font-size: 26px; /* 继续放大字体 */
            font-weight: 700; /* 更粗字重 */
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px; /* 增加间距 */
            min-width: 280px; /* 增加最小宽度 */
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.18); /* 增强阴影 */
        }

        .primary-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .primary-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.3);
        }

        .secondary-btn {
            background: #e9ecef;
            color: #6c757d;
        }

        .secondary-btn:hover {
            background: #dee2e6;
        }

        .progress-container {
            display: none;
            margin-top: 25px; /* 增加顶部边距 */
            margin-bottom: 25px; /* 增加底部边距 */
        }

        .progress-bar {
            width: 100%;
            height: 12px; /* 增加进度条高度 */
            background: #e9ecef;
            border-radius: 6px; /* 增加圆角 */
            overflow: hidden;
            margin-bottom: 15px; /* 增加底部边距 */
            border: 1px solid #dee2e6; /* 添加边框 */
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 3px;
            transition: width 0.3s ease;
            width: 0%;
        }

        .progress-text {
            text-align: center;
            font-size: 20px; /* 继续放大进度文本 */
            color: #6c757d;
            margin-bottom: 12px; /* 增加底部边距 */
            font-weight: 600; /* 增加字重 */
        }

        .status-text {
            text-align: center;
            margin-top: 20px; /* 增加顶部边距 */
            margin-bottom: 20px; /* 增加底部边距 */
            font-size: 26px; /* 与立即更新文字一样大小 */
            color: #667eea;
            font-weight: 700; /* 增加字重 */
        }

        .hidden {
            display: none !important;
        }

        .loading-spinner {
            width: 20px;
            height: 20px;
            border: 2px solid transparent;
            border-top: 2px solid currentColor;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .error-message {
            background: #f8d7da;
            color: #721c24;
            padding: 20px; /* 增加内边距 */
            border-radius: 12px; /* 增加圆角 */
            margin-top: 20px; /* 增加顶部边距 */
            margin-bottom: 20px; /* 增加底部边距 */
            border: 2px solid #f5c6cb; /* 增加边框粗细 */
            font-size: 26px; /* 与立即更新文字大小一致 */
            font-weight: 700; /* 增加字重 */
            text-align: center;
        }

        .success-message {
            background: #d4edda;
            color: #155724;
            padding: 20px; /* 增加内边距 */
            border-radius: 12px; /* 增加圆角 */
            margin-top: 20px; /* 增加顶部边距 */
            margin-bottom: 20px; /* 增加底部边距 */
            border: 2px solid #c3e6cb; /* 增加边框粗细 */
            font-size: 26px; /* 与立即更新文字大小一致 */
            font-weight: 700; /* 增加字重 */
            text-align: center;
        }

        /* 滚动条样式 */
        .update-content::-webkit-scrollbar {
            width: 6px;
        }

        .update-content::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 3px;
        }

        .update-content::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 3px;
        }

        .update-content::-webkit-scrollbar-thumb:hover {
            background: #a1a1a1;
        }

        /* 更新描述样式 */
        .update-description {
            line-height: 1.8;
            color: #333;
            font-size: 30px; /* 与版本号大小一致 */
            font-weight: 600; /* 增加字重 */
        }

        .update-description h1,
        .update-description h2,
        .update-description h3,
        .update-description h4,
        .update-description h5,
        .update-description h6 {
            margin: 10px 0 8px 0;
            color: #2c3e50;
        }

        .update-description p {
            margin: 12px 0;
            font-size: 30px; /* 与版本号大小一致 */
            font-weight: 600; /* 增加字重 */
        }

        .update-description ul,
        .update-description ol {
            margin: 8px 0;
            padding-left: 20px;
        }

        .update-description li {
            margin: 4px 0;
        }

        .update-description strong {
            color: #2c3e50;
            font-weight: 600;
        }

        .update-description em {
            font-style: italic;
            color: #555;
        }

        /* Windows平台自定义关闭按钮样式 - 更大尺寸 */
        .windows-close-btn {
            position: absolute;
            top: 15px;
            right: 15px;
            width: 40px; /* 继续放大关闭按钮 */
            height: 40px; /* 继续放大关闭按钮 */
            background: #e81123; /* Windows红色 */
            border: none;
            border-radius: 6px; /* 添加轻微圆角 */
            cursor: pointer;
            display: none; /* 默认隐藏，通过JavaScript控制显示 */
            align-items: center;
            justify-content: center;
            font-size: 20px; /* 更大的字体 */
            font-weight: bold;
            color: #fff;
            transition: all 0.2s ease;
            z-index: 1000;
            -webkit-app-region: no-drag;
            font-family: 'Segoe UI Symbol', sans-serif; /* Windows字体 */
            box-shadow: 0 2px 8px rgba(232, 17, 35, 0.3); /* 添加阴影 */
        }

        .windows-close-btn:hover {
            background: #f1707a; /* Windows悬停颜色 */
            transform: none; /* 移除缩放效果 */
        }

        .windows-close-btn:active {
            background: #c50e1f; /* Windows按下颜色 */
            transform: none; /* 移除缩放效果 */
        }

        /* macOS平台专用样式：优化600x480窗口布局 */
        .update-container.macos-platform {
            padding: 20px 25px 20px 25px; /* 适中的内边距 */
            background: white !important;
            border-radius: 10px !important;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1) !important;
            border: none !important;
            width: 100vw !important;
            height: 100vh !important;
            min-width: 500px; /* DMG版本最小宽度 */
            min-height: 480px; /* DMG版本最小高度 */
            max-width: none;
            max-height: none;
            margin: 0 !important;
            position: absolute;
            top: 0 !important;
            left: 0 !important;
        }

        /* macOS平台：logo尺寸优化 */
        .macos-platform .app-logo {
            width: 80px; /* 缩小logo */
            height: 80px;
            margin: 15px auto 15px; /* 减少边距 */
            border-radius: 16px;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
        }

        /* macOS平台：标题优化 */
        .macos-platform .update-title {
            font-size: 24px; /* 适中的标题字体 */
            font-weight: 600;
            margin-bottom: 0px; /* 移除底部边距 */
            letter-spacing: 0.5px;
        }

        /* macOS平台：内容区域优化 */
        .macos-platform .update-content {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 20px; /* 适中内边距 */
            margin-bottom: 15px; /* 减少底部边距 */
            margin-top: 0px; /* 移除顶部边距，紧贴标题 */
            font-size: 16px; /* 适中字体大小 */
            line-height: 1.6; /* 适中行高 */
            color: #555;
            overflow-y: auto;
            max-height: 200px; /* 适合500x480窗口的内容高度 */
            min-height: 120px; /* 最小高度 */
            border: 2px solid #e9ecef;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
        }

        /* macOS平台：更新描述样式优化 */
        .macos-platform .update-description {
            font-size: 16px; /* 适中字体大小 */
            font-weight: 400; /* 正常字重 */
            line-height: 1.6;
        }

        .macos-platform .update-description p {
            font-size: 16px; /* 适中字体大小 */
            font-weight: 400; /* 正常字重 */
            margin: 3px 0; /* 减少段落间距 */
        }

        .macos-platform .update-description h3 {
            font-size: 18px !important; /* 调整为18px */
            font-weight: 600 !important;
            margin-bottom: 0px !important; /* 移除底部边距 */
            margin-top: 0px !important; /* 移除顶部边距 */
        }

        /* macOS平台：按钮样式优化 */
        .macos-platform .update-btn {
            height: 50px; /* 适中按钮高度 */
            font-size: 16px; /* 适中字体 */
            font-weight: 600;
            border-radius: 12px;
            min-width: 200px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
        }

        .macos-platform .centered-btn {
            max-width: 300px;
            width: 300px;
        }

        /* Windows平台专用样式：直接作为主弹窗 */
        .update-container.windows-platform {
            padding: 40px 30px 30px 30px; /* 增加内边距，给元素更多空间 */
            background: white !important; /* 白色背景 */
            border-radius: 12px !important; /* 添加圆角 */
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15) !important; /* 添加阴影 */
            border: none !important; /* 移除边框 */
            width: 100vw !important; /* 完全填满视口宽度 */
            height: 100vh !important; /* 完全填满视口高度 */
            min-width: 500px; /* 最小宽度 */
            min-height: 480px; /* 最小高度 */
            max-width: none; /* 移除最大宽度限制 */
            max-height: none; /* 移除最大高度限制 */
            margin: 0 !important; /* 移除所有边距 */
            position: absolute; /* 绝对定位 */
            top: 0 !important; /* 顶部对齐 */
            left: 0 !important; /* 左侧对齐 */
        }

        /* Windows平台：内容区域样式与macOS完全一致 */
        .windows-platform .update-content {
            background: #f8f9fa; /* 与macOS一致的背景色 */
            border-radius: 12px; /* 适中圆角 */
            padding: 25px; /* 适中内边距 */
            margin-bottom: 25px; /* 适中底部边距 */
            font-size: 18px; /* 适中字体大小 */
            line-height: 1.8; /* 适中行高 */
            color: #555;
            overflow-y: auto;
            max-height: 533px; /* 缩小三分之二，从1600px缩小到533px */
            min-height: 293px; /* 缩小三分之二，从880px缩小到293px */
            border: 2px solid #e9ecef; /* 适中边框 */
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1); /* 适中阴影 */
        }

        /* Windows平台：按钮样式与macOS完全一致 */
        .windows-platform .update-btn {
            /* 保持与macOS一致的按钮样式 */
        }

        .windows-platform .primary-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        /* 拖拽功能样式 */
        .update-container {
            -webkit-app-region: drag;
        }

        /* 禁用所有子元素的拖拽，然后单独启用需要拖拽的区域 */
        .update-container * {
            -webkit-app-region: no-drag;
        }

        /* 启用拖拽的区域 */
        .update-main-content,
        .update-title,
        .update-content,
        .divider {
            -webkit-app-region: drag;
        }

        /* 确保按钮和交互元素不可拖拽 */
        .update-btn,
        .progress-container,
        .status-text,
        .error-message,
        .success-message {
            -webkit-app-region: no-drag;
        }

        /* 防止鼠标在安装过程中变成彩色圆球 */
        .installing {
            cursor: default !important;
        }

        .installing * {
            cursor: default !important;
        }

        /* 安装过程中的特殊样式 */
        .installing .progress-container {
            cursor: default !important;
        }

        .installing .progress-text {
            cursor: default !important;
        }


    </style>
</head>
<body>
    <div class="update-container" id="updateContainer" style="opacity: 0; visibility: hidden;">
        <!-- Windows平台自定义关闭按钮 -->
        <button class="windows-close-btn" id="windowsCloseBtn" onclick="closeUpdateWindow()">✕</button>

        <div class="update-main-content">
            <div class="app-logo"></div>
            <div class="update-title" id="updateTitle">新版本</div>

            <div class="divider"></div>

            <div class="update-content" id="updateContent">
                <!-- 内容将通过JavaScript动态加载 -->
            </div>
        </div>
        
        <div class="update-actions" id="updateActions">
            <button class="update-btn primary-btn centered-btn" onclick="startUpdate()" id="updateBtn">
                立即更新
            </button>
        </div>

        <div class="progress-container" id="progressContainer">
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            <div class="progress-text" id="progressText">准备下载...</div>
        </div>

        <div class="status-text hidden" id="statusText"></div>
        <div class="error-message hidden" id="errorMessage"></div>
        <div class="success-message hidden" id="successMessage"></div>
    </div>

    <script>
        let updateInfo = null;
        let isDownloading = false;
        let autoUpdateTimer = null;
        let countdownTimer = null;
        let countdown = 5;

        // 平台检测和UI初始化
        function initializePlatformUI() {
            // 检测平台
            const platform = window.updateAPI.getPlatform();
            console.log('检测到平台:', platform);

            const container = document.getElementById('updateContainer');
            const windowsCloseBtn = document.getElementById('windowsCloseBtn');

            if (platform === 'windows') {
                // Windows平台：显示自定义关闭按钮，保持与macOS一致的样式
                container.classList.add('windows-platform');
                container.classList.remove('macos-platform');
                document.body.classList.add('windows-platform');
                document.body.classList.remove('macos-platform');
                windowsCloseBtn.style.display = 'flex';

                // Windows平台强制设置尺寸
                enforceWindowSize();

                console.log('Windows平台：显示自定义关闭按钮，保持与macOS一致的样式');
            } else if (platform === 'macos') {
                // macOS平台：使用原生交通灯按钮，应用专门的样式
                container.classList.add('macos-platform');
                container.classList.remove('windows-platform');
                document.body.classList.add('macos-platform');
                document.body.classList.remove('windows-platform');
                windowsCloseBtn.style.display = 'none';
                console.log('macOS平台：使用原生交通灯按钮，应用600x480优化布局');
            } else {
                // 其他平台：使用标准窗口框架
                container.classList.remove('windows-platform');
                container.classList.remove('macos-platform');
                document.body.classList.remove('windows-platform');
                document.body.classList.remove('macos-platform');
                windowsCloseBtn.style.display = 'none';
                console.log('其他平台：使用标准窗口框架');
            }
        }

        // 关闭更新窗口函数
        function closeUpdateWindow() {
            console.log('用户点击关闭按钮');
            window.updateAPI.closeUpdateWindow();
        }

        // 禁用所有可能影响窗口大小的快捷键
        document.addEventListener('keydown', function(event) {
            // 禁用常见的快捷键组合
            if (event.ctrlKey || event.metaKey || event.altKey) {
                event.preventDefault();
                event.stopPropagation();
                return false;
            }

            // 禁用特定按键
            const blockedKeys = ['F11', 'F12', 'Escape', 'F1', 'F2', 'F3', 'F4', 'F5', 'F6', 'F7', 'F8', 'F9', 'F10'];
            if (blockedKeys.includes(event.key)) {
                event.preventDefault();
                event.stopPropagation();
                return false;
            }
        });

        // 禁用右键菜单
        document.addEventListener('contextmenu', function(event) {
            event.preventDefault();
            return false;
        });

        // 确保窗口尺寸固定
        function enforceWindowSize() {
            // 通过CSS确保内容区域固定
            document.body.style.width = '100vw';
            document.body.style.height = '100vh';
            document.body.style.overflow = 'hidden';
            document.body.style.margin = '0';
            document.body.style.padding = '0';
            document.body.style.background = 'transparent';

            // 确保容器直接作为主弹窗
            const container = document.getElementById('updateContainer');
            const platform = window.updateAPI.getPlatform();

            if (container) {
                container.style.width = '100vw';
                container.style.height = '100vh';
                container.style.maxWidth = 'none';
                container.style.maxHeight = 'none';
                container.style.position = 'absolute';
                container.style.top = '0';
                container.style.left = '0';
                container.style.margin = '0';
                container.style.background = 'white';
                container.style.border = 'none';

                // 根据平台设置不同的最小尺寸
                if (platform === 'macos') {
                    container.style.minWidth = '500px';
                    container.style.minHeight = '480px';
                    container.style.borderRadius = '10px';
                    container.style.boxShadow = '0 4px 20px rgba(0, 0, 0, 0.1)';
                } else {
                    container.style.minWidth = '500px';
                    container.style.minHeight = '480px';
                    container.style.borderRadius = '12px';
                    container.style.boxShadow = '0 8px 32px rgba(0, 0, 0, 0.15)';
                }
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializePlatformUI();
            enforceWindowSize();

            // 定期检查并强制窗口尺寸
            setInterval(enforceWindowSize, 1000);
        });

        // 监听更新信息
        window.updateAPI.onUpdateInfo((data) => {
            console.log('收到更新信息:', data);
            updateInfo = data;
            displayUpdateInfo(data);
        });

        // 监听下载进度
        window.updateAPI.onDownloadProgress((progress) => {
            console.log('下载进度:', progress);

            // 使用自定义状态消息或默认消息（只显示百分比）
            let statusMessage;
            if (progress.status) {
                statusMessage = progress.status;
            } else {
                statusMessage = `更新中 ${progress.percent}%`;
            }

            updateProgress(progress.percent, statusMessage);
        });

        // 监听下载开始
        window.updateAPI.onStartDownload(() => {
            console.log('开始下载');
            showProgress();
            updateProgress(0, '更新中 0%');
        });

        // 监听下载完成
        window.updateAPI.onDownloadComplete(() => {
            console.log('下载完成');
            updateProgress(100, '更新中 100%');
        });

        // 监听安装开始
        window.updateAPI.onStartInstall(() => {
            console.log('开始安装');
            updateProgress(100, '更新中 100%');

            // 添加安装状态样式，防止鼠标变成彩色圆球
            document.body.classList.add('installing');
            document.getElementById('updateContainer').classList.add('installing');
        });

        // 监听安装完成
        window.updateAPI.onInstallComplete(() => {
            console.log('安装完成');
            updateProgress(100, '更新中 100%');

            // 移除安装状态样式
            document.body.classList.remove('installing');
            document.getElementById('updateContainer').classList.remove('installing');

            // 不需要手动调用重启，app-updater.js会自动处理
        });

        // 监听下载错误
        window.updateAPI.onDownloadError((error) => {
            console.error('下载错误:', error);
            showError(`更新失败：${error}`);
            hideProgress();
            resetActions();
        });

        function displayUpdateInfo(info) {
            if (info) {
                // 设置更新标题
                document.getElementById('updateTitle').textContent = '新版本';
                
                // 直接显示更新内容
                document.getElementById('updateContent').innerHTML = formatUpdateContent(info);
                
                // 显示容器，避免闪烁
                const container = document.getElementById('updateContainer');
                container.style.opacity = '1';
                container.style.visibility = 'visible';
                
                // 强制更新时的处理（现在按钮已经是居中的单个按钮）
                if (info.mandatory) {
                    const updateBtn = document.getElementById('updateBtn');
                    if (updateBtn) {
                        updateBtn.textContent = '立即更新';
                    }

                    // 强制更新时，原生关闭按钮仍然可用，但关闭会退出应用
                }
                
                // 启动5秒倒计时自动更新
                startAutoUpdateCountdown();
            }
        }
        
        function startAutoUpdateCountdown() {
            const updateBtn = document.getElementById('updateBtn');
            if (!updateBtn) return;
            
            // 清除之前的计时器
            if (autoUpdateTimer) clearTimeout(autoUpdateTimer);
            if (countdownTimer) clearInterval(countdownTimer);
            
            countdown = 5;
            updateBtn.textContent = `立即更新 (${countdown}s)`;
            
            // 每秒更新倒计时显示
            countdownTimer = setInterval(() => {
                countdown--;
                if (countdown > 0) {
                    updateBtn.textContent = `立即更新 (${countdown}s)`;
                } else {
                    clearInterval(countdownTimer);
                    updateBtn.textContent = '立即更新';
                }
            }, 1000);
            
            // 5秒后自动点击更新
            autoUpdateTimer = setTimeout(() => {
                if (!isDownloading) {
                    console.log('5秒倒计时结束，自动开始更新');
                    startUpdate();
                }
            }, 5000);
        }

        function formatUpdateContent(info) {
            let content = `<h3>版本 ${info.version}</h3>`;

            // 优先使用description字段（网站后台的更新说明），然后是release_notes，最后是默认文本
            let updateDescription = '';
            if (info.description && info.description.trim()) {
                updateDescription = info.description;
            } else if (info.release_notes && info.release_notes.trim()) {
                updateDescription = info.release_notes;
            } else if (info.content && info.content.trim()) {
                // 兼容旧版本的content字段
                updateDescription = info.content;
            }

            if (updateDescription) {
                // 处理更新内容，支持HTML标记
                content += `<div class="update-description">${updateDescription}</div>`;
            } else {
                content += `<p>发现新版本，建议立即更新以获得更好的使用体验。</p>`;
            }

            return content;
        }

        function startUpdate() {
            if (isDownloading) return;
            
            // 清除自动更新倒计时
            if (autoUpdateTimer) {
                clearTimeout(autoUpdateTimer);
                autoUpdateTimer = null;
            }
            if (countdownTimer) {
                clearInterval(countdownTimer);
                countdownTimer = null;
            }
            
            isDownloading = true;
            console.log('用户手动开始更新');
            window.updateAPI.startUpdate();
        }

        function cancelUpdate() {
            // 现在没有"稍后提醒"按钮，这个函数主要用于处理窗口关闭
            // 检查是否为强制更新
            if (updateInfo && updateInfo.mandatory) {
                // 强制更新时不允许取消，直接退出应用
                console.log('强制更新：用户尝试关闭窗口，退出应用');
                window.updateAPI.closeUpdateWindow(); // 这会触发应用退出
                return;
            }

            console.log('用户关闭更新窗口');
            window.updateAPI.cancelUpdate();
        }

        function showProgress() {
            document.getElementById('updateActions').classList.add('hidden');
            document.getElementById('progressContainer').style.display = 'block';
        }

        function hideProgress() {
            document.getElementById('progressContainer').style.display = 'none';
        }

        function updateProgress(percent, text) {
            document.getElementById('progressFill').style.width = percent + '%';
            document.getElementById('progressText').textContent = text;
        }

        function showError(message) {
            const errorEl = document.getElementById('errorMessage');
            errorEl.textContent = message;
            errorEl.classList.remove('hidden');
            
            // 隐藏其他消息
            document.getElementById('successMessage').classList.add('hidden');
            document.getElementById('statusText').classList.add('hidden');
        }

        function showSuccess(message) {
            const successEl = document.getElementById('successMessage');
            successEl.textContent = message;
            successEl.classList.remove('hidden');
            
            // 隐藏其他消息
            document.getElementById('errorMessage').classList.add('hidden');
            document.getElementById('statusText').classList.add('hidden');
        }

        function showStatus(message) {
            const statusEl = document.getElementById('statusText');
            statusEl.textContent = message;
            statusEl.classList.remove('hidden');
            
            // 隐藏其他消息
            document.getElementById('errorMessage').classList.add('hidden');
            document.getElementById('successMessage').classList.add('hidden');
        }

        function resetActions() {
            isDownloading = false;
            document.getElementById('updateActions').classList.remove('hidden');
            
            // 重置按钮状态
            const updateBtn = document.getElementById('updateBtn');
            updateBtn.innerHTML = '立即更新';
            updateBtn.disabled = false;
        }

        // 防止右键菜单
        document.addEventListener('contextmenu', (e) => {
            e.preventDefault();
        });

        // 防止选择文本
        document.addEventListener('selectstart', (e) => {
            e.preventDefault();
        });
    </script>
</body>
</html>