<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>关于小梅花AI智能客服</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: #FFFFFF; /* 纯白色背景 */
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100vh;
            width: 100vw;
            padding: 0;
            margin: 0;
            -webkit-user-select: none;
            user-select: none;
            -webkit-app-region: drag; /* 整个窗口可拖拽 */
            overflow: hidden; /* 防止滚动条 */
            border-radius: 16px; /* 圆角边框 */
        }

        /* macOS原生关闭按钮 - 左上角 */
        .window-controls.mac {
            position: absolute;
            top: 15px;
            left: 15px;
            display: flex;
            gap: 8px;
            z-index: 1000;
            -webkit-app-region: no-drag; /* 按钮区域不可拖拽 */
        }

        /* Windows关闭按钮 - 右上角 */
        .window-controls.windows {
            position: absolute;
            top: 15px;
            right: 15px;
            display: flex;
            gap: 8px;
            z-index: 1000;
            -webkit-app-region: no-drag; /* 按钮区域不可拖拽 */
        }

        /* macOS样式按钮 */
        .window-control.mac {
            width: 14px;
            height: 14px;
            border-radius: 50%;
            border: none;
            cursor: pointer;
            position: relative;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .window-control.mac.close {
            background-color: #ff5f56;
        }

        .window-control.mac.close::after {
            content: '×';
            color: #4a0000;
            font-size: 12px;
            font-weight: bold;
            line-height: 1;
            opacity: 0;
            transition: opacity 0.2s ease;
        }

        .window-control.mac.close:hover::after {
            opacity: 1;
        }

        .window-control.mac:hover {
            transform: scale(1.1);
        }

        /* Windows样式按钮 */
        .window-control.windows {
            width: 24px;
            height: 24px;
            border-radius: 4px;
            border: none;
            cursor: pointer;
            position: relative;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(10px);
        }

        .window-control.windows.close::after {
            content: '×';
            color: #000000;
            font-size: 16px;
            font-weight: bold;
            line-height: 1;
        }

        .window-control.windows.close:hover {
            background-color: rgba(255, 255, 255, 0.9);
            transform: scale(1.05);
        }

        .about-container {
            background: #FFFFFF; /* 纯白色背景 */
            border-radius: 16px;
            padding: 40px 30px;
            text-align: center;
            width: 100%; /* 占满整个窗口宽度 */
            height: 100%; /* 占满整个窗口高度 */
            margin: 0;
            /* 移除 no-drag，让容器可以拖拽 */
            position: relative;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            box-sizing: border-box;
        }

        .app-logo {
            width: 64px;
            height: 64px;
            margin: 0 auto 20px;
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .app-name {
            font-size: 24px;
            font-weight: 600;
            color: #333;
            margin-bottom: 10px;
        }

        .app-version {
            font-size: 16px;
            color: #666;
            margin-bottom: 30px;
        }

        .app-links {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-bottom: 30px;
        }

        .app-link {
            color: #007AFF;
            text-decoration: none;
            font-size: 13px;
            padding: 6px 14px;
            border-radius: 6px;
            transition: background-color 0.2s;
            cursor: pointer;
            -webkit-app-region: no-drag; /* 链接不可拖拽 */
        }

        .app-link:hover {
            background-color: rgba(0, 122, 255, 0.1);
        }

        .copyright {
            font-size: 13px;
            color: #999;
            line-height: 1.4;
            margin-top: auto;
        }
    </style>
</head>
<body>
    <!-- macOS窗口控制按钮 -->
    <div class="window-controls mac" id="mac-controls">
        <button class="window-control mac close" onclick="closeWindow()"></button>
    </div>

    <!-- Windows窗口控制按钮 -->
    <div class="window-controls windows" id="windows-controls">
        <button class="window-control windows close" onclick="closeWindow()"></button>
    </div>

    <div class="about-container">
        <div class="app-logo" id="app-logo"></div>
        
        <div class="app-name">小梅花AI智能客服</div>
        <div class="app-version" id="app-version">版本：1.0.5</div>
        
        <div class="app-links" id="app-links">
            <!-- 协议链接将通过JavaScript动态添加 -->
        </div>
        
        <div class="copyright">
            Copyright © 2025 小梅花AI科技版权所有
        </div>
    </div>

    <script>
        // 设置Logo图片和平台检测
        document.addEventListener('DOMContentLoaded', () => {
            const logoElement = document.getElementById('app-logo');
            // 使用指定的logo路径 - 相对于renderer文件夹的位置
            logoElement.style.backgroundImage = 'url(../assets/logo.png)';

            // 平台检测
            const platform = navigator.platform.toLowerCase();
            const isWindows = platform.includes('win');
            const isMac = platform.includes('mac');

            const macControls = document.getElementById('mac-controls');
            const windowsControls = document.getElementById('windows-controls');

            if (isWindows) {
                // Windows平台：显示Windows按钮，隐藏Mac按钮
                if (macControls) macControls.style.display = 'none';
                if (windowsControls) windowsControls.style.display = 'flex';
            } else {
                // macOS平台：显示Mac按钮，隐藏Windows按钮
                if (windowsControls) windowsControls.style.display = 'none';
                if (macControls) macControls.style.display = 'flex';
            }
        });

        // 接收主进程传递的版本信息
        window.electronAPI?.onVersionInfo?.((version) => {
            document.getElementById('app-version').textContent = `版本：${version}`;
        });

        // 接收协议信息
        window.electronAPI?.onAgreementsInfo?.((agreements) => {
            console.log('收到协议信息:', agreements);
            const linksContainer = document.getElementById('app-links');
            linksContainer.innerHTML = ''; // 清空现有内容

            if (agreements && agreements.length > 0) {
                console.log(`显示 ${agreements.length} 个协议`);

                // 清空现有内容并设置容器样式
                linksContainer.style.cssText = `
                    display: flex;
                    flex-wrap: wrap;
                    gap: 12px;
                    justify-content: center;
                    margin-top: 20px;
                `;

                agreements.forEach(agreement => {
                    console.log('添加协议模块:', agreement.title);
                    const agreementModule = document.createElement('div');
                    agreementModule.className = 'app-link agreement-module';
                    agreementModule.style.cssText = `
                        display: inline-block;
                        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                        color: white;
                        padding: 10px 16px;
                        border-radius: 10px;
                        cursor: pointer;
                        text-decoration: none;
                        font-size: 13px;
                        font-weight: 600;
                        transition: all 0.3s ease;
                        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
                        border: none;
                        min-width: 100px;
                        text-align: center;
                    `;

                    // 确保协议标题显示正确
                    agreementModule.textContent = agreement.title || '用户协议';

                    // 添加悬停效果
                    agreementModule.addEventListener('mouseenter', () => {
                        agreementModule.style.transform = 'translateY(-2px)';
                        agreementModule.style.boxShadow = '0 6px 20px rgba(102, 126, 234, 0.4)';
                    });

                    agreementModule.addEventListener('mouseleave', () => {
                        agreementModule.style.transform = 'translateY(0)';
                        agreementModule.style.boxShadow = '0 4px 15px rgba(102, 126, 234, 0.3)';
                    });

                    agreementModule.onclick = () => openAgreement(agreement);
                    linksContainer.appendChild(agreementModule);
                });
            } else {
                console.log('没有协议数据，显示暂无协议');
                // 如果没有协议，显示默认的空状态
                const noAgreements = document.createElement('span');
                noAgreements.style.color = '#999';
                noAgreements.style.fontSize = '12px';
                noAgreements.textContent = '暂无协议';
                linksContainer.appendChild(noAgreements);
            }
        });

        function closeWindow() {
            window.electronAPI?.closeAboutWindow?.();
        }

        function minimizeWindow() {
            window.electronAPI?.minimizeAboutWindow?.();
        }

        function maximizeWindow() {
            // 关于窗口不支持最大化，保持原样
        }

        function openAgreement(agreement) {
            window.electronAPI?.openAgreement?.(agreement);
        }

        // 键盘快捷键支持
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' || (e.metaKey && e.key === 'w')) {
                closeWindow();
            }
        });

        // 窗口加载完成后的初始化
        document.addEventListener('DOMContentLoaded', async () => {
            // 请求版本信息和协议信息
            window.electronAPI?.requestVersionInfo?.();

            // 延迟请求协议信息，确保API已初始化
            setTimeout(() => {
                console.log('请求协议信息...');
                window.electronAPI?.requestAgreementsInfo?.();
            }, 500);

            // 如果5秒后仍未收到协议信息，再次尝试
            setTimeout(() => {
                const linksContainer = document.getElementById('app-links');
                if (linksContainer && linksContainer.innerHTML.trim() === '') {
                    console.log('重新请求协议信息...');
                    window.electronAPI?.requestAgreementsInfo?.();
                }
            }, 5000);
        });
    </script>
</body>
</html>