<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>小梅花AI - 重要通知</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        html {
            background: transparent !important;
            margin: 0 !important;
            padding: 0 !important;
        }

        body {
            font-family: 'Microsoft YaHei', 'PingFang SC', 'Hiragino Sans GB', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: transparent !important;
            height: 100vh;
            width: 100vw;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
            margin: 0 !important;
            padding: 0 !important;
            position: fixed;
            top: 0;
            left: 0;
        }

        .popup-container {
            background: white;
            border-radius: 20px;
            box-shadow: none;
            width: 500px;
            height: 350px;
            overflow: hidden;
            position: relative;
            border: none;
            margin: auto;
            transform: translate(0, 0);
            display: flex;
            flex-direction: column;
        }

        .popup-title-bar {
            background: linear-gradient(135deg, #ff6b9d 0%, #e91e63 100%);
            padding: 13px 30px;
            color: white;
            position: relative;
            border-radius: 20px 20px 0 0;
            text-align: center;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
        }

        .popup-logo {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            object-fit: cover;
        }

        .popup-title {
            font-size: 20px;
            font-weight: 600;
            margin: 0;
            text-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            letter-spacing: 0.5px;
        }

        .close-button {
            position: absolute;
            top: 50%;
            right: 20px;
            transform: translateY(-50%);
            background: rgba(255, 255, 255, 0.25);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            width: 36px;
            height: 36px;
            color: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            font-weight: bold;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .close-button:hover {
            background: rgba(255, 255, 255, 0.35);
            transform: translateY(-50%) scale(1.05);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .popup-content {
            padding: 30px 30px;
            max-height: 200px;
            overflow-y: auto;
            color: #333;
            line-height: 1.6;
            background: white;
            font-size: 16px;
            text-align: center;
            flex: 1;
        }

        .popup-content h1, .popup-content h2, .popup-content h3 {
            color: #2c3e50;
            margin-bottom: 15px;
        }

        .popup-content p {
            margin-bottom: 12px;
        }

        .popup-content ul, .popup-content ol {
            margin-left: 20px;
            margin-bottom: 12px;
        }

        .popup-content a {
            color: #667eea;
            text-decoration: none;
            border-bottom: 1px solid transparent;
            transition: border-color 0.2s ease;
        }

        .popup-content a:hover {
            border-bottom-color: #667eea;
        }

        .popup-content strong {
            color: #2c3e50;
            font-weight: 600;
        }

        /* 新增：弹窗内容样式 */
        .popup-content-body {
            text-align: center;
        }

        .popup-description {
            font-size: 18px;
            color: #666;
            margin-bottom: 30px;
            line-height: 1.6;
        }

        .feature-list {
            display: flex;
            flex-direction: column;
            gap: 25px;
            margin: 30px 0;
            text-align: left;
        }

        .feature-item {
            display: flex;
            align-items: center;
            justify-content: flex-start;
            padding: 0;
        }

        .check-icon {
            color: #4CAF50;
            font-size: 20px;
            font-weight: bold;
            margin-right: 15px;
            flex-shrink: 0;
        }

        .feature-text {
            font-size: 16px;
            color: #333;
            line-height: 1.4;
            font-weight: 500;
        }

        .popup-actions {
            padding: 15px 40px;
            border-top: 1px solid #e2e8f0;
            display: flex;
            justify-content: center;
            gap: 15px;
            background: white;
            border-radius: 0 0 20px 20px;
            flex-shrink: 0;
        }

        .btn {
            padding: 15px 35px;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: all 0.3s ease;
            min-width: 120px;
            letter-spacing: 0.5px;
            text-align: center;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .btn-primary {
            background: linear-gradient(135deg, #7c4dff 0%, #651fff 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(124, 77, 255, 0.3);
            padding: 15px 40px;
            min-width: 140px;
        }

        .btn-primary:hover {
            box-shadow: 0 6px 20px rgba(124, 77, 255, 0.4);
            transform: translateY(-2px);
            background: linear-gradient(135deg, #8c5aff 0%, #7c4dff 100%);
        }

        .btn-secondary {
            background: #f1f5f9;
            color: #64748b;
            border: 1px solid #e2e8f0;
        }

        .btn-secondary:hover {
            background: #e2e8f0;
            color: #475569;
            transform: translateY(-1px);
        }

        /* 弹窗类型样式 */
        .popup-once .popup-title-bar {
            background: linear-gradient(135deg, #ffc107, #ff9800);
        }

        .popup-weekly .popup-title-bar {
            background: linear-gradient(135deg, #007bff, #0056b3);
        }

        .popup-permanent .popup-title-bar {
            background: linear-gradient(135deg, #28a745, #20c997);
        }

        /* 滚动条样式 */
        .popup-content::-webkit-scrollbar {
            width: 6px;
        }

        .popup-content::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 3px;
        }

        .popup-content::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 3px;
        }

        .popup-content::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }

        /* 响应式设计 */
        @media (max-width: 500px) {
            .popup-container {
                width: 95vw;
                margin: 10px;
            }
            
            .popup-title-bar {
                padding: 12px 15px;
            }
            
            .popup-title {
                font-size: 16px;
            }
            
            .popup-content {
                padding: 20px;
                max-height: 250px;
            }
            
            .popup-actions {
                padding: 6px 20px;
                flex-direction: column;
            }
            
            .btn {
                width: 100%;
            }
        }

        /* 动画效果 */
        @keyframes slideIn {
            from {
                opacity: 0;
                transform: scale(0.8) translateY(-20px);
            }
            to {
                opacity: 1;
                transform: scale(1) translateY(0);
            }
        }

        .popup-container {
            animation: slideIn 0.3s ease-out;
        }
    </style>
</head>
<body>
    <div id="popup-container" class="popup-container">
        <div id="popup-title-bar" class="popup-title-bar">
            <img src="../assets/logo.png" alt="小梅花AI" class="popup-logo">
            <h2 id="popup-title" class="popup-title">小梅花AI智能客服</h2>
            <button id="close-button" class="close-button" title="关闭">×</button>
        </div>
        
        <div id="popup-content" class="popup-content">
            <!-- 弹窗内容将通过JavaScript加载 -->
            <p>正在加载通知内容...</p>
        </div>
        
        <div class="popup-actions">
            <button id="confirm-button" class="btn btn-primary">我知道了</button>
        </div>
    </div>

    <script>
        // 等待DOM加载完成
        document.addEventListener('DOMContentLoaded', async () => {
            console.log('📄 弹窗DOM加载完成，开始初始化');

            // 添加延迟确保preload脚本完全加载
            await new Promise(resolve => setTimeout(resolve, 100));

            try {
                // 检查popupAPI是否可用
                if (window.popupAPI) {
                    console.log('✅ popupAPI可用，开始获取弹窗数据...');
                    const popup = await window.popupAPI.getCurrentPopup();

                    console.log('🔍 获取到的弹窗数据:', popup);

                    if (popup && popup.title) {
                        console.log('📋 弹窗数据有效，开始渲染:', popup.title);
                        renderPopup(popup);
                    } else {
                        console.warn('⚠️ 弹窗数据无效或为空:', popup);
                        showErrorMessage('无法加载通知内容，请稍后重试。');
                    }
                } else {
                    console.error('❌ popupAPI不可用，window对象:', Object.keys(window));
                    showErrorMessage('无法加载通知内容，请稍后重试。');
                }
            } catch (error) {
                console.error('❌ 加载弹窗数据失败:', error);
                showErrorMessage('无法加载通知内容，请稍后重试。');
            }
        });

        // 渲染弹窗内容
        function renderPopup(popup) {
            console.log('🎨 开始渲染弹窗:', popup);

            try {
                // 设置标题 - 保持固定标题
                const titleElement = document.getElementById('popup-title');
                if (titleElement) {
                    titleElement.textContent = '小梅花AI智能客服';
                    console.log('✅ 标题设置成功: 小梅花AI智能客服');
                }

                // 设置内容
                const contentElement = document.getElementById('popup-content');
                if (contentElement) {
                    const defaultContent = `
                        <div style="text-align: center; padding: 20px 0;">
                            <h3 style="color: #2c3e50; margin-bottom: 15px;">版本：1.0.5</h3>
                            <p style="color: #666; margin-bottom: 20px;">暂无协议</p>
                            <p style="color: #999; font-size: 14px;">Copyright © 2025 小梅花AI科技版权所有</p>
                        </div>
                    `;
                    contentElement.innerHTML = popup.content || defaultContent;
                    console.log('✅ 内容设置成功');
                }

                // 绑定关闭按钮事件
                const closeButton = document.getElementById('close-button');
                if (closeButton) {
                    closeButton.addEventListener('click', async () => {
                        console.log('🔒 用户点击关闭按钮');
                        await closePopup(popup.id);
                    });
                    console.log('✅ 关闭按钮事件绑定成功');
                }

                // 绑定确认按钮事件
                const confirmButton = document.getElementById('confirm-button');
                if (confirmButton) {
                    confirmButton.addEventListener('click', async () => {
                        console.log('✅ 用户点击确认按钮');
                        await window.popupAPI.clickPopup(popup.id, 'confirm');
                        await closePopup(popup.id);
                    });
                    console.log('✅ 确认按钮事件绑定成功');
                }

                // 处理内容中的链接点击
                const links = contentElement.querySelectorAll('a[href]');
                links.forEach((link, index) => {
                    link.addEventListener('click', async (e) => {
                        e.preventDefault();
                        const url = link.getAttribute('href');
                        console.log(`🔗 用户点击链接 ${index + 1}:`, url);
                        await window.popupAPI.clickPopup(popup.id, 'link_click');
                        await window.popupAPI.openExternal(url);
                    });
                });

                if (links.length > 0) {
                    console.log(`✅ ${links.length} 个链接事件绑定成功`);
                }

                console.log('🎉 弹窗渲染完成');

            } catch (error) {
                console.error('❌ 渲染弹窗失败:', error);
                showErrorMessage('渲染弹窗内容时出错。');
            }
        }

        // 显示错误信息
        function showErrorMessage(message) {
            const titleElement = document.getElementById('popup-title');
            const contentElement = document.getElementById('popup-content');

            if (titleElement) titleElement.textContent = '加载失败';
            if (contentElement) contentElement.innerHTML = `<p style="color: #666;">${message}</p>`;
        }

        // 关闭弹窗的辅助函数
        async function closePopup(popupId) {
            try {
                if (window.popupAPI) {
                    if (popupId) {
                        await window.popupAPI.closePopup(popupId);
                    } else {
                        const popup = await window.popupAPI.getCurrentPopup();
                        if (popup) {
                            await window.popupAPI.closePopup(popup.id);
                        }
                    }
                }
            } catch (error) {
                console.error('❌ 关闭弹窗失败:', error);
                // 强制关闭窗口
                if (window.close) {
                    window.close();
                }
            }
        }

        // ESC键关闭
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                closePopup();
            }
        });
    </script>
</body>
</html>