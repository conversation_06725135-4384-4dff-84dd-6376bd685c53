/**
 * 版本号动态更新模块
 * 用于在渲染进程中动态更新所有版本号显示
 */

class VersionUpdater {
  constructor() {
    this.versionInfo = null;
    this.init();
  }

  /**
   * 初始化版本更新器
   */
  async init() {
    try {
      // 从主进程获取版本信息
      if (window.xiaomeihuaAPI && window.xiaomeihuaAPI.getVersionInfo) {
        this.versionInfo = await window.xiaomeihuaAPI.getVersionInfo();
        this.updateAllVersionDisplays();
      }
    } catch (error) {
      console.error('初始化版本更新器失败:', error);
      // 使用默认版本信息
      this.versionInfo = {
        version: '1.0.0',
        versionString: 'v1.0.0',
        fullVersionString: 'v1.0.0 © 2025 小梅花AI科技',
        productName: '小梅花AI智能客服',
        productNameWithVersion: '小梅花AI智能客服 v1.0.0',
        company: '小梅花AI科技',
        copyrightYear: '2025'
      };
      this.updateAllVersionDisplays();
    }
  }

  /**
   * 更新所有版本号显示
   */
  updateAllVersionDisplays() {
    if (!this.versionInfo) return;

    // 更新左下角版本号
    this.updateSidebarVersion();
    
    // 更新设置页面版本号
    this.updateSettingsVersion();
    
    // 更新登录页面版本号（如果存在）
    this.updateLoginVersion();
    
    // 更新窗口标题
    this.updateWindowTitle();
    
    // 更新关于对话框版本号
    this.updateAboutDialogVersion();
  }

  /**
   * 更新左下角版本号
   */
  updateSidebarVersion() {
    const sidebarVersionElements = document.querySelectorAll('.sidebar-bottom div');
    sidebarVersionElements.forEach(element => {
      if (element.textContent.startsWith('v') || element.textContent.match(/\d+\.\d+\.\d+/)) {
        element.textContent = this.versionInfo.versionString;
      }
    });
  }

  /**
   * 更新设置页面版本号
   */
  updateSettingsVersion() {
    // 查找设置页面中的版本显示元素
    const settingsVersionElements = document.querySelectorAll('.settings-value');
    settingsVersionElements.forEach(element => {
      if (element.textContent.startsWith('v') || element.textContent.match(/\d+\.\d+\.\d+/)) {
        element.textContent = this.versionInfo.versionString;
      }
    });

    // 更新软件版本标签
    const versionLabels = document.querySelectorAll('.settings-label');
    versionLabels.forEach(label => {
      if (label.textContent === '软件版本') {
        const valueElement = label.parentElement.querySelector('.settings-value');
        if (valueElement) {
          valueElement.textContent = this.versionInfo.versionString;
        }
      }
    });
  }

  /**
   * 更新登录页面版本号
   */
  updateLoginVersion() {
    const versionElements = document.querySelectorAll('.version');
    versionElements.forEach(element => {
      if (element.textContent.includes('版本:') || element.textContent.includes('©')) {
        element.textContent = `版本: ${this.versionInfo.fullVersionString}`;
      }
    });
  }

  /**
   * 更新窗口标题
   */
  updateWindowTitle() {
    if (document.title.includes('小梅花')) {
      document.title = this.versionInfo.productName;
    }
  }

  /**
   * 更新关于对话框版本号
   */
  updateAboutDialogVersion() {
    // 监听关于对话框的创建
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        mutation.addedNodes.forEach((node) => {
          if (node.nodeType === Node.ELEMENT_NODE) {
            // 查找关于对话框中的版本信息
            const versionElements = node.querySelectorAll('*');
            versionElements.forEach(element => {
              if (element.textContent && element.textContent.match(/v\d+\.\d+\.\d+/)) {
                element.textContent = element.textContent.replace(/v\d+\.\d+\.\d+/, this.versionInfo.versionString);
              }
              if (element.textContent && element.textContent.includes('小梅花AI智能客服') && element.textContent.includes('v')) {
                element.textContent = this.versionInfo.productNameWithVersion;
              }
            });
          }
        });
      });
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true
    });
  }

  /**
   * 手动更新版本信息
   */
  async refreshVersionInfo() {
    await this.init();
  }

  /**
   * 获取当前版本信息
   */
  getVersionInfo() {
    return this.versionInfo;
  }

  /**
   * 在指定元素中查找并更新版本号
   */
  updateVersionInElement(element, versionString = null) {
    if (!element) return;
    
    const version = versionString || this.versionInfo.versionString;
    const fullVersion = versionString ? `${versionString} © ${this.versionInfo.copyrightYear} ${this.versionInfo.company}` : this.versionInfo.fullVersionString;
    
    // 更新文本内容中的版本号
    if (element.textContent) {
      element.textContent = element.textContent.replace(/v\d+\.\d+\.\d+/g, version);
      element.textContent = element.textContent.replace(/版本:\s*v\d+\.\d+\.\d+[^©]*©[^$]*/g, `版本: ${fullVersion}`);
    }
    
    // 递归更新子元素
    element.querySelectorAll('*').forEach(child => {
      if (child.textContent && child.textContent.match(/v\d+\.\d+\.\d+/)) {
        child.textContent = child.textContent.replace(/v\d+\.\d+\.\d+/g, version);
      }
    });
  }
}

// 创建全局实例
window.versionUpdater = new VersionUpdater();

// 页面加载完成后自动更新版本号
document.addEventListener('DOMContentLoaded', () => {
  if (window.versionUpdater) {
    window.versionUpdater.updateAllVersionDisplays();
  }
});

// 导出模块
if (typeof module !== 'undefined' && module.exports) {
  module.exports = VersionUpdater;
}
