<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户协议</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: transparent;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
            margin: 0;
            padding: 0;
        }

        .agreement-container {
            background: white;
            border-radius: 12px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            width: 100%;
            height: 100%;
            max-width: none;
            max-height: none;
            overflow: hidden;
            position: relative;
            display: flex;
            flex-direction: column;
        }

        .agreement-header {
            background: linear-gradient(135deg, #667eea, #764ba2);
            padding: 20px;
            color: white;
            text-align: center;
            position: relative;
            flex-shrink: 0;
        }

        .agreement-title {
            font-size: 20px;
            font-weight: 600;
            margin: 0;
        }

        .agreement-content {
            padding: 25px;
            flex: 1;
            overflow-y: auto;
            color: #333;
            line-height: 1.6;
        }

        .agreement-content h1, 
        .agreement-content h2, 
        .agreement-content h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            margin-top: 20px;
        }

        .agreement-content h1:first-child,
        .agreement-content h2:first-child,
        .agreement-content h3:first-child {
            margin-top: 0;
        }

        .agreement-content p {
            margin-bottom: 12px;
        }

        .agreement-content ul, 
        .agreement-content ol {
            margin-left: 20px;
            margin-bottom: 12px;
        }

        .agreement-content li {
            margin-bottom: 6px;
        }

        .agreement-content a {
            color: #667eea;
            text-decoration: none;
            border-bottom: 1px solid transparent;
            transition: border-color 0.2s ease;
        }

        .agreement-content a:hover {
            border-bottom-color: #667eea;
        }

        .agreement-content strong {
            color: #2c3e50;
            font-weight: 600;
        }

        .agreement-content blockquote {
            border-left: 4px solid #667eea;
            margin: 15px 0;
            padding: 10px 15px;
            background: #f8f9fa;
            font-style: italic;
        }

        .close-button {
            position: absolute;
            top: 15px;
            right: 15px;
            background: rgba(255, 255, 255, 0.2);
            border: none;
            border-radius: 6px;
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            font-size: 18px;
            color: white;
            font-weight: bold;
            transition: all 0.2s ease;
            z-index: 10;
        }

        .close-button:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.05);
        }



        /* 滚动条样式 */
        .agreement-content::-webkit-scrollbar {
            width: 6px;
        }

        .agreement-content::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 3px;
        }

        .agreement-content::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 3px;
        }

        .agreement-content::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }

        /* 响应式设计 */
        @media (max-width: 600px) {
            .agreement-content {
                padding: 20px;
                padding-top: 50px;
            }
        }

        /* 动画效果 */
        @keyframes slideIn {
            from {
                opacity: 0;
                transform: scale(0.9) translateY(-20px);
            }
            to {
                opacity: 1;
                transform: scale(1) translateY(0);
            }
        }

        .agreement-container {
            animation: slideIn 0.3s ease-out;
        }

        /* 加载状态 */
        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }

        .loading::after {
            content: '';
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 2px solid #ddd;
            border-top-color: #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-left: 10px;
        }

        @keyframes spin {
            to {
                transform: rotate(360deg);
            }
        }
    </style>
</head>
<body>
    <div id="agreement-container" class="agreement-container">
        <div class="agreement-header">
            <h1 id="agreement-title" class="agreement-title">用户协议</h1>
            <button id="close-button" class="close-button">×</button>
        </div>

        <div id="agreement-content" class="agreement-content">
            <div class="loading">正在加载协议内容</div>
        </div>
    </div>

    <script>
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', async () => {
            console.log('协议页面加载完成，开始初始化...');

            // 获取当前协议数据
            try {
                if (window.agreementAPI && window.agreementAPI.getCurrentAgreement) {
                    const agreement = await window.agreementAPI.getCurrentAgreement();
                    console.log('获取到协议数据:', agreement);

                    if (agreement) {
                        // 更新标题
                        const titleElement = document.getElementById('agreement-title');
                        if (titleElement && agreement.title) {
                            titleElement.textContent = agreement.title;
                        }

                        // 更新内容（不再在内容中重复显示标题）
                        const contentElement = document.getElementById('agreement-content');
                        if (contentElement && agreement.content) {
                            contentElement.innerHTML = agreement.content;
                        } else {
                            contentElement.innerHTML = '<div style="padding: 20px; text-align: center; color: #666;">暂无协议内容</div>';
                        }
                    } else {
                        // 没有协议数据
                        const contentElement = document.getElementById('agreement-content');
                        if (contentElement) {
                            contentElement.innerHTML = '<div style="padding: 20px; text-align: center; color: #666;">暂无协议内容</div>';
                        }
                    }
                } else {
                    console.error('协议API不可用');
                    const contentElement = document.getElementById('agreement-content');
                    if (contentElement) {
                        contentElement.innerHTML = '<div style="padding: 20px; text-align: center; color: #666;">协议加载失败</div>';
                    }
                }
            } catch (error) {
                console.error('加载协议失败:', error);
                const contentElement = document.getElementById('agreement-content');
                if (contentElement) {
                    contentElement.innerHTML = '<div style="padding: 20px; text-align: center; color: #666;">协议加载失败</div>';
                }
            }

            // 绑定关闭按钮事件
            const closeButton = document.getElementById('close-button');

            function closeWindow() {
                if (window.agreementAPI && window.agreementAPI.closeWindow) {
                    window.agreementAPI.closeWindow();
                } else {
                    window.close();
                }
            }

            if (closeButton) {
                closeButton.addEventListener('click', closeWindow);
            }
        });

        // 键盘快捷键支持
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                // ESC 关闭窗口
                if (window.agreementAPI && window.agreementAPI.closeWindow) {
                    window.agreementAPI.closeWindow();
                } else {
                    window.close();
                }
            }
        });
    </script>
</body>
</html>