/**
 * APP设置API客户端
 * 处理弹窗、协议等功能的API调用
 */

const axios = require('axios');
const Store = require('electron-store');

class AppSettingsAPI {
    constructor() {
        this.store = new Store({
            name: 'app-settings-config',
            encryptionKey: 'xiaomeihua-settings-2025'
        });
        
        // API基础配置 - 支持本地开发和生产环境
        const defaultURL = this.detectEnvironment();
        this.baseURL = this.store.get('apiBaseURL', defaultURL);
        this.timeout = 10000;
        
        // 创建axios实例
        this.apiClient = axios.create({
            baseURL: this.baseURL,
            timeout: this.timeout,
            headers: {
                'Content-Type': 'application/json',
                'User-Agent': 'XiaoMeiHua-App/1.0.0'
            }
        });
        
        // 设置响应拦截器
        this.apiClient.interceptors.response.use(
            response => response,
            error => {
                console.error('API请求失败:', error.message);
                return Promise.reject(error);
            }
        );
    }

    /**
     * 检测运行环境并返回合适的API URL
     * @returns {string} API基础URL
     */
    detectEnvironment() {
        const { app } = require('electron');
        const path = require('path');
        const fs = require('fs');

        // 检查是否在开发环境
        const isDev = !app.isPackaged;

        if (isDev) {
            // 开发环境：检查本地后台是否存在
            const localBackendPath = path.join(__dirname, '../../../小梅花后台_27-0844');
            if (fs.existsSync(localBackendPath)) {
                console.log('🔍 检测到本地后台，使用本地API');
                return 'http://localhost:8080/api';
            }
        }

        // 生产环境或本地后台不存在时使用线上API
        console.log('🌐 使用线上API');
        return 'https://xiaomeihuakefu.cn/api';
    }

    /**
     * 设置API基础URL
     * @param {string} url - API基础URL
     */
    setBaseURL(url) {
        this.baseURL = url;
        this.apiClient.defaults.baseURL = url;
        this.store.set('apiBaseURL', url);
    }
    
    /**
     * 获取当前活跃的弹窗
     * @returns {Promise<Object>} 弹窗数据
     */
    async getActivePopup() {
        try {
            console.log('🔍 调用API获取活跃弹窗...');

            // 首先尝试本地API调用
            console.log('🏠 尝试本地API调用...');
            const localResult = await this.callLocalAPI();
            console.log('🏠 本地API调用结果:', localResult);

            if (localResult.success) {
                console.log('✅ 本地API调用成功，返回结果');
                return localResult;
            }

            // 本地API失败，尝试远程独立弹窗API
            console.log('🌐 本地API失败，尝试远程独立弹窗API...');
            const response = await this.apiClient.get('/popup.php');

            console.log('📡 远程API响应:', response.data);

            if (response.data.success) {
                const popup = response.data.popup;
                console.log('✅ 获取活跃弹窗成功:', popup ? popup.title : '无弹窗');
                return {
                    success: true,
                    popup: popup
                };
            } else {
                console.warn('⚠️ 远程API返回失败:', response.data.message);
                return { success: false, message: response.data.message || '获取弹窗失败' };
            }
        } catch (error) {
            console.error('❌ 获取弹窗API调用失败:', error.message);
            console.error('❌ 错误详情:', error);

            // 如果所有API都失败，使用备用方案
            console.log('🔄 所有API都失败，使用备用方案');
            return this.getFallbackPopup();
        }
    }

    /**
     * 调用本地API（直接执行PHP脚本）
     * @returns {Promise<Object>} API响应
     */
    async callLocalAPI() {
        try {
            const { spawn } = require('child_process');
            const path = require('path');
            const fs = require('fs');

            // 查找本地后台目录
            const possiblePaths = [
                path.join(__dirname, '../../../小梅花后台_27-0844'),
                path.join(__dirname, '../../小梅花后台_27-0844'),
                path.join(__dirname, '../小梅花后台_27-0844'),
                path.join(__dirname, '../../../../小梅花后台_27-0844'),
                path.join(__dirname, '../../../../../小梅花后台_27-0844')
            ];

            let backendPath = null;
            for (const p of possiblePaths) {
                if (fs.existsSync(p)) {
                    backendPath = p;
                    break;
                }
            }

            if (!backendPath) {
                console.log('📭 未找到本地后台目录');
                return { success: false, message: '本地后台不存在' };
            }

            console.log('📂 找到本地后台:', backendPath);

            // 执行独立弹窗API获取弹窗数据
            return new Promise((resolve) => {
                const scriptPath = path.join(backendPath, 'api', 'popup.php');

                // 设置环境变量模拟GET请求
                const env = {
                    ...process.env,
                    REQUEST_METHOD: 'GET',
                    REQUEST_URI: '/api/popup.php'
                };

                const php = spawn('php', [scriptPath], {
                    cwd: backendPath,
                    env: env,
                    stdio: ['pipe', 'pipe', 'pipe']
                });
                let output = '';
                let error = '';

                php.stdout.on('data', (data) => {
                    output += data.toString();
                });

                php.stderr.on('data', (data) => {
                    error += data.toString();
                });

                php.on('close', (code) => {
                    console.log('🔍 PHP脚本执行完成，代码:', code);
                    console.log('🔍 PHP输出长度:', output.length);
                    if (error) console.log('🔍 PHP错误:', error);

                    if (code === 0 && output.trim()) {
                        try {
                            // 尝试提取JSON部分
                            const lines = output.trim().split('\n');
                            let jsonLine = '';

                            // 查找包含JSON的行
                            for (const line of lines) {
                                if (line.trim().startsWith('{') && line.includes('"success"')) {
                                    jsonLine = line.trim();
                                    break;
                                }
                            }

                            if (jsonLine) {
                                const data = JSON.parse(jsonLine);
                                console.log('✅ 本地API调用成功:', data);
                                resolve(data);
                            } else {
                                console.error('❌ 未找到有效的JSON响应');
                                resolve({ success: false, message: '未找到有效的JSON响应' });
                            }
                        } catch (e) {
                            console.error('❌ 解析本地API响应失败:', e.message);
                            console.error('❌ 原始输出:', output);
                            resolve({ success: false, message: '解析响应失败' });
                        }
                    } else {
                        console.error('❌ 本地API调用失败:', error || '未知错误');
                        resolve({ success: false, message: error || '本地API调用失败' });
                    }
                });
            });

        } catch (error) {
            console.error('❌ 本地API调用异常:', error.message);
            return { success: false, message: error.message };
        }
    }

    /**
     * 获取备用弹窗（当主API失败时）
     * @returns {Promise<Object>} 备用弹窗数据
     */
    async getFallbackPopup() {
        console.log('🔄 API调用失败，不显示任何弹窗');

        // 不再返回备用弹窗，确保只有后台创建的弹窗才会显示
        return {
            success: false,
            message: 'API调用失败，无弹窗数据',
            popup: null
        };
    }
    
    /**
     * 获取已发布的协议
     * @returns {Promise<Object>} 协议数据
     */
    async getPublishedAgreement() {
        try {
            const response = await this.apiClient.get('/app_settings.php/agreement/list');
            if (response.data.success && response.data.published_agreements) {
                // 返回所有已发布的协议
                return {
                    success: true,
                    agreements: response.data.published_agreements,
                    agreement: response.data.published_agreements[0] || null // 兼容旧版本
                };
            }
            return { success: false, message: '获取协议失败' };
        } catch (error) {
            console.error('获取协议失败:', error);
            return { success: false, message: error.message };
        }
    }

    /**
     * 获取协议列表 - 主进程调用的方法
     * @returns {Promise<Object>} 协议列表
     */
    async getAgreements() {
        return await this.getPublishedAgreements();
    }

    /**
     * 获取所有已发布的协议列表
     * @returns {Promise<Object>} 协议列表
     */
    async getPublishedAgreements() {
        try {
            console.log('🔍 开始获取协议列表...');

            // 优先使用app_settings.php获取所有协议列表
            try {
                console.log('🔄 优先调用app_settings.php获取所有协议...');
                const response = await this.apiClient.get('/app_settings.php/agreement/list');
                console.log('📡 协议列表API响应:', response.data);

                if (response.data.success && response.data.published_agreements) {
                    console.log(`✅ 成功获取 ${response.data.published_agreements.length} 个协议`);
                    return {
                        success: true,
                        agreements: response.data.published_agreements
                    };
                }
            } catch (apiError) {
                console.warn('⚠️ app_settings.php API调用失败:', apiError.message);
            }

            // 如果app_settings.php失败，尝试通过agreement.php获取单个协议作为备用
            try {
                console.log('🔄 备用方案：调用agreement.php获取单个协议...');
                const response = await this.apiClient.get('/agreement.php?type=privacy');
                console.log('📡 agreement.php响应:', response.data);

                if (response.data.success && response.data.agreement) {
                    console.log('✅ 通过agreement.php成功获取协议（备用方案）');
                    return {
                        success: true,
                        agreements: [response.data.agreement]
                    };
                }
            } catch (agreementError) {
                console.warn('⚠️ agreement.php API调用失败:', agreementError.message);
            }

            // 尝试本地备用方法
            try {
                console.log('🔄 尝试本地备用协议获取方法...');
                const fallbackResult = await this.getAgreement('privacy');
                if (fallbackResult.success && fallbackResult.agreement) {
                    console.log('✅ 本地备用方法成功获取协议');
                    return {
                        success: true,
                        agreements: [fallbackResult.agreement]
                    };
                }
            } catch (fallbackError) {
                console.error('❌ 本地备用方法也失败:', fallbackError);
            }

            console.warn('⚠️ 所有协议获取方法都失败');
            return { success: false, message: '获取协议列表失败' };
        } catch (error) {
            console.error('❌ 获取协议列表失败:', error);
            return { success: false, message: error.message };
        }
    }

    /**
     * 比较版本号
     * @param {string} version1 - 版本号1
     * @param {string} version2 - 版本号2
     * @returns {number} 比较结果 (1: version1 > version2, -1: version1 < version2, 0: 相等)
     */
    compareVersions(version1, version2) {
        const v1Parts = version1.split('.').map(Number);
        const v2Parts = version2.split('.').map(Number);
        
        const maxLength = Math.max(v1Parts.length, v2Parts.length);
        
        for (let i = 0; i < maxLength; i++) {
            const v1Part = v1Parts[i] || 0;
            const v2Part = v2Parts[i] || 0;
            
            if (v1Part > v2Part) return 1;
            if (v1Part < v2Part) return -1;
        }
        
        return 0;
    }
    
    /**
     * 测试API连接
     * @returns {Promise<boolean>} 连接是否成功
     */
    async testConnection() {
        try {
            const response = await this.apiClient.get('/test_connection.php');
            return response.data.success === true;
        } catch (error) {
            console.error('API连接测试失败:', error);
            return false;
        }
    }
    
    /**
     * 记录弹窗显示日志
     * @param {number} popupId - 弹窗ID
     * @param {string} action - 操作类型 (show, close, click)
     */
    async logPopupAction(popupId, action) {
        try {
            await this.apiClient.post('/app_settings.php/popup/log', {
                popup_id: popupId,
                action: action,
                timestamp: new Date().toISOString(),
                app_version: require('../../package.json').version
            });
        } catch (error) {
            console.error('记录弹窗日志失败:', error);
        }
    }

    /**
     * 获取协议内容 - 使用独立API接口
     * @param {string} type 协议类型 (privacy/terms)
     * @returns {Promise<Object>} 协议数据
     */
    async getAgreement(type = 'privacy') {
        try {
            console.log(`🔍 获取${type}协议（独立API）...`);

            // 首先尝试本地API调用
            const localResult = await this.callLocalAgreementAPI(type);
            if (localResult.success) {
                return localResult;
            }

            // 本地API失败，尝试远程独立协议API
            console.log('🌐 本地API失败，尝试远程独立协议API...');
            const response = await this.apiClient.get(`/agreement_standalone.php?type=${type}`);

            console.log('📡 协议API响应:', response.data);

            if (response.data.success) {
                const agreement = response.data.agreement;
                console.log('✅ 获取协议成功:', agreement ? agreement.title : '无协议');
                return {
                    success: true,
                    agreement: agreement
                };
            } else {
                console.warn('⚠️ 协议API返回失败:', response.data.message);
                return { success: false, message: response.data.message || '获取协议失败' };
            }
        } catch (error) {
            console.error('❌ 获取协议API调用失败:', error.message);
            return { success: false, message: error.message };
        }
    }

    /**
     * 调用本地协议API
     * @param {string} type 协议类型
     * @returns {Promise<Object>} API响应
     */
    async callLocalAgreementAPI(type) {
        try {
            const { spawn } = require('child_process');
            const path = require('path');
            const fs = require('fs');

            // 查找本地后台目录
            const possiblePaths = [
                path.join(__dirname, '../../../小梅花后台_27-0844'),
                path.join(__dirname, '../../小梅花后台_27-0844'),
                path.join(__dirname, '../小梅花后台_27-0844')
            ];

            let backendPath = null;
            for (const p of possiblePaths) {
                if (fs.existsSync(p)) {
                    backendPath = p;
                    break;
                }
            }

            if (!backendPath) {
                return { success: false, message: '本地后台不存在' };
            }

            // 执行独立协议API（使用不依赖数据库的版本）
            return new Promise((resolve) => {
                const scriptPath = path.join(backendPath, 'api', 'agreement_standalone.php');

                // 设置环境变量模拟GET请求
                const env = { ...process.env, QUERY_STRING: `type=${type}` };
                const php = spawn('php', [scriptPath], { cwd: backendPath, env });
                let output = '';
                let error = '';

                php.stdout.on('data', (data) => {
                    output += data.toString();
                });

                php.stderr.on('data', (data) => {
                    error += data.toString();
                });

                php.on('close', (code) => {
                    if (code === 0 && output.trim()) {
                        try {
                            const data = JSON.parse(output.trim());
                            resolve(data);
                        } catch (e) {
                            resolve({ success: false, message: '解析协议响应失败' });
                        }
                    } else {
                        resolve({ success: false, message: error || '本地协议API调用失败' });
                    }
                });
            });

        } catch (error) {
            return { success: false, message: error.message };
        }
    }




}

module.exports = AppSettingsAPI;