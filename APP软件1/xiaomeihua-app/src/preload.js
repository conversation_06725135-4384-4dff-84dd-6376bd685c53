const { contextBridge, ipcRenderer, remote } = require('electron');

// 创建一个事件缓存，避免重复监听
const eventListeners = new Map();

// 优化的事件监听函数
function createEventListener(channel, callback) {
  // 如果已经有这个通道的监听器，先移除
  if (eventListeners.has(channel)) {
    ipcRenderer.removeListener(channel, eventListeners.get(channel));
  }
  
  // 创建新的监听器函数
  const listener = (event, ...args) => callback(...args);
  
  // 保存到缓存
  eventListeners.set(channel, listener);
  
  // 添加监听
  ipcRenderer.on(channel, listener);
  
  // 返回清理函数
  return () => {
    ipcRenderer.removeListener(channel, listener);
    eventListeners.delete(channel);
  };
}

// 获取主进程中缓存的店铺信息
function getCachedShopInfo() {
  try {
    // 尝试从remote获取全局变量
    if (remote && remote.getGlobal) {
      return remote.getGlobal('shopInfoCache');
    }
    
    // 如果remote不可用，通过同步IPC获取
    return ipcRenderer.sendSync('get-cached-shop-info');
  } catch (error) {
    console.error('获取缓存的店铺信息失败:', error);
    return null;
  }
}

// 禁用键盘刷新功能
document.addEventListener('DOMContentLoaded', () => {
  // 监听键盘事件，禁止F5和Ctrl+R刷新
  window.addEventListener('keydown', (event) => {
    // 禁止F5刷新
    if (event.key === 'F5') {
      event.preventDefault();
      console.log('已阻止F5刷新');
      return false;
    }
    
    // 禁止Ctrl+R刷新
    if ((event.ctrlKey || event.metaKey) && (event.key === 'r' || event.key === 'R')) {
      event.preventDefault();
      console.log('已阻止Ctrl+R/Command+R刷新');
      return false;
    }
    
    // 禁止Ctrl+Shift+R强制刷新
    if ((event.ctrlKey || event.metaKey) && event.shiftKey && (event.key === 'r' || event.key === 'R')) {
      event.preventDefault();
      console.log('已阻止Ctrl+Shift+R/Command+Shift+R强制刷新');
      return false;
    }
  }, true);
  
  console.log('已设置键盘刷新拦截');
});

// 监听window消息，用于捕获webview中的新窗口请求
window.addEventListener('message', (event) => {
  if (event.data && event.data.type === 'xiaomeihua-new-window' && event.data.url) {
    console.log('Preload捕获到新窗口请求:', event.data.url, event.data.title, event.data.isBottomLink ? '(底部链接)' : '', event.data.isSpecialStoreUrl ? '(特殊商城链接)' : '');
    
    // 处理上架产品页面的特殊情况
    let url = event.data.url;
    let title = event.data.title || '新页面';
    
    if (url.includes('merchant/product') || 
        url.includes('上架产品') || 
        title.includes('上架产品')) {
      console.log('Preload检测到上架产品页面链接，重定向到filehelper');
      url = 'https://filehelper.weixin.qq.com/';
      title = '上架产品';
    }
    
    // 检查是否是特定的微信商城链接
    const storeUrls = [
      'store.weixin.qq.com/shop/home',
      'store.weixin.qq.com/shop/order/list',
      'store.weixin.qq.com/shop/order/detail'
    ];
    
    // 检查是否匹配特定的微信商城链接
    const isSpecialStoreUrl = event.data.isSpecialStoreUrl || storeUrls.some(storeUrl => url.includes(storeUrl));
    
    // 转发消息到渲染进程
    window.dispatchEvent(new CustomEvent('xiaomeihua-open-tab', {
      detail: { 
        url: url, 
        title: title,
        isBottomLink: event.data.isBottomLink || false,
        isSpecialStoreUrl: isSpecialStoreUrl
      }
    }));
    
    // 同时通过IPC发送到主进程，确保消息能被处理
    ipcRenderer.send('new-window-request', {
      url: url,
      title: title,
      isBottomLink: event.data.isBottomLink || false,
      isSpecialStoreUrl: isSpecialStoreUrl
    });
  }
});

// 暴露安全的API给渲染进程
contextBridge.exposeInMainWorld('xiaomeihuaAPI', {
  // 卡密验证 - 添加超时处理
  verifyLicense: (licenseKey) => {
    return Promise.race([
      ipcRenderer.invoke('verify-license', licenseKey),
      new Promise((_, reject) => 
        setTimeout(() => reject(new Error('验证超时，请重试')), 15000)
      )
    ]).catch(err => ({ 
      success: false, 
      message: err.message || '验证失败，请重试' 
    }));
  },
  
  // 获取店铺信息 - 优先使用缓存
  getShopInfo: () => {
    // 先尝试获取缓存的店铺信息
    const cachedInfo = getCachedShopInfo();
    if (cachedInfo && Object.keys(cachedInfo).length > 0) {
      console.log('使用缓存的店铺信息:', cachedInfo);
      return Promise.resolve(cachedInfo);
    }
    
    console.log('缓存中没有店铺信息，从主进程获取');
    // 如果没有缓存，再从主进程获取
    return ipcRenderer.invoke('get-shop-info').then(info => {
      console.log('从主进程获取的店铺信息:', info);
      return info;
    }).catch(err => {
      console.error('获取店铺信息失败:', err);
      // 返回一个默认的店铺信息对象，避免undefined错误
      return {
        license: '',
        shopId: 'unknown',
        shopName: '未知店铺',
        hasCustomerService: true,
        hasProductListing: true
      };
    });
  },
  
  // 获取许可证密钥
  getLicenseKey: () => ipcRenderer.invoke('get-license-key'),

  // 获取已保存的卡密
  getSavedLicense: () => ipcRenderer.invoke('get-license-key'),

  // 检查卡密状态
  checkLicenseStatus: () => ipcRenderer.invoke('check-license-status'),

  // 触发卡密到期强制退出
  triggerLicenseExpiredLogout: () => ipcRenderer.invoke('trigger-license-expired-logout'),

  // 【新增】获取剪贴板内容
  getClipboardText: () => ipcRenderer.invoke('get-clipboard-text'),

  // 获取协议内容
  getAgreement: (type = 'privacy') => ipcRenderer.invoke('get-agreement', type),

  // 获取所有已发布的协议列表
  getPublishedAgreements: () => ipcRenderer.invoke('get-published-agreements'),

  // 打开协议独立窗口
  openAgreementWindow: (agreement) => ipcRenderer.invoke('open-agreement-window', agreement),

  // 打开外部链接
  openExternal: (url) => ipcRenderer.invoke('open-external', url),

  // 打开店铺浏览器
  openShopBrowser: (shopInfo) => ipcRenderer.invoke('open-shop-browser', shopInfo),
  
  // 创建新标签页
  createNewTab: (url, title) => ipcRenderer.invoke('create-new-tab', { url, title }),
  
  // 退出登录
  logout: () => ipcRenderer.invoke('logout'),
  verifyLogoutState: () => ipcRenderer.invoke('verify-logout-state'),
  
  // 打开URL
  openUrl: (url, title) => {
    console.log(`[STEP 3/5] Preload(preload.js) 正在调用ipcRenderer.send('new-window-request')`);
    ipcRenderer.send('new-window-request', {
      url: url,
      title: title || '新页面'
    });
  },
  
  // 【新增】移除店铺
  removeShop: (shopId) => ipcRenderer.invoke('remove-shop', shopId),
  
  // 【新增】打开添加店铺对话框
  openAddShopDialog: () => ipcRenderer.invoke('open-add-shop-dialog'),

  // 【新增】确保店铺session分区存在
  ensureShopSession: (shopId) => ipcRenderer.invoke('ensure-shop-session', shopId),

  // 窗口控制
  closeWindow: () => ipcRenderer.send('close-window'),
  minimizeWindow: () => ipcRenderer.send('minimize-window'),
  maximizeWindow: () => ipcRenderer.send('maximize-window'),

  // 【旧方案】暴露获取preload-browser.js绝对路径的API
  getPreloadBrowserPath: () => ipcRenderer.invoke('get-preload-browser-path'),

  // 【全新修复方案】暴露获取 preload-browser.js 脚本内容的API
  getPreloadScriptContent: () => ipcRenderer.invoke('get-preload-script-content'),

  // 【新增】跨页面执行API
  registerPage: (pageInfo) => ipcRenderer.invoke('register-page', pageInfo),
  unregisterPage: (pageId) => ipcRenderer.invoke('unregister-page', pageId),
  executeCrossPageScript: (scriptData) => ipcRenderer.invoke('execute-cross-page-script', scriptData),
  setSharedData: (key, value) => ipcRenderer.invoke('set-shared-data', key, value),
  getSharedData: (key) => ipcRenderer.invoke('get-shared-data', key),
  autoOpenPage: (pageConfig) => ipcRenderer.invoke('auto-open-page', pageConfig),
  getPageList: () => ipcRenderer.invoke('get-page-list'),
  broadcastMessage: (message) => ipcRenderer.invoke('broadcast-message', message),
  sendToPage: (pageId, message) => ipcRenderer.invoke('send-to-page', pageId, message),

  // 【新增】跨页面消息监听
  onCrossPageMessage: (callback) => {
    return createEventListener('cross-page-message', callback);
  },
  
  // 监听事件 - 使用优化的事件监听函数
  onAutoVerify: (callback) => createEventListener('auto-verify', callback),

  // 【新增】监听自动填充卡密事件（仅填充，不验证）
  onAutoFillLicense: (callback) => createEventListener('auto-fill-license', callback),

  onShopInfo: (callback) => createEventListener('shop-info', callback),
  
  onCreateTab: (callback) => createEventListener('create-tab', callback),
  
  // 高优先级标签页创建
  onCreateTabHighPriority: (callback) => createEventListener('create-tab-high-priority', callback),
  
  // 监听webview新窗口请求
  onWebviewNewWindow: (callback) => {
    const handler = (event) => {
      if (event.detail && event.detail.url) {
        callback(event.detail.url);
      }
    };
    window.addEventListener('xiaomeihua-open-tab', handler);
    return () => window.removeEventListener('xiaomeihua-open-tab', handler);
  },
  
  // 【新增】监听店铺信息更新
  onUpdateShopInfo: (callback) => createEventListener('update-shop-info', callback),

  // 【新增】监听强制店铺检测事件
  onForceShopDetection: (callback) => createEventListener('force-shop-detection', callback),

  // 【新增】监听清理webview缓存事件
  onClearAllWebviewCache: (callback) => createEventListener('clear-all-webview-cache', callback),

  // 【新增】监听完全重置webview事件
  onResetAllWebviews: (callback) => createEventListener('reset-all-webviews', callback),

  // 在API中添加刷新Cookie的方法
  refreshCookies: () => ipcRenderer.send('refresh-cookies'),
  saveCookie: (cookie) => ipcRenderer.send('save-cookie', cookie),
  restoreKeyCookies: () => ipcRenderer.send('restore-key-cookies'),

  // 【新增】处理微信小店重定向错误
  handleWeixinRedirectError: (errorData) => ipcRenderer.invoke('handle-weixin-redirect-error', errorData),

  // 【新增】检查微信小店状态
  checkWeixinStoreStatus: () => ipcRenderer.invoke('check-weixin-store-status'),

  // 【新增】发送页面退出检测事件
  sendPageExitDetected: (exitData) => ipcRenderer.send('page-exit-detected', exitData),

  // 【新增】触发店铺检测事件
  triggerShopDetection: (detectionData) => ipcRenderer.send('trigger-shop-detection', detectionData),

  // 【新增】更新窗口标题
  updateWindowTitle: (title) => ipcRenderer.invoke('update-window-title', title),

  // 【新增】确保后台活动保持功能
  ensureBackgroundActivity: () => {
    console.log('🔄 确保后台活动保持...');

    // 重写页面可见性API，使页面认为自己始终可见
    if (typeof document !== 'undefined') {
      Object.defineProperty(document, 'hidden', {
        get: function() { return false; },
        configurable: true
      });

      Object.defineProperty(document, 'visibilityState', {
        get: function() { return 'visible'; },
        configurable: true
      });

      // 阻止visibilitychange事件
      const originalAddEventListener = document.addEventListener;
      document.addEventListener = function(type, listener, options) {
        if (type === 'visibilitychange') {
          console.log('阻止visibilitychange事件监听器');
          return;
        }
        return originalAddEventListener.call(this, type, listener, options);
      };
    }

    // 确保定时器在后台继续运行
    const originalSetTimeout = window.setTimeout;
    const originalSetInterval = window.setInterval;

    window.setTimeout = function(callback, delay, ...args) {
      return originalSetTimeout.call(window, callback, Math.max(delay, 1), ...args);
    };

    window.setInterval = function(callback, delay, ...args) {
      return originalSetInterval.call(window, callback, Math.max(delay, 1), ...args);
    };

    console.log('✅ 后台活动保持设置完成');
  },

  // 【新增】重新激活功能
  reactivateFeatures: () => {
    console.log('🔄 重新激活所有功能...');

    // 触发自定义事件，通知页面重新激活
    if (typeof window !== 'undefined' && window.dispatchEvent) {
      window.dispatchEvent(new CustomEvent('xiaomeihua-reactivate', {
        detail: { timestamp: Date.now() }
      }));
    }

    console.log('✅ 功能重新激活完成');
  },

  // 版本信息API
  getVersionInfo: () => ipcRenderer.invoke('get-version-info'),

  // 架构信息API
  getArchitectureInfo: () => ipcRenderer.invoke('get-architecture-info'),




});