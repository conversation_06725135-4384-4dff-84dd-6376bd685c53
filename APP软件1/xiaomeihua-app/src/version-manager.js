/**
 * 版本管理模块
 * 统一管理和提供版本信息
 */

const { app } = require('electron');
const path = require('path');
const fs = require('fs');

class VersionManager {
  constructor() {
    this.version = null;
    this.productName = null;
    this.company = '小梅花AI科技';
    this.copyrightYear = '2025';
    this.loadVersionInfo();
  }

  /**
   * 加载版本信息
   */
  loadVersionInfo() {
    try {
      // 优先从package.json读取版本信息
      const packageJsonPath = path.join(__dirname, '..', 'package.json');
      if (fs.existsSync(packageJsonPath)) {
        const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
        this.version = packageJson.version;
        this.productName = packageJson.productName || '小梅花AI智能客服';
      }

      // 如果Electron app可用，也从app获取版本信息作为备份
      if (app && app.getVersion) {
        const appVersion = app.getVersion();
        if (appVersion && !this.version) {
          this.version = appVersion;
        }
      }

      // 默认版本号
      if (!this.version) {
        this.version = '1.0.0';
      }

      if (!this.productName) {
        this.productName = '小梅花AI智能客服';
      }

    } catch (error) {
      console.error('加载版本信息失败:', error);
      // 使用默认值
      this.version = '1.0.0';
      this.productName = '小梅花AI智能客服';
    }
  }

  /**
   * 获取版本号（不带v前缀）
   */
  getVersion() {
    return this.version;
  }

  /**
   * 获取版本字符串（带v前缀）
   */
  getVersionString() {
    return `v${this.version}`;
  }

  /**
   * 获取完整版本信息
   */
  getFullVersionString() {
    return `${this.getVersionString()} © ${this.copyrightYear} ${this.company}`;
  }

  /**
   * 获取产品名称
   */
  getProductName() {
    return this.productName;
  }

  /**
   * 获取产品名称和版本
   */
  getProductNameWithVersion() {
    return `${this.productName} ${this.getVersionString()}`;
  }

  /**
   * 获取公司名称
   */
  getCompany() {
    return this.company;
  }

  /**
   * 获取版权年份
   */
  getCopyrightYear() {
    return this.copyrightYear;
  }

  /**
   * 获取所有版本信息
   */
  getAllVersionInfo() {
    return {
      version: this.getVersion(),
      versionString: this.getVersionString(),
      fullVersionString: this.getFullVersionString(),
      productName: this.getProductName(),
      productNameWithVersion: this.getProductNameWithVersion(),
      company: this.getCompany(),
      copyrightYear: this.getCopyrightYear()
    };
  }

  /**
   * 更新版本号（仅在内存中，不修改文件）
   */
  updateVersion(newVersion) {
    if (newVersion && /^\d+\.\d+\.\d+$/.test(newVersion)) {
      this.version = newVersion;
      return true;
    }
    return false;
  }
}

// 创建单例实例
const versionManager = new VersionManager();

module.exports = versionManager;
