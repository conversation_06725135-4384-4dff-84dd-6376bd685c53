const fs = require('fs');
const path = require('path');
const os = require('os');

/**
 * 清理桌面上的 .xiaomeihua-backup 文件夹
 * 这个脚本会在应用启动时运行，清理旧版本在桌面创建的备份文件夹
 */
class DesktopBackupCleaner {
  constructor() {
    this.userHome = os.homedir();
    this.desktopBackupPath = path.join(this.userHome, 'Desktop', '.xiaomeihua-backup');
  }

  /**
   * 检查桌面备份文件夹是否存在
   */
  checkDesktopBackupExists() {
    try {
      return fs.existsSync(this.desktopBackupPath);
    } catch (error) {
      console.warn('检查桌面备份文件夹失败:', error.message);
      return false;
    }
  }

  /**
   * 备份桌面文件夹中的重要数据到新位置
   */
  async backupImportantData(targetPath) {
    try {
      if (!fs.existsSync(this.desktopBackupPath)) {
        return true;
      }

      // 确保目标目录存在
      if (!fs.existsSync(targetPath)) {
        fs.mkdirSync(targetPath, { recursive: true });
      }

      // 复制重要文件
      const importantFiles = ['login-backup.json', 'cookies.json', 'login-state.json'];
      let copiedFiles = 0;

      for (const fileName of importantFiles) {
        const sourcePath = path.join(this.desktopBackupPath, fileName);
        const targetFilePath = path.join(targetPath, fileName);

        if (fs.existsSync(sourcePath)) {
          try {
            fs.copyFileSync(sourcePath, targetFilePath);
            console.log(`✅ 备份文件已迁移: ${fileName}`);
            copiedFiles++;
          } catch (error) {
            console.warn(`⚠️ 迁移文件失败: ${fileName}`, error.message);
          }
        }
      }

      console.log(`📦 共迁移 ${copiedFiles} 个重要文件到新位置`);
      return true;
    } catch (error) {
      console.error('备份重要数据失败:', error);
      return false;
    }
  }

  /**
   * 删除桌面备份文件夹
   */
  async removeDesktopBackup() {
    try {
      if (!fs.existsSync(this.desktopBackupPath)) {
        console.log('✅ 桌面备份文件夹不存在，无需清理');
        return true;
      }

      // 递归删除文件夹
      this.removeDirectoryRecursive(this.desktopBackupPath);
      console.log('✅ 桌面备份文件夹已清理完成');
      return true;
    } catch (error) {
      console.error('删除桌面备份文件夹失败:', error);
      return false;
    }
  }

  /**
   * 递归删除目录
   */
  removeDirectoryRecursive(dirPath) {
    if (fs.existsSync(dirPath)) {
      const files = fs.readdirSync(dirPath);
      
      for (const file of files) {
        const filePath = path.join(dirPath, file);
        const stat = fs.statSync(filePath);
        
        if (stat.isDirectory()) {
          this.removeDirectoryRecursive(filePath);
        } else {
          fs.unlinkSync(filePath);
        }
      }
      
      fs.rmdirSync(dirPath);
    }
  }

  /**
   * 获取新的备份目录路径
   */
  getNewBackupPath() {
    try {
      const { app } = require('electron');
      const isDev = !app.isPackaged;
      
      if (isDev) {
        return path.join(path.dirname(app.getAppPath()), '.xiaomeihua-backup');
      } else {
        // 尝试获取安装目录
        const possiblePaths = [
          path.dirname(process.execPath),
          path.dirname(app.getAppPath()),
          process.env.PORTABLE_EXECUTABLE_DIR,
          'C:\\Program Files\\小梅花AI智能客服',
          'D:\\小梅花AI智能客服'
        ];

        for (const installPath of possiblePaths) {
          if (installPath && fs.existsSync(installPath)) {
            return path.join(installPath, '.xiaomeihua-backup');
          }
        }

        // 备用路径
        return path.join(path.dirname(process.execPath), '.xiaomeihua-backup');
      }
    } catch (error) {
      console.warn('获取新备份路径失败，使用默认路径:', error.message);
      return path.join('C:\\Program Files\\小梅花AI智能客服', '.xiaomeihua-backup');
    }
  }

  /**
   * 执行完整的清理流程
   */
  async performCleanup() {
    try {
      console.log('🧹 开始清理桌面备份文件夹...');

      // 检查是否存在桌面备份文件夹
      if (!this.checkDesktopBackupExists()) {
        console.log('✅ 桌面备份文件夹不存在，无需清理');
        return true;
      }

      // 获取新的备份路径
      const newBackupPath = this.getNewBackupPath();
      console.log(`📁 新备份路径: ${newBackupPath}`);

      // 备份重要数据到新位置
      const backupSuccess = await this.backupImportantData(newBackupPath);
      if (!backupSuccess) {
        console.warn('⚠️ 数据迁移失败，但将继续清理流程');
      }

      // 删除桌面备份文件夹
      const cleanupSuccess = await this.removeDesktopBackup();
      
      if (cleanupSuccess) {
        console.log('✅ 桌面备份文件夹清理完成');
        console.log(`📦 备份数据已迁移到: ${newBackupPath}`);
      }

      return cleanupSuccess;
    } catch (error) {
      console.error('清理流程失败:', error);
      return false;
    }
  }
}

module.exports = DesktopBackupCleaner;
