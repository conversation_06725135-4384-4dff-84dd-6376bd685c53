/**
 * Windows专用持久化存储系统
 * 专门为exe安装包设计，确保100%可靠的登录状态保持
 */

const { app } = require('electron');
const path = require('path');
const fs = require('fs');
const os = require('os');
const crypto = require('crypto');

class WindowsPersistentStorage {
  constructor() {
    this.isWindows = process.platform === 'win32';
    this.storagePaths = [];
    this.registryPaths = [];
    this.initialized = false;
    this.encryptionKey = 'xiaomeihua-win-storage-2025';
  }

  /**
   * 初始化Windows持久化存储
   */
  async initialize() {
    if (!this.isWindows || this.initialized) return;

    console.log('🔧 初始化Windows专用持久化存储...');

    try {
      // 设置多重存储路径
      await this.setupMultipleStoragePaths();
      
      // 创建所有存储目录
      await this.createAllDirectories();
      
      // 设置Windows注册表存储
      await this.setupRegistryStorage();
      
      // 测试所有存储路径
      await this.testAllStoragePaths();
      
      this.initialized = true;
      console.log('✅ Windows专用持久化存储初始化完成');
    } catch (error) {
      console.error('❌ Windows持久化存储初始化失败:', error);
      throw error;
    }
  }

  /**
   * 设置多重存储路径
   */
  async setupMultipleStoragePaths() {
    const userHome = os.homedir();
    const userName = os.userInfo().username;

    // 获取应用程序安装目录
    const appInstallPath = this.getAppInstallPath();

    // 主存储路径（优先级从高到低）
    this.storagePaths = [
      // 1. 用户AppData Local
      path.join(userHome, 'AppData', 'Local', 'XiaomeihuaAI', 'LoginData'),

      // 2. 用户AppData Roaming
      path.join(userHome, 'AppData', 'Roaming', 'XiaomeihuaAI', 'LoginData'),

      // 3. 用户Documents
      path.join(userHome, 'Documents', 'XiaomeihuaAI', 'LoginData'),

      // 4. 程序安装目录下的数据文件夹
      path.join('C:', 'ProgramData', 'XiaomeihuaAI', 'LoginData'),

      // 5. D盘备份（如果存在）
      ...(fs.existsSync('D:') ? [path.join('D:', 'XiaomeihuaAI', 'LoginData')] : []),

      // 6. 系统临时目录备份
      path.join(os.tmpdir(), 'XiaomeihuaAI', 'LoginData'),

      // 7. 应用程序安装目录备份文件夹（替代桌面备份）
      path.join(appInstallPath, '.xiaomeihua-backup'),
    ];

    console.log(`📁 设置了 ${this.storagePaths.length} 个存储路径`);
  }

  /**
   * 获取应用程序安装目录
   */
  getAppInstallPath() {
    try {
      const { app } = require('electron');
      const isDev = !app.isPackaged;

      if (isDev) {
        // 开发环境：使用项目根目录
        return path.dirname(app.getAppPath());
      } else {
        // 生产环境：尝试获取实际安装目录
        const possiblePaths = [
          path.dirname(process.execPath),  // 可执行文件所在目录
          path.dirname(app.getAppPath()),  // 应用程序路径的父目录
          process.env.PORTABLE_EXECUTABLE_DIR,  // 便携版目录
          'C:\\Program Files\\小梅花AI智能客服',  // 默认安装目录
          'D:\\小梅花AI智能客服'  // D盘安装目录
        ];

        // 查找第一个存在的目录
        for (const installPath of possiblePaths) {
          if (installPath && fs.existsSync(installPath)) {
            console.log(`✅ 找到安装目录: ${installPath}`);
            return installPath;
          }
        }

        // 如果都找不到，使用可执行文件目录
        const fallbackPath = path.dirname(process.execPath);
        console.log(`⚠️ 使用备用安装目录: ${fallbackPath}`);
        return fallbackPath;
      }
    } catch (error) {
      console.warn('⚠️ 获取安装目录失败，使用默认路径:', error.message);
      return 'C:\\Program Files\\小梅花AI智能客服';
    }
  }

  /**
   * 创建所有存储目录
   */
  async createAllDirectories() {
    for (const storagePath of this.storagePaths) {
      try {
        if (!fs.existsSync(storagePath)) {
          fs.mkdirSync(storagePath, { recursive: true });
          
          // 设置目录权限
          await this.setDirectoryPermissions(storagePath);
          
          console.log(`✅ 创建存储目录: ${storagePath}`);
        }
      } catch (error) {
        console.warn(`⚠️ 创建目录失败: ${storagePath}`, error.message);
      }
    }
  }

  /**
   * 设置目录权限
   */
  async setDirectoryPermissions(dirPath) {
    try {
      const { execSync } = require('child_process');
      const username = os.userInfo().username;
      
      // 给当前用户完全控制权限
      execSync(`icacls "${dirPath}" /grant "${username}:(OI)(CI)F" /T`, { stdio: 'ignore' });
      
      // 给所有用户读写权限
      execSync(`icacls "${dirPath}" /grant "Everyone:(OI)(CI)M" /T`, { stdio: 'ignore' });
      
      console.log(`🔐 权限设置完成: ${dirPath}`);
    } catch (error) {
      console.warn(`⚠️ 权限设置失败: ${dirPath}`, error.message);
    }
  }

  /**
   * 设置Windows注册表存储
   */
  async setupRegistryStorage() {
    try {
      this.registryPaths = [
        'HKEY_CURRENT_USER\\Software\\XiaomeihuaAI\\LoginData',
        'HKEY_LOCAL_MACHINE\\Software\\XiaomeihuaAI\\LoginData'
      ];
      
      console.log('📋 注册表存储路径设置完成');
    } catch (error) {
      console.warn('⚠️ 注册表存储设置失败:', error.message);
    }
  }

  /**
   * 测试所有存储路径
   */
  async testAllStoragePaths() {
    const workingPaths = [];
    
    for (const storagePath of this.storagePaths) {
      try {
        const testFile = path.join(storagePath, 'test-write.tmp');
        const testData = 'test-' + Date.now();
        
        fs.writeFileSync(testFile, testData);
        const readData = fs.readFileSync(testFile, 'utf-8');
        
        if (readData === testData) {
          fs.unlinkSync(testFile);
          workingPaths.push(storagePath);
          console.log(`✅ 存储路径测试通过: ${storagePath}`);
        }
      } catch (error) {
        console.warn(`❌ 存储路径测试失败: ${storagePath}`, error.message);
      }
    }

    console.log(`📊 可用存储路径: ${workingPaths.length}/${this.storagePaths.length}`);
    
    if (workingPaths.length === 0) {
      throw new Error('没有可用的存储路径，登录保持功能将无法工作');
    }
  }

  /**
   * 加密数据
   */
  encryptData(data) {
    try {
      const cipher = crypto.createCipher('aes-256-cbc', this.encryptionKey);
      let encrypted = cipher.update(JSON.stringify(data), 'utf8', 'hex');
      encrypted += cipher.final('hex');
      return encrypted;
    } catch (error) {
      console.error('数据加密失败:', error);
      return JSON.stringify(data); // 降级到明文存储
    }
  }

  /**
   * 解密数据
   */
  decryptData(encryptedData) {
    try {
      const decipher = crypto.createDecipher('aes-256-cbc', this.encryptionKey);
      let decrypted = decipher.update(encryptedData, 'hex', 'utf8');
      decrypted += decipher.final('utf8');
      return JSON.parse(decrypted);
    } catch (error) {
      console.warn('数据解密失败，尝试明文解析:', error.message);
      try {
        return JSON.parse(encryptedData); // 尝试明文解析
      } catch (parseError) {
        console.error('明文解析也失败:', parseError);
        return null;
      }
    }
  }

  /**
   * 保存登录状态到所有位置
   */
  async saveLoginState(loginData) {
    if (!this.isWindows || !this.initialized) return false;

    console.log('💾 Windows专用存储：保存登录状态...');

    const stateData = {
      timestamp: Date.now(),
      platform: 'win32',
      version: app.getVersion(),
      hasLogin: true,
      loginData: loginData,
      saveTime: new Date().toISOString(),
      checksum: this.calculateChecksum(loginData)
    };

    let successCount = 0;
    const errors = [];

    // 1. 保存到文件系统
    for (const storagePath of this.storagePaths) {
      try {
        const loginFile = path.join(storagePath, 'login-state.dat');
        const cookiesFile = path.join(storagePath, 'cookies.dat');
        const backupFile = path.join(storagePath, 'login-backup.dat');

        // 加密并保存主数据
        const encryptedData = this.encryptData(stateData);
        fs.writeFileSync(loginFile, encryptedData);

        // 保存Cookie数据
        if (loginData.cookies) {
          const encryptedCookies = this.encryptData(loginData.cookies);
          fs.writeFileSync(cookiesFile, encryptedCookies);
        }

        // 创建备份
        fs.writeFileSync(backupFile, encryptedData);

        // 强制同步到磁盘
        const fd = fs.openSync(loginFile, 'r');
        fs.fsyncSync(fd);
        fs.closeSync(fd);

        successCount++;
        console.log(`✅ 文件存储成功: ${storagePath}`);
      } catch (error) {
        errors.push({ path: storagePath, error: error.message });
        console.warn(`❌ 文件存储失败: ${storagePath}`, error.message);
      }
    }

    // 2. 保存到Windows注册表
    await this.saveToRegistry(stateData);

    // 3. 保存到环境变量（临时存储）
    await this.saveToEnvironment(stateData);

    console.log(`💾 Windows专用存储完成: ${successCount}/${this.storagePaths.length} 个文件路径成功`);
    return successCount > 0;
  }

  /**
   * 保存到Windows注册表
   */
  async saveToRegistry(stateData) {
    try {
      const { execSync } = require('child_process');
      const encryptedData = this.encryptData(stateData);
      
      // 保存到用户注册表
      const userRegCmd = `reg add "HKCU\\Software\\XiaomeihuaAI" /v "LoginState" /t REG_SZ /d "${encryptedData}" /f`;
      execSync(userRegCmd, { stdio: 'ignore' });
      
      console.log('✅ 注册表存储成功');
    } catch (error) {
      console.warn('⚠️ 注册表存储失败:', error.message);
    }
  }

  /**
   * 保存到环境变量
   */
  async saveToEnvironment(stateData) {
    try {
      const encryptedData = this.encryptData(stateData);
      process.env.XIAOMEIHUA_LOGIN_STATE = encryptedData;
      console.log('✅ 环境变量存储成功');
    } catch (error) {
      console.warn('⚠️ 环境变量存储失败:', error.message);
    }
  }

  /**
   * 计算数据校验和
   */
  calculateChecksum(data) {
    try {
      const hash = crypto.createHash('md5');
      hash.update(JSON.stringify(data));
      return hash.digest('hex');
    } catch (error) {
      return 'checksum-error';
    }
  }

  /**
   * 从所有位置恢复登录状态
   */
  async restoreLoginState() {
    if (!this.isWindows || !this.initialized) return null;

    console.log('🔄 Windows专用存储：恢复登录状态...');

    // 1. 尝试从文件系统恢复
    const fileState = await this.restoreFromFiles();
    if (fileState) return fileState;

    // 2. 尝试从注册表恢复
    const registryState = await this.restoreFromRegistry();
    if (registryState) return registryState;

    // 3. 尝试从环境变量恢复
    const envState = await this.restoreFromEnvironment();
    if (envState) return envState;

    console.log('❌ 所有存储位置都无法恢复登录状态');
    return null;
  }

  /**
   * 从文件系统恢复
   */
  async restoreFromFiles() {
    for (const storagePath of this.storagePaths) {
      try {
        const loginFile = path.join(storagePath, 'login-state.dat');
        const backupFile = path.join(storagePath, 'login-backup.dat');

        // 尝试主文件
        if (fs.existsSync(loginFile)) {
          const stateData = await this.loadAndValidateFile(loginFile);
          if (stateData) {
            console.log(`✅ 从主文件恢复成功: ${storagePath}`);
            return stateData;
          }
        }

        // 尝试备份文件
        if (fs.existsSync(backupFile)) {
          const stateData = await this.loadAndValidateFile(backupFile);
          if (stateData) {
            console.log(`✅ 从备份文件恢复成功: ${storagePath}`);
            return stateData;
          }
        }
      } catch (error) {
        console.warn(`⚠️ 文件恢复失败: ${storagePath}`, error.message);
      }
    }
    return null;
  }

  /**
   * 加载并验证文件
   */
  async loadAndValidateFile(filePath) {
    try {
      const encryptedData = fs.readFileSync(filePath, 'utf-8');
      const stateData = this.decryptData(encryptedData);
      
      if (!stateData || !stateData.hasLogin) {
        return null;
      }

      // 检查时效性（24小时内）
      const now = Date.now();
      const stateAge = now - stateData.timestamp;
      const maxAge = 24 * 60 * 60 * 1000;

      if (stateAge > maxAge) {
        console.log(`⚠️ 登录状态已过期: ${filePath}`);
        return null;
      }

      // 验证校验和
      if (stateData.checksum && stateData.loginData) {
        const expectedChecksum = this.calculateChecksum(stateData.loginData);
        if (stateData.checksum !== expectedChecksum) {
          console.warn(`⚠️ 数据校验和不匹配: ${filePath}`);
          return null;
        }
      }

      return stateData;
    } catch (error) {
      console.warn(`⚠️ 文件加载失败: ${filePath}`, error.message);
      return null;
    }
  }

  /**
   * 从注册表恢复
   */
  async restoreFromRegistry() {
    try {
      const { execSync } = require('child_process');
      const result = execSync('reg query "HKCU\\Software\\XiaomeihuaAI" /v "LoginState"', { encoding: 'utf8' });
      
      const match = result.match(/LoginState\s+REG_SZ\s+(.+)/);
      if (match && match[1]) {
        const stateData = this.decryptData(match[1].trim());
        if (stateData && stateData.hasLogin) {
          console.log('✅ 从注册表恢复成功');
          return stateData;
        }
      }
    } catch (error) {
      console.warn('⚠️ 注册表恢复失败:', error.message);
    }
    return null;
  }

  /**
   * 从环境变量恢复
   */
  async restoreFromEnvironment() {
    try {
      const encryptedData = process.env.XIAOMEIHUA_LOGIN_STATE;
      if (encryptedData) {
        const stateData = this.decryptData(encryptedData);
        if (stateData && stateData.hasLogin) {
          console.log('✅ 从环境变量恢复成功');
          return stateData;
        }
      }
    } catch (error) {
      console.warn('⚠️ 环境变量恢复失败:', error.message);
    }
    return null;
  }

  /**
   * 清除所有存储的登录状态
   */
  async clearAllLoginState() {
    if (!this.isWindows) return;

    console.log('🗑️ Windows专用存储：清除所有登录状态...');

    // 清除文件
    for (const storagePath of this.storagePaths) {
      try {
        const files = ['login-state.dat', 'cookies.dat', 'login-backup.dat'];
        for (const file of files) {
          const filePath = path.join(storagePath, file);
          if (fs.existsSync(filePath)) {
            fs.unlinkSync(filePath);
          }
        }
      } catch (error) {
        console.warn(`⚠️ 文件清除失败: ${storagePath}`, error.message);
      }
    }

    // 清除注册表
    try {
      const { execSync } = require('child_process');
      execSync('reg delete "HKCU\\Software\\XiaomeihuaAI" /v "LoginState" /f', { stdio: 'ignore' });
    } catch (error) {
      console.warn('⚠️ 注册表清除失败:', error.message);
    }

    // 清除环境变量
    delete process.env.XIAOMEIHUA_LOGIN_STATE;

    console.log('✅ Windows专用存储清除完成');
  }
}

module.exports = WindowsPersistentStorage;
