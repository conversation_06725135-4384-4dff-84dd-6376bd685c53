/**
 * 跨页面脚本注入增强器
 * 为注入的脚本提供跨页面执行能力和自动页面打开功能
 */

/**
 * 创建跨页面执行环境脚本
 */
function createCrossPageEnvironment(pageInfo = {}) {
  return `
    // 跨页面执行环境初始化
    (function() {
      'use strict';
      
      console.log('🔄 初始化跨页面执行环境...');
      
      // 页面信息
      window.pageInfo = {
        url: window.location.href,
        title: document.title,
        type: '${pageInfo.type || 'unknown'}',
        shopId: '${pageInfo.shopId || ''}',
        capabilities: ${JSON.stringify(pageInfo.capabilities || [])},
        loadedAt: new Date().toISOString()
      };
      
      // 跨页面API对象
      window.CrossPageAPI = {
        // 执行跨页面脚本
        executeScript: async function(scriptData) {
          try {
            const result = await window.xiaomeihuaAPI.executeCrossPageScript(scriptData);
            console.log('跨页面脚本执行结果:', result);
            return result;
          } catch (error) {
            console.error('跨页面脚本执行失败:', error);
            throw error;
          }
        },
        
        // 自动打开页面
        openPage: async function(pageConfig) {
          try {
            const result = await window.xiaomeihuaAPI.autoOpenPage(pageConfig);
            console.log('自动打开页面结果:', result);
            return result;
          } catch (error) {
            console.error('自动打开页面失败:', error);
            throw error;
          }
        },
        
        // 设置共享数据
        setSharedData: async function(key, value) {
          try {
            const result = await window.xiaomeihuaAPI.setSharedData(key, value);
            console.log('设置共享数据成功:', key, value);
            return result;
          } catch (error) {
            console.error('设置共享数据失败:', error);
            throw error;
          }
        },
        
        // 获取共享数据
        getSharedData: async function(key) {
          try {
            const result = await window.xiaomeihuaAPI.getSharedData(key);
            console.log('获取共享数据:', key, result);
            return result;
          } catch (error) {
            console.error('获取共享数据失败:', error);
            throw error;
          }
        },
        
        // 获取页面列表
        getPageList: async function() {
          try {
            const result = await window.xiaomeihuaAPI.getPageList();
            console.log('获取页面列表:', result);
            return result;
          } catch (error) {
            console.error('获取页面列表失败:', error);
            throw error;
          }
        },
        
        // 广播消息
        broadcast: async function(message) {
          try {
            const result = await window.xiaomeihuaAPI.broadcastMessage(message);
            console.log('广播消息成功:', message);
            return result;
          } catch (error) {
            console.error('广播消息失败:', error);
            throw error;
          }
        },
        
        // 发送消息到指定页面
        sendToPage: async function(pageId, message) {
          try {
            const result = await window.xiaomeihuaAPI.sendToPage(pageId, message);
            console.log('发送消息到页面成功:', pageId, message);
            return result;
          } catch (error) {
            console.error('发送消息到页面失败:', error);
            throw error;
          }
        },
        
        // 等待页面加载
        waitForPage: function(selector, timeout = 10000) {
          return new Promise((resolve, reject) => {
            const startTime = Date.now();
            
            function check() {
              const element = document.querySelector(selector);
              if (element) {
                resolve(element);
                return;
              }
              
              if (Date.now() - startTime > timeout) {
                reject(new Error('等待页面元素超时: ' + selector));
                return;
              }
              
              setTimeout(check, 100);
            }
            
            check();
          });
        },
        
        // 等待页面完全加载
        waitForPageReady: function(timeout = 30000) {
          return new Promise((resolve, reject) => {
            if (document.readyState === 'complete') {
              resolve();
              return;
            }
            
            const timer = setTimeout(() => {
              reject(new Error('等待页面加载超时'));
            }, timeout);
            
            window.addEventListener('load', () => {
              clearTimeout(timer);
              resolve();
            });
          });
        },
        
        // 页面跳转
        navigateTo: function(url, newTab = false) {
          if (newTab) {
            return this.openPage({ url: url, title: '新页面' });
          } else {
            window.location.href = url;
          }
        },
        
        // 获取当前页面信息
        getCurrentPageInfo: function() {
          return window.pageInfo;
        }
      };
      
      // 自动页面打开器
      window.AutoPageOpener = {
        // 打开页面并等待加载
        openAndWait: async function(url, options = {}) {
          const {
            title = '自动打开页面',
            waitForSelector = '',
            executeAfterLoad = '',
            timeout = 30000,
            delay = 0
          } = options;
          
          console.log('自动打开页面并等待:', url);
          
          const pageConfig = {
            url: url,
            title: title,
            shopId: window.pageInfo.shopId,
            waitForLoad: true,
            executeAfterLoad: executeAfterLoad,
            delay: delay
          };
          
          const result = await window.CrossPageAPI.openPage(pageConfig);
          
          if (waitForSelector) {
            // 等待新页面中的特定元素
            return new Promise((resolve, reject) => {
              const checkInterval = setInterval(async () => {
                try {
                  const pages = await window.CrossPageAPI.getPageList();
                  const targetPage = pages.find(p => p.url.includes(url));
                  
                  if (targetPage) {
                    // 检查目标页面是否有指定元素
                    const checkScript = \`
                      document.querySelector('\${waitForSelector}') !== null
                    \`;
                    
                    const checkResult = await window.CrossPageAPI.executeScript({
                      script: checkScript,
                      targetPages: [targetPage.id]
                    });
                    
                    if (checkResult.success && checkResult.results[0]?.result?.result) {
                      clearInterval(checkInterval);
                      resolve(targetPage);
                    }
                  }
                } catch (error) {
                  console.error('检查页面元素失败:', error);
                }
              }, 1000);
              
              setTimeout(() => {
                clearInterval(checkInterval);
                reject(new Error('等待页面元素超时'));
              }, timeout);
            });
          }
          
          return result;
        },
        
        // 批量打开页面
        openMultiple: async function(urls, options = {}) {
          const {
            sequential = false,
            delay = 1000,
            executeAfterAll = ''
          } = options;
          
          console.log('批量打开页面:', urls);
          
          const results = [];
          
          if (sequential) {
            // 顺序打开
            for (const url of urls) {
              const result = await this.openAndWait(url, options);
              results.push(result);
              
              if (delay > 0) {
                await new Promise(resolve => setTimeout(resolve, delay));
              }
            }
          } else {
            // 并行打开
            const promises = urls.map(url => this.openAndWait(url, options));
            const parallelResults = await Promise.allSettled(promises);
            results.push(...parallelResults.map(r => r.value || r.reason));
          }
          
          // 执行完成后的脚本
          if (executeAfterAll) {
            await window.CrossPageAPI.executeScript({
              script: executeAfterAll,
              targetType: 'all'
            });
          }
          
          return results;
        }
      };
      
      // 跨页面数据同步器
      window.DataSyncer = {
        // 同步数据到所有页面
        syncToAll: async function(data) {
          const timestamp = new Date().toISOString();
          
          for (const [key, value] of Object.entries(data)) {
            await window.CrossPageAPI.setSharedData(key, {
              value: value,
              timestamp: timestamp,
              source: window.pageInfo.url
            });
          }
          
          // 广播同步事件
          await window.CrossPageAPI.broadcast({
            type: 'data-sync',
            data: data,
            timestamp: timestamp,
            source: window.pageInfo.url
          });
        },
        
        // 从共享数据获取最新数据
        getLatest: async function(keys) {
          const result = {};
          
          for (const key of keys) {
            const data = await window.CrossPageAPI.getSharedData(key);
            if (data) {
              result[key] = data.value;
            }
          }
          
          return result;
        }
      };
      
      // 监听跨页面消息
      if (window.xiaomeihuaAPI && window.xiaomeihuaAPI.onCrossPageMessage) {
        window.xiaomeihuaAPI.onCrossPageMessage((message) => {
          console.log('收到跨页面消息:', message);
          
          // 触发自定义事件
          const event = new CustomEvent('crossPageMessage', {
            detail: message
          });
          window.dispatchEvent(event);
          
          // 处理数据同步消息
          if (message.type === 'data-sync') {
            console.log('处理数据同步:', message.data);
            
            // 触发数据同步事件
            const syncEvent = new CustomEvent('dataSyncReceived', {
              detail: {
                data: message.data,
                timestamp: message.timestamp,
                source: message.source
              }
            });
            window.dispatchEvent(syncEvent);
          }
        });
      }
      
      // 注册当前页面
      if (window.xiaomeihuaAPI && window.xiaomeihuaAPI.registerPage) {
        window.xiaomeihuaAPI.registerPage(window.pageInfo).then(result => {
          if (result.success) {
            window.pageInfo.id = result.pageId;
            console.log('页面注册成功:', result.pageId);
            
            // 触发页面注册完成事件
            const event = new CustomEvent('pageRegistered', {
              detail: { pageId: result.pageId, pageInfo: window.pageInfo }
            });
            window.dispatchEvent(event);
          }
        }).catch(error => {
          console.error('页面注册失败:', error);
        });
      }
      
      // 页面卸载时注销
      window.addEventListener('beforeunload', () => {
        if (window.pageInfo.id && window.xiaomeihuaAPI && window.xiaomeihuaAPI.unregisterPage) {
          window.xiaomeihuaAPI.unregisterPage(window.pageInfo.id);
        }
      });
      
      console.log('✅ 跨页面执行环境初始化完成');
      
      // 触发环境就绪事件
      const readyEvent = new CustomEvent('crossPageEnvironmentReady', {
        detail: {
          pageInfo: window.pageInfo,
          api: window.CrossPageAPI,
          autoOpener: window.AutoPageOpener,
          dataSyncer: window.DataSyncer
        }
      });
      window.dispatchEvent(readyEvent);
      
    })();
  `;
}

/**
 * 创建脚本自动打开页面的辅助函数
 */
function createAutoPageOpenerHelpers() {
  return `
    // 脚本自动打开页面辅助函数
    window.ScriptHelpers = window.ScriptHelpers || {};
    
    // 简化的页面打开函数
    window.ScriptHelpers.openPage = async function(url, options = {}) {
      return await window.AutoPageOpener.openAndWait(url, options);
    };
    
    // 简化的跨页面执行函数
    window.ScriptHelpers.executeOnPage = async function(pageUrl, script) {
      return await window.CrossPageAPI.executeScript({
        script: script,
        targetUrl: pageUrl
      });
    };
    
    // 简化的数据共享函数
    window.ScriptHelpers.shareData = async function(key, value) {
      return await window.CrossPageAPI.setSharedData(key, value);
    };
    
    window.ScriptHelpers.getData = async function(key) {
      return await window.CrossPageAPI.getSharedData(key);
    };
    
    // 等待元素出现
    window.ScriptHelpers.waitForElement = function(selector, timeout = 10000) {
      return window.CrossPageAPI.waitForPage(selector, timeout);
    };
    
    // 页面跳转
    window.ScriptHelpers.goto = function(url, newTab = false) {
      return window.CrossPageAPI.navigateTo(url, newTab);
    };
    
    console.log('✅ 脚本辅助函数已加载');
  `;
}

module.exports = {
  createCrossPageEnvironment,
  createAutoPageOpenerHelpers
};
