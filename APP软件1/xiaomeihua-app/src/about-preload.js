const { contextBridge, ipcRenderer } = require('electron');

// 向渲染进程暴露安全的API
contextBridge.exposeInMainWorld('electronAPI', {
  // 关闭关于窗口
  closeAboutWindow: () => {
    ipcRenderer.send('close-about-window');
  },

  // 最小化关于窗口
  minimizeAboutWindow: () => {
    ipcRenderer.send('minimize-about-window');
  },

  // 打开协议弹窗
  openAgreement: (agreement) => {
    ipcRenderer.send('open-agreement', agreement);
  },

  // 请求版本信息
  requestVersionInfo: () => {
    ipcRenderer.send('request-version-info');
  },

  // 请求协议信息
  requestAgreementsInfo: () => {
    ipcRenderer.send('request-agreements-info');
  },

  // 监听版本信息
  onVersionInfo: (callback) => {
    ipcRenderer.on('version-info', (event, version) => {
      callback(version);
    });
  },

  // 监听协议信息
  onAgreementsInfo: (callback) => {
    ipcRenderer.on('agreements-info', (event, agreements) => {
      callback(agreements);
    });
  }
});

// 防止拖拽文件到窗口
document.addEventListener('DOMContentLoaded', () => {
  document.addEventListener('dragover', (e) => {
    e.preventDefault();
  });
  
  document.addEventListener('drop', (e) => {
    e.preventDefault();
  });
});