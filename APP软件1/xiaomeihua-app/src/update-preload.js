const { contextBridge, ipcRenderer } = require('electron');

// 暴露更新相关的API到渲染进程
contextBridge.exposeInMainWorld('updateAPI', {
  // 接收更新信息
  onUpdateInfo: (callback) => {
    ipcRenderer.on('update-info', (event, data) => callback(data));
  },

  // 接收下载进度
  onDownloadProgress: (callback) => {
    ipcRenderer.on('download-progress', (event, data) => callback(data));
  },

  // 接收下载开始
  onStartDownload: (callback) => {
    ipcRenderer.on('start-download', () => callback());
  },

  // 接收下载完成
  onDownloadComplete: (callback) => {
    ipcRenderer.on('download-complete', () => callback());
  },

  // 接收安装开始
  onStartInstall: (callback) => {
    ipcRenderer.on('start-install', () => callback());
  },

  // 接收安装完成
  onInstallComplete: (callback) => {
    ipcRenderer.on('install-complete', () => callback());
  },

  // 接收下载错误
  onDownloadError: (callback) => {
    ipcRenderer.on('download-error', (event, error) => callback(error));
  },

  // 开始更新
  startUpdate: () => {
    ipcRenderer.send('start-update');
  },

  // 取消更新
  cancelUpdate: () => {
    ipcRenderer.send('cancel-update');
  },

  // 关闭更新窗口
  closeUpdateWindow: () => {
    ipcRenderer.send('close-update-window');
  },

  // 重启应用
  restartApp: () => {
    ipcRenderer.send('restart-app');
  },

  // 最小化窗口
  minimizeWindow: () => {
    ipcRenderer.send('minimize-update-window');
  },

  // 开始拖拽
  startDrag: () => {
    ipcRenderer.send('start-drag');
  },

  // 获取平台信息
  getPlatform: () => {
    return process.platform === 'win32' ? 'windows' :
           process.platform === 'darwin' ? 'macos' :
           process.platform === 'linux' ? 'linux' : 'other';
  }
});