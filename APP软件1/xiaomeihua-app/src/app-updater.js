const { autoUpdater } = require('electron-updater');
const { app, dialog, shell, BrowserWindow } = require('electron');
const fs = require('fs');
const path = require('path');
const { execSync, spawn } = require('child_process');
const axios = require('axios');

class AppUpdater {
  constructor() {
    this.updateWindow = null;
    this.isChecking = false;
    this.downloadProgress = 0;
    this.isDownloading = false;
    this.updateInfo = null;
    this.downloadUrl = null;
    this.downloadPath = null;
    this.logFile = path.join(app.getPath('userData'), 'update.log');
    this.downloadDir = path.join(app.getPath('userData'), 'downloads');

    // 更新状态管理
    this.updateStatusFile = path.join(app.getPath('userData'), 'update_status.json');
    this.lastUpdateCheck = this.loadUpdateStatus();

    // 确保下载目录存在
    if (!fs.existsSync(this.downloadDir)) {
      fs.mkdirSync(this.downloadDir, { recursive: true });
    }

    // 【新增】启动时清理过期临时文件
    this.cleanupTemporaryFiles();

    this.setupEventListeners();
    this.log('AppUpdater 初始化完成');
  }

  log(message) {
    const timestamp = new Date().toISOString();
    const logMessage = `[${timestamp}] ${message}\n`;
    console.log(`[AppUpdater] ${message}`);

    try {
      fs.appendFileSync(this.logFile, logMessage);
    } catch (error) {
      console.error('写入日志失败:', error);
    }
  }

  // 加载更新状态
  loadUpdateStatus() {
    try {
      if (fs.existsSync(this.updateStatusFile)) {
        const data = fs.readFileSync(this.updateStatusFile, 'utf8');
        return JSON.parse(data);
      }
    } catch (error) {
      this.log(`加载更新状态失败: ${error.message}`);
    }
    return {
      lastVersion: null,
      lastCheckTime: null,
      updateCompleted: false
    };
  }

  // 保存更新状态
  saveUpdateStatus(status) {
    try {
      fs.writeFileSync(this.updateStatusFile, JSON.stringify(status, null, 2));
      this.log(`更新状态已保存: ${JSON.stringify(status)}`);
    } catch (error) {
      this.log(`保存更新状态失败: ${error.message}`);
    }
  }

  // 标记更新完成
  markUpdateCompleted(version) {
    const status = {
      lastVersion: version,
      lastCheckTime: new Date().toISOString(),
      updateCompleted: true,
      completedTimestamp: Date.now()  // 添加时间戳用于更精确的时间控制
    };
    this.saveUpdateStatus(status);
    this.lastUpdateCheck = status;
    this.log(`✅ 更新完成标记已保存: ${version} (时间戳: ${status.completedTimestamp})`);
  }

  setupEventListeners() {
    // 配置自动更新器
    autoUpdater.autoDownload = false;
    autoUpdater.autoInstallOnAppQuit = false;

    // 设置IPC事件监听器
    this.setupIpcListeners();

    // 监听更新事件
    autoUpdater.on('checking-for-update', () => {
      this.log('开始检查更新...');
    });

    autoUpdater.on('update-available', (info) => {
      this.log(`发现新版本: ${info.version}`);
      this.updateInfo = info;
    });

    autoUpdater.on('update-not-available', (info) => {
      this.log('当前已是最新版本');
    });

    autoUpdater.on('error', (err) => {
      this.log(`更新检查失败: ${err.message}`);
    });

    autoUpdater.on('download-progress', (progressObj) => {
      this.downloadProgress = Math.round(progressObj.percent);
      this.log(`下载进度: ${this.downloadProgress}%`);
      
      if (this.updateWindow && !this.updateWindow.isDestroyed()) {
        this.updateWindow.webContents.send('download-progress', {
          percent: this.downloadProgress,
          bytesPerSecond: progressObj.bytesPerSecond,
          total: progressObj.total,
          transferred: progressObj.transferred
        });
      }
    });

    autoUpdater.on('update-downloaded', (info) => {
      this.log('更新下载完成');
      this.isDownloading = false;

      if (this.updateWindow && !this.updateWindow.isDestroyed()) {
        this.updateWindow.webContents.send('download-complete');
      }
    });
  }

  setupIpcListeners() {
    const { ipcMain } = require('electron');

    // 监听开始更新
    ipcMain.on('start-update', () => {
      this.log('收到开始更新请求');
      this.startUpdate();
    });

    // 监听取消更新
    ipcMain.on('cancel-update', () => {
      this.log('收到取消更新请求');
      this.cancelUpdate();
    });

    // 监听关闭更新窗口
    ipcMain.on('close-update-window', () => {
      this.log('收到关闭更新窗口请求');

      // 检查是否为强制更新
      if (this.updateInfo && this.updateInfo.mandatory) {
        this.log('强制更新：用户尝试关闭更新窗口，退出应用');
        const { app } = require('electron');
        app.quit();
        return;
      }

      this.closeUpdateWindow();
    });

    // 监听重启应用
    ipcMain.on('restart-app', () => {
      this.log('收到重启应用请求');
      this.restartApp();
    });

    // 监听最小化窗口
    ipcMain.on('minimize-update-window', () => {
      this.log('收到最小化窗口请求');
      if (this.updateWindow && !this.updateWindow.isDestroyed()) {
        this.updateWindow.minimize();
      }
    });
  }

  closeUpdateWindow() {
    if (this.updateWindow && !this.updateWindow.isDestroyed()) {
      this.updateWindow.close();
    }
  }

  cancelUpdate() {
    // 取消更新逻辑
    this.isDownloading = false;
    this.closeUpdateWindow();
  }

  restartApp() {
    const { app } = require('electron');
    app.relaunch();
    app.exit();
  }

  /**
   * 【关键修复】获取可靠的当前版本号
   * 优先从package.json读取，确保版本号与构建时一致
   */
  getCurrentVersionReliably() {
    try {
      // 方法1：尝试从package.json读取版本号
      const packageJsonPath = path.join(__dirname, '..', 'package.json');
      if (fs.existsSync(packageJsonPath)) {
        const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
        if (packageJson.version && this.isValidVersionFormat(packageJson.version)) {
          this.log(`📱 从package.json获取版本号: ${packageJson.version}`);
          return packageJson.version;
        }
      }

      // 方法2：从Electron app获取版本号（备用方案）
      const appVersion = app.getVersion();
      if (appVersion && this.isValidVersionFormat(appVersion)) {
        this.log(`📱 从Electron app获取版本号: ${appVersion}`);
        return appVersion;
      }

      // 方法3：从版本管理器获取（最后的备用方案）
      try {
        const versionManager = require('./version-manager');
        const vmVersion = versionManager.getVersion();
        if (vmVersion && this.isValidVersionFormat(vmVersion)) {
          this.log(`📱 从版本管理器获取版本号: ${vmVersion}`);
          return vmVersion;
        }
      } catch (vmError) {
        this.log(`版本管理器获取失败: ${vmError.message}`);
      }

      // 默认版本号
      this.log('⚠️ 无法获取有效版本号，使用默认版本1.0.0');
      return '1.0.0';

    } catch (error) {
      this.log(`获取版本号失败: ${error.message}`);
      return app.getVersion() || '1.0.0';
    }
  }

  /**
   * 精准检测当前平台和架构
   * 返回对应的API端点URL
   */
  getPlatformInfo() {
    const platform = process.platform;
    const arch = process.arch;

    this.log(`🔍 检测平台信息: platform=${platform}, arch=${arch}`);

    if (platform === 'win32') {
      return {
        platform: 'windows',
        architecture: 'x64',
        apiUrl: 'https://xiaomeihuakefu.cn/api/app_update_windows.php',
        platformKey: 'windows'
      };
    } else if (platform === 'darwin') {
      if (arch === 'arm64') {
        // M系列芯片 - 使用专用M芯片API
        return {
          platform: 'macos',
          architecture: 'm1',
          apiUrl: 'https://xiaomeihuakefu.cn/api/app_update_macos_m1.php',
          platformKey: 'macos_m1'
        };
      } else {
        // Intel芯片 - 使用专用API端点
        return {
          platform: 'macos',
          architecture: 'intel',
          apiUrl: 'https://xiaomeihuakefu.cn/api/app_update_macos_intel.php',
          platformKey: 'macos_intel'
        };
      }
    } else {
      throw new Error(`不支持的平台: ${platform}`);
    }
  }

  /**
   * 从后台API获取更新信息
   */
  async getUpdateInfoFromBackend() {
    try {
      // 【关键修复】获取当前版本号时优先从package.json读取，确保版本号一致性
      const currentVersion = this.getCurrentVersionReliably();
      const platformInfo = this.getPlatformInfo();
      
      this.log(`📡 开始检查更新: ${platformInfo.platformKey}`);
      this.log(`📱 当前版本: ${currentVersion}`);
      this.log(`🌐 API地址: ${platformInfo.apiUrl}`);
      this.log(`🏗️  架构信息: ${platformInfo.architecture} (${platformInfo.platform})`);
      
      // 构造请求参数
      const params = {
        action: 'check',
        version: currentVersion,
        platform: platformInfo.platformKey,  // 添加平台信息
        architecture: platformInfo.architecture  // 添加架构信息
      };
      
      // 设置请求配置
      const config = {
        method: 'GET',
        url: platformInfo.apiUrl,
        params: params,
        timeout: 15000, // 15秒超时
        headers: {
          'User-Agent': `XiaoMeiHua-App/${currentVersion} (${platformInfo.platform}; ${platformInfo.architecture})`,
          'Accept': 'application/json',
          'Cache-Control': 'no-cache'
        }
      };
      
      this.log(`🚀 发送请求: ${JSON.stringify(params)}`);
      
      // 发送请求
      const response = await axios(config);
      
      this.log(`📥 收到响应: status=${response.status}`);
      this.log(`📄 响应数据: ${JSON.stringify(response.data, null, 2)}`);
      
      // 验证响应格式
      if (!response.data || typeof response.data !== 'object') {
        throw new Error('API响应格式无效');
      }
      
      // 处理API错误响应
      if (!response.data.success) {
        const errorMsg = response.data.message || '后台返回错误';
        this.log(`❌ API返回错误: ${errorMsg}`);
        
        // 如果是数据库相关错误，标记为暂时性错误，不触发更新
        if (errorMsg.includes('SQLSTATE') || errorMsg.includes('Column not found') || 
            errorMsg.includes('数据库') || errorMsg.includes('检查更新失败')) {
          this.log('🚫 检测到数据库错误，跳过此次更新检查');
          return {
            hasUpdate: false,
            currentVersion: currentVersion,
            latestVersion: currentVersion,
            skipReason: 'database_error',
            errorMessage: errorMsg
          };
        }
        
        throw new Error(errorMsg);
      }
      
      const data = response.data.data;
      if (!data) {
        throw new Error('API响应缺少data字段');
      }
      
      // 检查是否有更新
      const hasUpdate = data.has_update;
      this.log(`🔍 更新检查结果: ${hasUpdate ? '发现新版本' : '已是最新版本'}`);
      this.log(`📊 API返回数据: 当前版本=${data.current_version}, 最新版本=${data.latest_version}`);
      
      if (hasUpdate && data.update_info) {
        const updateInfo = data.update_info;
        
        // 获取版本号信息
        const latestVersion = data.latest_version;
        
        // 【关键修复】信任后台API的版本比较结果，移除客户端重复检查
        // 后台API已经进行了正确的版本比较，客户端只需要进行基本验证
        
        // 基本验证：版本号格式检查
        if (!latestVersion || !this.isValidVersionFormat(latestVersion)) {
          this.log(`⚠️ 发现无效版本号: ${latestVersion}，跳过更新`);
          return {
            hasUpdate: false,
            currentVersion: currentVersion,
            latestVersion: currentVersion,
            skipReason: 'invalid_version_format',
            errorMessage: `无效的版本号格式: ${latestVersion}`
          };
        }

        // 获取下载链接
        let downloadUrl = null;
        let backupUrl = null;

        // 处理download_urls字段（新格式）
        if (updateInfo.download_urls) {
          if (platformInfo.architecture === 'm1') {
            // M芯片用户：优先使用M芯片专用链接
            downloadUrl = updateInfo.download_urls.macos_m1;
            this.log(`🍎 M芯片用户，查找专用下载链接: ${downloadUrl ? '找到' : '未找到'}`);
          } else if (platformInfo.architecture === 'intel') {
            // Intel芯片用户：优先使用Intel专用链接，fallback到通用macOS链接
            downloadUrl = updateInfo.download_urls.macos_intel || updateInfo.download_urls.macos;
            this.log(`💻 Intel芯片用户，使用下载链接: ${downloadUrl ? 'Intel专用或通用' : '未找到'}`);
          } else if (platformInfo.platform === 'windows') {
            downloadUrl = updateInfo.download_urls.windows;
            this.log(`🪟 Windows用户，使用下载链接: ${downloadUrl ? '找到' : '未找到'}`);
          }
        }
        
        // 兼容旧格式：直接使用download_url字段
        if (!downloadUrl && updateInfo.download_url) {
          downloadUrl = updateInfo.download_url;
          this.log(`🔄 使用兼容格式下载链接: ${downloadUrl}`);
        }
        
        // 获取备用下载链接
        if (updateInfo.backup_url) {
          backupUrl = updateInfo.backup_url;
        }

        // 验证下载链接
        if (!downloadUrl) {
          this.log(`⚠️ 没有找到适合的下载链接，跳过更新`);
          return {
            hasUpdate: false,
            currentVersion: currentVersion,
            latestVersion: latestVersion,
            skipReason: 'no_download_url',
            errorMessage: '没有找到适合的下载链接'
          };
        }

        this.log(`📦 新版本信息:`);
        this.log(`   版本号: ${latestVersion}`);
        this.log(`   标题: ${updateInfo.title}`);
        this.log(`   强制更新: ${updateInfo.force_update}`);
        this.log(`   下载链接: ${downloadUrl}`);
        this.log(`   备用链接: ${backupUrl || updateInfo.backup_url || '无'}`);

        return {
          hasUpdate: true,
          updateInfo: {
            version: latestVersion,
            title: updateInfo.title,
            description: updateInfo.description,
            releaseNotes: updateInfo.release_notes,
            forceUpdate: updateInfo.force_update,
            downloadUrl: downloadUrl,
            backupUrl: backupUrl || updateInfo.backup_url,
            createdAt: updateInfo.created_at,
            platform: platformInfo.platform,
            architecture: platformInfo.architecture
          }
        };
      } else {
        return {
          hasUpdate: false,
          currentVersion: currentVersion,
          latestVersion: data.latest_version
        };
      }
      
    } catch (error) {
      this.log(`❌ API请求失败: ${error.message}`);
      if (error.response) {
        this.log(`HTTP状态: ${error.response.status}`);
        this.log(`响应数据: ${JSON.stringify(error.response.data)}`);
        
        // 对于500错误（服务器内部错误），不应该触发更新
        if (error.response.status === 500) {
          this.log('🚫 服务器内部错误，跳过此次更新检查');
          return {
            hasUpdate: false,
            currentVersion: app.getVersion(),
            latestVersion: app.getVersion(),
            skipReason: 'server_error',
            errorMessage: error.message
          };
        }
      }
      throw error;
    }
  }

  async checkForUpdates(retryCount = 0) {
    const maxRetries = 2; // 最大重试次数
    
    if (this.isChecking) {
      this.log('正在检查更新中，跳过重复检查');
      return false;
    }

    try {
      this.isChecking = true;
      
      // 检查是否刚刚完成更新（防止循环更新提示）
      // 【关键修复】使用可靠的版本号获取方法
      const currentVersion = this.getCurrentVersionReliably();
      if (this.detectUpdateCompletion(currentVersion)) {
        this.log('✅ 检测到刚完成更新，跳过此次检查避免循环提示');
        this.isChecking = false;
        return false;
      }
      
      // 从后台API获取更新信息
      const result = await this.getUpdateInfoFromBackend();
      
      // 检查是否因为错误而跳过
      if (result.skipReason) {
        this.log(`⚠️ 跳过更新检查，原因: ${result.skipReason}`);
        this.log(`💡 错误信息: ${result.errorMessage}`);
        this.isChecking = false;
        return false;
      }
      
      if (result.hasUpdate) {
        this.updateInfo = result.updateInfo;
        
        // 显示更新弹窗
        this.showUpdateWindow();
        
        this.log(`✅ 发现更新: ${result.updateInfo.version}`);
        this.isChecking = false;
        return true;
      } else {
        this.log(`✅ 已是最新版本: ${currentVersion}`);
        this.isChecking = false;
        return false;
      }
      
    } catch (error) {
      this.log(`❌ 检查更新失败 (尝试 ${retryCount + 1}/${maxRetries + 1}): ${error.message}`);
      
      // 如果是网络相关错误并且还有重试机会，则进行重试
      if (retryCount < maxRetries && (
        error.code === 'ECONNRESET' || 
        error.code === 'ECONNREFUSED' || 
        error.code === 'ETIMEDOUT' ||
        error.message.includes('timeout') ||
        error.message.includes('Network Error') ||
        error.message.includes('connect ECONNREFUSED')
      )) {
        this.log(`🔄 网络错误，${3}秒后进行第${retryCount + 2}次重试...`);
        this.isChecking = false;
        
        // 等待3秒后重试
        setTimeout(() => {
          this.checkForUpdates(retryCount + 1);
        }, 3000);
        
        return false;
      }
      
      this.log('⚠️ 由于API错误，不显示更新弹窗，避免误导用户');
      this.isChecking = false;
      return false;
    }
  }

  /**
   * 【彻底修复】智能检测应用是否刚完成更新 - 解决重复更新提示的致命bug
   * 这是重启后执行的逻辑，必须使用重启后的真实版本号
   */
  detectUpdateCompletion(currentVersion) {
    try {
      this.log(`🔍 开始检测更新完成状态，当前版本: ${currentVersion}`);
      
      // 【第零步】增强的冷却期检查，延长到20分钟
      const completionCooldown = this.checkUpdateCompletionCooldownEnhanced();
      if (completionCooldown.inCooldown) {
        this.log(`❄️ 处于更新完成冷却期，跳过检查。剩余: ${completionCooldown.remainingTime}秒`);
        return true;
      }
      
      // 【第一步】检查应用启动时间，如果是刚启动（10秒内），可能是刚更新完重启
      const appStartTime = Date.now() - (process.uptime() * 1000);
      const timeSinceStart = Date.now() - appStartTime;
      if (timeSinceStart < 10000) { // 10秒内启动
        this.log(`🚀 应用刚启动${Math.round(timeSinceStart/1000)}秒，可能是更新后重启`);
        // 检查是否有版本变化的迹象
        if (this.lastUpdateCheck.lastVersion && 
            this.lastUpdateCheck.lastVersion !== currentVersion) {
          this.log(`✅ 检测到版本变化且应用刚启动，标记更新完成`);
          this.markUpdateCompleted(currentVersion);
          this.setUpdateCompletionCooldownEnhanced(currentVersion);
          return true;
        }
      }
      
      // 【第二步】检查是否有待处理的更新完成标记
      const pendingUpdate = this.checkPendingUpdateCompletion();
      if (pendingUpdate) {
        this.log(`📋 发现待处理的更新完成标记，预期版本: ${pendingUpdate.expectedVersion}`);
        
        // 【关键修复】使用增强的版本匹配检查
        if (this.isVersionMatch(currentVersion, pendingUpdate.expectedVersion)) {
          this.log(`✅ 版本匹配确认，更新确实完成: ${currentVersion}`);
          // 使用重启后的真实版本号标记更新完成
          this.markUpdateCompleted(currentVersion);
          // 清除待处理的更新标记
          this.clearPendingUpdateMark();
          // 【新增】设置更新完成冷却期
          this.setUpdateCompletionCooldownEnhanced(currentVersion);
          return true;
        } else if (this.isNewerVersion(currentVersion, pendingUpdate.expectedVersion)) {
          // 【新增】当前版本比预期版本更新的情况
          this.log(`🎉 当前版本比预期版本更新: 当前${currentVersion} > 预期${pendingUpdate.expectedVersion}`);
          this.markUpdateCompleted(currentVersion);
          this.clearPendingUpdateMark();
          this.setUpdateCompletionCooldownEnhanced(currentVersion);
          return true;
        } else {
          this.log(`⚠️ 版本不匹配，可能更新失败: 期望${pendingUpdate.expectedVersion}, 实际${currentVersion}`);
          // 【修复】延长待处理标记的有效期，给更新过程更多时间
          const timeDiff = Date.now() - pendingUpdate.timestamp;
          if (timeDiff < 10 * 60 * 1000) { // 延长到10分钟内不清除，给更新过程充足时间
            this.log(`⏰ 更新可能仍在进行中，保留待处理标记 (${Math.round(timeDiff/1000)}秒)`);
            return false; // 不标记为完成，但也不清除标记
          } else {
            this.log(`⏰ 待处理标记已超时，清除无效标记`);
            this.clearPendingUpdateMark();
          }
        }
      }
      
      // 【第三步】检查版本是否比上次记录的版本更新
      if (this.lastUpdateCheck.lastVersion &&
          this.isNewerVersion(currentVersion, this.lastUpdateCheck.lastVersion)) {
        this.log(`✅ 检测到版本已更新 (${this.lastUpdateCheck.lastVersion} -> ${currentVersion})`);
        this.log(`📱 使用重启后的真实版本号标记更新完成: ${currentVersion}`);
        this.markUpdateCompleted(currentVersion);
        this.setUpdateCompletionCooldownEnhanced(currentVersion);
        return true;
      }

      // 【第四步】增强：检查是否版本号发生了任何变化（包括降级的情况）
      if (this.lastUpdateCheck.lastVersion &&
          this.lastUpdateCheck.lastVersion !== currentVersion) {
        this.log(`🔍 检测到版本号变化: ${this.lastUpdateCheck.lastVersion} -> ${currentVersion}`);

        // 更新最后已知版本，避免重复检查
        this.lastUpdateCheck.lastVersion = currentVersion;
        this.saveUpdateStatus(this.lastUpdateCheck);

        // 如果是版本降级或相同版本，也跳过更新检查一段时间
        this.log(`📝 版本变化已记录，跳过本次更新检查`);
        this.setUpdateCompletionCooldownEnhanced(currentVersion);
        return true;
      }

      this.log(`📊 更新完成检测结果: 无更新迹象`);
      return false;
      
    } catch (error) {
      this.log(`更新完成检测失败: ${error.message}`);
      return false;
    }
  }

  /**
   * 【新增】增强的更新完成冷却期检查，延长到20分钟
   */
  checkUpdateCompletionCooldownEnhanced() {
    try {
      if (this.lastUpdateCheck.completedTimestamp) {
        const cooldownPeriod = 20 * 60 * 1000; // 20分钟冷却期
        const timeSinceCompletion = Date.now() - this.lastUpdateCheck.completedTimestamp;
        
        if (timeSinceCompletion < cooldownPeriod) {
          const remainingTime = Math.ceil((cooldownPeriod - timeSinceCompletion) / 1000);
          return {
            inCooldown: true,
            remainingTime: remainingTime
          };
        }
      }
    } catch (error) {
      this.log(`检查增强冷却期失败: ${error.message}`);
    }
    
    return { inCooldown: false, remainingTime: 0 };
  }

  /**
   * 【新增】设置增强的更新完成冷却期，延长到20分钟
   */
  setUpdateCompletionCooldownEnhanced(version) {
    try {
      const cooldownStatus = {
        ...this.lastUpdateCheck,
        lastVersion: version,
        lastCheckTime: new Date().toISOString(),
        updateCompleted: true,
        completedTimestamp: Date.now(),
        cooldownUntil: Date.now() + (20 * 60 * 1000) // 20分钟后
      };
      
      this.saveUpdateStatus(cooldownStatus);
      this.lastUpdateCheck = cooldownStatus;
      this.log(`🧊 已设置增强更新完成冷却期: 20分钟 (版本: ${version})`);
    } catch (error) {
      this.log(`设置增强冷却期失败: ${error.message}`);
    }
  }

  /**
   * 【新增】检查更新完成冷却期
   */
  checkUpdateCompletionCooldown() {
    try {
      if (this.lastUpdateCheck.completedTimestamp) {
        const cooldownPeriod = 10 * 60 * 1000; // 10分钟冷却期
        const timeSinceCompletion = Date.now() - this.lastUpdateCheck.completedTimestamp;
        
        if (timeSinceCompletion < cooldownPeriod) {
          const remainingTime = Math.ceil((cooldownPeriod - timeSinceCompletion) / 1000);
          return {
            inCooldown: true,
            remainingTime: remainingTime
          };
        }
      }
    } catch (error) {
      this.log(`检查冷却期失败: ${error.message}`);
    }
    
    return { inCooldown: false, remainingTime: 0 };
  }

  /**
   * 【新增】设置更新完成冷却期
   */
  setUpdateCompletionCooldown(version) {
    try {
      const cooldownStatus = {
        ...this.lastUpdateCheck,
        lastVersion: version,
        lastCheckTime: new Date().toISOString(),
        updateCompleted: true,
        completedTimestamp: Date.now(),
        cooldownUntil: Date.now() + (10 * 60 * 1000) // 10分钟后
      };
      
      this.saveUpdateStatus(cooldownStatus);
      this.lastUpdateCheck = cooldownStatus;
      this.log(`🧊 已设置更新完成冷却期: 10分钟 (版本: ${version})`);
    } catch (error) {
      this.log(`设置冷却期失败: ${error.message}`);
    }
  }

  /**
   * 【修复】检查是否有待处理的更新完成标记
   */
  checkPendingUpdateCompletion() {
    try {
      const pendingFile = path.join(app.getPath('userData'), 'pending_update.json');
      if (fs.existsSync(pendingFile)) {
        const data = fs.readFileSync(pendingFile, 'utf8');
        const pending = JSON.parse(data);
        
        // 【修复】延长合理时间范围到10分钟，给macOS DMG安装更多时间
        const timeDiff = Date.now() - pending.timestamp;
        if (timeDiff < 10 * 60 * 1000) { // 10分钟
          this.log(`📋 找到有效的待处理更新标记: ${pending.expectedVersion} (${Math.round(timeDiff/1000)}秒前)`);
          return pending;
        } else {
          this.log('⏰ 待处理更新标记已过期，清除');
          this.clearPendingUpdateMark();
        }
      }
    } catch (error) {
      this.log(`检查待处理更新失败: ${error.message}`);
    }
    return null;
  }

  /**
   * 标记待处理的更新完成（更新过程中调用）
   */
  markPendingUpdateCompletion(expectedVersion) {
    try {
      const pendingFile = path.join(app.getPath('userData'), 'pending_update.json');
      const pendingData = {
        expectedVersion: expectedVersion,
        timestamp: Date.now(),
        created: new Date().toISOString()
      };
      fs.writeFileSync(pendingFile, JSON.stringify(pendingData, null, 2));
      this.log(`📝 已标记待处理更新完成: ${expectedVersion}`);
    } catch (error) {
      this.log(`标记待处理更新失败: ${error.message}`);
    }
  }

  /**
   * 清除待处理的更新标记
   */
  clearPendingUpdateMark() {
    try {
      const pendingFile = path.join(app.getPath('userData'), 'pending_update.json');
      if (fs.existsSync(pendingFile)) {
        fs.unlinkSync(pendingFile);
        this.log('🗑️ 已清除待处理更新标记');
      }
    } catch (error) {
      this.log(`清除待处理更新标记失败: ${error.message}`);
    }
  }

  /**
   * 【新增】检查两个版本号是否匹配（考虑格式差异）
   */
  isVersionMatch(version1, version2) {
    if (!version1 || !version2) return false;
    
    // 直接字符串比较
    if (version1 === version2) return true;
    
    // 标准化后比较
    const norm1 = this.normalizeVersion(version1);
    const norm2 = this.normalizeVersion(version2);
    
    const match = norm1 === norm2;
    this.log(`🔍 版本匹配检查: ${version1}(${norm1}) vs ${version2}(${norm2}) = ${match}`);
    return match;
  }

  /**
   * 【新增】检查版本1是否比版本2更新
   */
  isNewerVersion(version1, version2) {
    if (!version1 || !version2) return false;
    
    // 如果版本相同，则不是更新版本
    if (this.isVersionMatch(version1, version2)) {
      return false;
    }
    
    // 使用严格版本比较
    const comparison = this.compareVersionsStrict(version1, version2);
    const isNewer = comparison > 0;
    
    this.log(`📊 版本更新检查: ${version1} ${isNewer ? '>' : '≤'} ${version2} (结果: ${isNewer})`);
    return isNewer;
  }

  /**
   * 【增强修复】版本号严格比较
    this.log(`严格比较版本: ${version1} vs ${version2}`);
    
    // 处理空值情况
    if (!version1 || !version2) {
      this.log('版本号为空，无法比较');
      return 0;
    }

    // 首先进行字符串完全匹配检查
    if (version1.toString() === version2.toString()) {
      this.log('版本号字符串完全相同，返回0');
      return 0;
    }

    // 标准化版本号
    const v1 = this.normalizeVersion(version1);
    const v2 = this.normalizeVersion(version2);
    
    // 标准化后再次检查
    if (v1 === v2) {
      this.log(`标准化后版本号相同: ${v1} === ${v2}，返回0`);
      return 0;
    }
    
    const v1parts = v1.split('.').map(part => {
      const num = parseInt(part, 10);
      return isNaN(num) ? 0 : num;
    });
    const v2parts = v2.split('.').map(part => {
      const num = parseInt(part, 10);
      return isNaN(num) ? 0 : num;
    });
    
    // 确保两个版本号数组长度相同
    const maxLength = Math.max(v1parts.length, v2parts.length);
    while (v1parts.length < maxLength) v1parts.push(0);
    while (v2parts.length < maxLength) v2parts.push(0);
    
    this.log(`严格解析后的版本号: [${v1parts.join(', ')}] vs [${v2parts.join(', ')}]`);
    
    for (let i = 0; i < maxLength; i++) {
      const v1part = v1parts[i];
      const v2part = v2parts[i];
      
      if (v1part > v2part) {
        this.log(`严格版本比较结果: ${v1} > ${v2} (返回 1)`);
        return 1;
      }
      if (v1part < v2part) {
        this.log(`严格版本比较结果: ${v1} < ${v2} (返回 -1)`);
        return -1;
      }
    }
    
    this.log(`严格版本比较结果: ${v1} = ${v2} (返回 0)`);
    return 0;
  }

  normalizeVersion(version) {
    if (!version) return '0.0.0';
    
    // 移除可能的前缀（如 v1.0.0）
    let clean = version.toString().replace(/^v/, '').trim();
    
    // 分割版本号
    const parts = clean.split('.');
    
    // 补齐缺失的部分
    while (parts.length < 3) {
      parts.push('0');
    }
    
    // 确保每部分都是数字，移除非数字字符
    const normalizedParts = parts.slice(0, 3).map(part => {
      const num = parseInt(part.replace(/[^0-9]/g, ''), 10);
      return isNaN(num) ? 0 : num;
    });
    
    const normalized = normalizedParts.join('.');
    this.log(`版本标准化: ${version} -> ${normalized}`);
    return normalized;
  }

  /**
   * 验证版本号格式是否有效
   */
  isValidVersionFormat(version) {
    if (!version || typeof version !== 'string') {
      this.log(`版本格式验证失败: 版本号为空或不是字符串 - ${version}`);
      return false;
    }

    // 移除可能的前缀
    const clean = version.replace(/^v/, '').trim();

    // 检查是否符合 x.y.z 格式
    if (!/^[0-9]+\.[0-9]+\.[0-9]+$/.test(clean)) {
      this.log(`版本格式验证失败: 不符合x.y.z格式 - ${version}`);
      return false;
    }

    // 检查每个部分是否为有效数字
    const parts = clean.split('.');
    for (const part of parts) {
      const num = parseInt(part, 10);
      if (isNaN(num) || num < 0) {
        this.log(`版本格式验证失败: 包含无效数字部分 - ${version}`);
        return false;
      }
    }

    this.log(`版本格式验证通过: ${version}`);
    return true;
  }

  showUpdateWindow() {
    if (this.updateWindow && !this.updateWindow.isDestroyed()) {
      this.updateWindow.focus();
      return;
    }

    this.log('创建更新弹窗');

    // 获取平台信息
    const platformInfo = this.getPlatformInfo();
    const isWindows = platformInfo.platform === 'windows';
    const isMacOS = platformInfo.platform === 'macos';

    // 根据平台设置不同的窗口配置
    const windowConfig = {
      width: 500,  // DMG和EXE版本都使用500px宽度
      height: 480,  // DMG和EXE版本都使用480px高度
      resizable: false,
      minimizable: false,
      maximizable: false,
      alwaysOnTop: true,
      transparent: false,
      backgroundColor: '#ffffff',
      skipTaskbar: false, // 确保在任务栏显示
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        preload: path.join(__dirname, 'update-preload.js'),
        enableRemoteModule: false,
        webSecurity: true
      },
      icon: path.join(__dirname, 'assets/logo.png'),
      show: false,
      // 禁用所有快捷键
      autoHideMenuBar: true
    };

    if (isMacOS) {
      // macOS: 使用原生交通灯按钮
      windowConfig.frame = true;
      windowConfig.titleBarStyle = 'hiddenInset';
      windowConfig.useContentSize = true; // macOS使用内容尺寸
    } else if (isWindows) {
      // Windows: 完全透明窗口，让内容直接作为主弹窗
      windowConfig.frame = false;
      windowConfig.transparent = true; // 完全透明背景
      windowConfig.backgroundColor = undefined; // 移除背景色
      windowConfig.hasShadow = false; // 移除窗口阴影
      windowConfig.skipTaskbar = false; // 保持在任务栏显示
      windowConfig.useContentSize = false; // Windows无边框窗口不使用内容尺寸
      windowConfig.thickFrame = false; // 移除厚边框
      windowConfig.roundedCorners = false; // 移除圆角
    } else {
      // Linux等其他平台: 使用标准窗口框架
      windowConfig.frame = true;
    }

    this.updateWindow = new BrowserWindow(windowConfig);

    // Windows平台强制设置窗口尺寸
    if (isWindows) {
      this.updateWindow.setSize(500, 480);
      this.updateWindow.setMinimumSize(500, 480);
      this.updateWindow.setMaximumSize(500, 480);

      // 确保窗口内容直接作为主弹窗
      this.updateWindow.webContents.once('dom-ready', () => {
        this.updateWindow.webContents.insertCSS(`
          html, body {
            margin: 0 !important;
            padding: 0 !important;
            width: 100vw !important;
            height: 100vh !important;
            overflow: hidden !important;
            background: transparent !important;
          }
          .update-container.windows-platform {
            position: absolute !important;
            top: 0 !important;
            left: 0 !important;
            width: 100vw !important;
            height: 100vh !important;
            margin: 0 !important;
            border-radius: 12px !important;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15) !important;
            border: none !important;
            background: white !important;
          }
        `);
      });
    }

    // 禁用所有快捷键和菜单
    this.updateWindow.setMenuBarVisibility(false);
    this.updateWindow.setAutoHideMenuBar(true);

    // 禁用快捷键（包括最小化快捷键）
    this.updateWindow.webContents.on('before-input-event', (event, input) => {
      // 禁用所有快捷键组合
      if (input.control || input.meta || input.alt) {
        event.preventDefault();
        return;
      }

      // 禁用特定按键
      const blockedKeys = ['F11', 'F12', 'Escape'];
      if (blockedKeys.includes(input.key)) {
        event.preventDefault();
        return;
      }
    });

    // 禁用右键菜单
    this.updateWindow.webContents.on('context-menu', (event) => {
      event.preventDefault();
    });

    // 确保窗口尺寸固定
    this.updateWindow.on('resize', () => {
      this.updateWindow.setSize(500, 480);
    });

    // 加载更新窗口HTML
    this.updateWindow.loadFile(path.join(__dirname, 'renderer/update.html'));

    this.updateWindow.once('ready-to-show', () => {
      // 根据平台确保窗口尺寸
      if (isWindows) {
        this.updateWindow.setSize(500, 480);
      } else if (isMacOS) {
        this.updateWindow.setSize(500, 480);
      }

      this.updateWindow.show();
      this.updateWindow.center();

      // 立即发送更新信息，避免页面闪烁
      if (this.updateWindow && !this.updateWindow.isDestroyed()) {
        this.updateWindow.webContents.send('update-info', this.updateInfo);
      }
    });

    this.updateWindow.on('closed', () => {
      this.updateWindow = null;
    });

    // 修改窗口关闭逻辑：任何情况下关闭都退出应用
    this.updateWindow.on('close', (event) => {
      this.log('用户关闭更新窗口，退出应用');
      // 不阻止窗口关闭
      // 直接退出整个应用
      const { app } = require('electron');
      setTimeout(() => {
        app.quit();
      }, 100);
    });
  }

  async startUpdate() {
    if (this.isDownloading || !this.updateInfo) {
      this.log('正在下载或没有更新信息');
      return;
    }

    try {
      this.isDownloading = true;
      this.log(`🚀 开始更新到版本: ${this.updateInfo.version}`);
      this.log(`📦 主下载链接: ${this.updateInfo.downloadUrl}`);
      if (this.updateInfo.backupUrl) {
        this.log(`🔄 备用下载链接: ${this.updateInfo.backupUrl}`);
      }
      
      // 通知UI开始下载
      if (this.updateWindow && !this.updateWindow.isDestroyed()) {
        this.updateWindow.webContents.send('start-download');
        this.updateWindow.webContents.send('download-progress', {
          percent: 0,
          status: '正在准备下载...'
        });
      }
      
      // 【增强】下载文件，包含智能重试和备用链接切换
      let downloadedFilePath;
      try {
        downloadedFilePath = await this.downloadFile(this.updateInfo.downloadUrl);
      } catch (primaryError) {
        this.log(`❌ 主下载链接失败: ${primaryError.message}`);
        
        // 通知UI主链接失败，准备尝试备用链接
        if (this.updateWindow && !this.updateWindow.isDestroyed()) {
          this.updateWindow.webContents.send('download-progress', {
            percent: 0,
            status: '主链接失败，尝试备用链接...'
          });
        }
        
        // 尝试备用下载链接
        if (this.updateInfo.backupUrl) {
          this.log(`🔄 尝试备用下载链接: ${this.updateInfo.backupUrl}`);
          try {
            downloadedFilePath = await this.downloadFile(this.updateInfo.backupUrl, true);
          } catch (backupError) {
            this.log(`❌ 备用下载链接也失败: ${backupError.message}`);
            throw new Error(`所有下载链接都失败。主链接错误: ${primaryError.message}; 备用链接错误: ${backupError.message}`);
          }
        } else {
          throw primaryError;
        }
      }
      
      this.log(`✅ 文件下载完成: ${downloadedFilePath}`);
      
      // 通知UI下载完成，准备安装
      if (this.updateWindow && !this.updateWindow.isDestroyed()) {
        this.updateWindow.webContents.send('download-progress', {
          percent: 100,
          status: '下载完成，准备安装...'
        });
      }
      
      // 安装更新
      await this.installUpdate(downloadedFilePath);
      
    } catch (error) {
      this.log(`❌ 更新失败: ${error.message}`);
      this.isDownloading = false;
      
      // 【增强】提供更详细的错误信息给用户
      let userFriendlyError = error.message;
      if (error.message.includes('文件大小异常')) {
        userFriendlyError = '下载文件不完整，请检查网络连接后重试。';
      } else if (error.message.includes('下载失败')) {
        userFriendlyError = '网络连接问题导致下载失败，请稍后重试。';
      } else if (error.message.includes('所有下载链接都失败')) {
        userFriendlyError = '所有下载服务器都无法访问，请稍后重试或联系技术支持。';
      }
      
      if (this.updateWindow && !this.updateWindow.isDestroyed()) {
        this.updateWindow.webContents.send('download-error', userFriendlyError);
      }
    }
  }

  /**
   * 下载更新文件 - 增强版本，支持重试和更好的错误处理
   */
  async downloadFile(url, isUsingBackup = false) {
    const maxRetries = 3; // 最大重试次数
    let lastError = null;
    
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        this.log(`📥 开始下载尝试 ${attempt}/${maxRetries}${isUsingBackup ? ' (备用链接)' : ''}`);
        const filePath = await this.performDownload(url, isUsingBackup, attempt);
        return filePath;
      } catch (error) {
        lastError = error;
        this.log(`❌ 下载尝试 ${attempt}/${maxRetries} 失败: ${error.message}`);
        
        // 如果不是最后一次尝试，等待后重试
        if (attempt < maxRetries) {
          const retryDelay = attempt * 2000; // 递增延迟：2s, 4s, 6s
          this.log(`⏰ 等待 ${retryDelay/1000} 秒后重试...`);
          await new Promise(resolve => setTimeout(resolve, retryDelay));
        }
      }
    }
    
    // 所有重试都失败了
    throw new Error(`下载失败，已重试${maxRetries}次。最后错误: ${lastError.message}`);
  }

  /**
   * 执行单次下载操作
   */
  async performDownload(url, isUsingBackup = false, attempt = 1) {
    return new Promise((resolve, reject) => {
      try {
        const platformInfo = this.getPlatformInfo();
        const fileExtension = platformInfo.platform === 'windows' ? 'exe' : 'dmg';
        const fileName = `update_${Date.now()}_attempt${attempt}.${fileExtension}`;
        const filePath = path.join(this.downloadDir, fileName);
        
        this.log(`📁 下载文件路径: ${filePath}`);
        if (isUsingBackup) {
          this.log('⚠️ 注意：正在使用备用下载链接');
        }
        
        const writer = fs.createWriteStream(filePath);
        
        axios({
          method: 'GET',
          url: url,
          responseType: 'stream',
          timeout: 300000, // 5分钟超时
          maxRedirects: 20,
          maxContentLength: 500 * 1024 * 1024, // 500MB最大文件大小
          maxBodyLength: 500 * 1024 * 1024,
          validateStatus: function (status) {
            return status >= 200 && status < 400;
          },
          headers: {
            'User-Agent': `XiaoMeiHua-App/${app.getVersion()} (${platformInfo.platform}; ${platformInfo.architecture})`,
            'Accept': '*/*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8'
          }
        }).then(response => {
          this.log(`📊 下载响应状态: ${response.status}`);
          this.log(`🌐 最终下载URL: ${response.request.res.responseUrl || url}`);
          this.log(`📄 响应内容类型: ${response.headers['content-type']}`);
          
          const totalLength = parseInt(response.headers['content-length'], 10);
          let downloadedLength = 0;
          
          if (totalLength > 0) {
            this.log(`📊 文件总大小: ${(totalLength / 1024 / 1024).toFixed(2)} MB`);
          }
          
          response.data.on('data', (chunk) => {
            downloadedLength += chunk.length;
            if (totalLength > 0) {
              const progress = Math.round((downloadedLength / totalLength) * 100);
              this.downloadProgress = progress;

              if (this.updateWindow && !this.updateWindow.isDestroyed()) {
                const statusText = isUsingBackup ? `使用备用链接更新中 ${progress}%` : `更新中 ${progress}%`;
                this.updateWindow.webContents.send('download-progress', {
                  percent: progress,
                  downloaded: downloadedLength,
                  total: totalLength,
                  status: statusText
                });
              }
              
              // 每下载10%打印一次日志
              if (progress % 10 === 0 && progress > 0) {
                const logPrefix = isUsingBackup ? '备用链接下载进度' : '下载进度';
                this.log(`${logPrefix}: ${progress}% (${(downloadedLength / 1024 / 1024).toFixed(2)} MB)`);
              }
            }
          });
          
          response.data.pipe(writer);
          
          writer.on('finish', () => {
            this.log(`✅ 文件下载完成: ${filePath}`);
            // 【修复】增强文件验证逻辑，解决文件大小异常误判
            if (fs.existsSync(filePath)) {
              const stats = fs.statSync(filePath);
              this.log(`📊 下载文件大小: ${(stats.size / 1024 / 1024).toFixed(2)} MB`);
              
              // 【修复】更智能的文件大小验证：使用更宽松和实际的阈值
              const platformInfo = this.getPlatformInfo();
              let minSizeThreshold = 512 * 1024; // 默认512KB，更宽松
              
              if (platformInfo.platform === 'macos') {
                // macOS DMG文件：考虑到压缩等因素，降低到2MB
                minSizeThreshold = 2 * 1024 * 1024; // 2MB（比之前的10MB更合理）
              } else if (platformInfo.platform === 'windows') {
                // Windows EXE文件，降低到1MB
                minSizeThreshold = 1 * 1024 * 1024; // 1MB  
              }
              
              this.log(`🔍 文件大小验证阈值: ${(minSizeThreshold / 1024 / 1024).toFixed(2)} MB`);
              
              // 【新增】增强的内容类型验证：更宽松的文件头检查
              if (this.validateFileIntegrityEnhanced(filePath, platformInfo)) {
                if (stats.size >= minSizeThreshold) {
                  this.downloadPath = filePath;
                  this.log('✅ 文件大小和内容验证通过');
                  resolve(filePath);
                } else {
                  this.log(`❌ 文件大小不足: ${(stats.size / 1024 / 1024).toFixed(2)} MB < ${(minSizeThreshold / 1024 / 1024).toFixed(2)} MB`);
                  
                  // 【修复】如果文件内容正确但偏小，可能是正常的小版本更新
                  if (stats.size > 100 * 1024) { // 至少100KB，可能是小的补丁包
                    this.log('⚠️ 文件偏小但可能是有效的小更新包，尝试继续');
                    this.downloadPath = filePath;
                    resolve(filePath);
                  } else {
                    const detailedError = this.generateDetailedSizeError(stats.size, minSizeThreshold, platformInfo);
                    reject(new Error(detailedError));
                  }
                }
              } else {
                this.log('❌ 文件内容验证失败，可能不是有效的安装包');
                reject(new Error('下载的文件内容异常，不是有效的安装包格式'));
              }
            } else {
              reject(new Error('下载的文件不存在'));
            }
          });
          
          writer.on('error', (error) => {
            this.log(`❌ 文件写入失败: ${error.message}`);
            reject(error);
          });
          
        }).catch(error => {
          this.log(`❌ 下载请求失败: ${error.message}`);
          if (error.response) {
            this.log(`HTTP状态: ${error.response.status}`);
            this.log(`响应头: ${JSON.stringify(error.response.headers)}`);
          }
          reject(error);
        });
        
      } catch (error) {
        this.log(`❌ 下载文件失败: ${error.message}`);
        reject(error);
      }
    });
  }

  /**
   * 安装更新文件
   */
  async installUpdate(filePath) {
    try {
      this.log(`🔧 开始安装更新: ${filePath}`);
      
      if (this.updateWindow && !this.updateWindow.isDestroyed()) {
        this.updateWindow.webContents.send('start-install');
      }

      const platformInfo = this.getPlatformInfo();
      
      if (platformInfo.platform === 'windows') {
        // Windows: 执行EXE安装程序
        await this.executeWindowsInstaller(filePath);
      } else if (platformInfo.platform === 'macos') {
        // macOS: 自动挂载DMG并安装
        await this.mountDmgAndInstall(filePath);
      } else {
        throw new Error(`不支持的平台: ${platformInfo.platform}`);
      }

    } catch (error) {
      this.log(`❌ 安装更新失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * Windows安装程序执行
   */
  async executeWindowsInstaller(exePath) {
    return new Promise((resolve, reject) => {
      try {
        this.log(`🔧 执行Windows安装程序: ${exePath}`);
        
        // 标记待处理的更新完成（Windows安装前）
        if (this.updateInfo && this.updateInfo.version) {
          this.markPendingUpdateCompletion(this.updateInfo.version);
          this.log(`📝 已标记Windows待处理更新: ${this.updateInfo.version}`);
        }
        
        // 通知安装完成
        if (this.updateWindow && !this.updateWindow.isDestroyed()) {
          this.updateWindow.webContents.send('install-complete');
        }
        
        // 启动安装程序
        const installer = spawn(exePath, [], {
          detached: true,
          stdio: 'ignore'
        });
        
        installer.unref();
        
        // Windows安装程序会自动处理，这里直接退出当前应用
        setTimeout(() => {
          app.quit();
        }, 2000);
        
        resolve();
        
      } catch (error) {
        this.log(`❌ Windows安装失败: ${error.message}`);
        reject(error);
      }
    });
  }

  /**
   * macOS DMG挂载和安装
   */
  async mountDmgAndInstall(dmgPath) {
    return new Promise((resolve, reject) => {
      try {
        this.log(`🔧 挂载DMG文件: ${dmgPath}`);

        // 验证DMG文件是否存在
        if (!fs.existsSync(dmgPath)) {
          throw new Error(`DMG文件不存在: ${dmgPath}`);
        }

        // 获取文件信息
        const stats = fs.statSync(dmgPath);
        this.log(`📊 DMG文件大小: ${(stats.size / 1024 / 1024).toFixed(2)} MB`);

        // 【修复】使用与下载验证一致的大小阈值，避免二次验证失败
        const minSizeThreshold = 2 * 1024 * 1024; // 2MB，与下载验证一致
        if (stats.size < minSizeThreshold) {
          throw new Error(`DMG文件大小异常：实际${(stats.size / 1024 / 1024).toFixed(2)}MB，期望至少${(minSizeThreshold / 1024 / 1024).toFixed(2)}MB。可能下载不完整`);
        }

        // 使用异步方式执行安装，避免阻塞主线程
        this.performAsyncInstallation(dmgPath, resolve, reject);

      } catch (error) {
        this.log(`❌ DMG安装失败: ${error.message}`);
        reject(error);
      }
    });
  }

  /**
   * 异步执行macOS安装过程
   */
  async performAsyncInstallation(dmgPath, resolve, reject) {
    try {
      const { spawn } = require('child_process');

      let mountOutput = '';
      const mountProcess = spawn('hdiutil', ['attach', dmgPath, '-nobrowse', '-quiet'], {
        stdio: ['pipe', 'pipe', 'pipe']
      });

      mountProcess.stdout.on('data', (data) => {
        mountOutput += data.toString();
      });

      mountProcess.stderr.on('data', (data) => {
        this.log(`挂载错误输出: ${data.toString()}`);
      });

      mountProcess.on('close', async (code) => {
        if (code !== 0) {
          // 尝试强制挂载
          this.log('尝试强制挂载...');
          const forceMountProcess = spawn('hdiutil', ['attach', dmgPath, '-nobrowse', '-quiet', '-force'], {
            stdio: ['pipe', 'pipe', 'pipe']
          });

          let forceMountOutput = '';
          forceMountProcess.stdout.on('data', (data) => {
            forceMountOutput += data.toString();
          });

          forceMountProcess.on('close', async (forceCode) => {
            if (forceCode !== 0) {
              reject(new Error('DMG挂载失败'));
              return;
            }

            mountOutput = forceMountOutput;
            await this.continueInstallation(mountOutput, dmgPath, resolve, reject);
          });
        } else {
          this.log('✅ DMG挂载成功');
          this.log(`挂载输出: ${mountOutput}`);
          await this.continueInstallation(mountOutput, dmgPath, resolve, reject);
        }
      });

    } catch (error) {
      reject(error);
    }
  }

  /**
   * 继续macOS安装过程
   */
  async continueInstallation(mountOutput, dmgPath, resolve, reject) {
    try {
      // 从挂载输出中提取挂载点
      let mountPoint = null;
      if (mountOutput) {
        const lines = mountOutput.split('\n');
        for (const line of lines) {
          if (line.includes('/Volumes/')) {
            const match = line.match(/\/Volumes\/[^\s]+/);
            if (match) {
              mountPoint = match[0];
              break;
            }
          }
        }
      }

      // 如果没有从输出中找到，尝试查找最新的挂载点
      if (!mountPoint) {
        this.log('从输出中未找到挂载点，搜索/Volumes目录');
        const volumesDir = '/Volumes';
        const volumes = fs.readdirSync(volumesDir);

        // 查找包含app文件的挂载点
        for (const volume of volumes) {
          const volumePath = path.join(volumesDir, volume);
          try {
            if (fs.existsSync(volumePath) && fs.statSync(volumePath).isDirectory()) {
              const files = fs.readdirSync(volumePath);
              const appFile = files.find(file => file.endsWith('.app'));
              if (appFile) {
                mountPoint = volumePath;
                this.log(`✅ 找到挂载点: ${mountPoint}`);
                break;
              }
            }
          } catch (err) {
            // 忽略无法访问的卷
            continue;
          }
        }
      }

      if (!mountPoint || !fs.existsSync(mountPoint)) {
        throw new Error('无法找到DMG挂载点');
      }

      // 查找应用程序文件
      const files = fs.readdirSync(mountPoint);
      const appFile = files.find(file => file.endsWith('.app'));

      if (!appFile) {
        throw new Error('在DMG中未找到应用程序');
      }

      const appPath = path.join(mountPoint, appFile);
      this.log(`✅ 找到应用程序: ${appPath}`);

      // 异步复制应用到Applications目录
      await this.asyncCopyApplication(appPath, mountPoint, dmgPath, resolve, reject);

    } catch (error) {
      this.log(`❌ 安装过程失败: ${error.message}`);
      reject(error);
    }
  }

  /**
   * 异步复制应用程序到Applications目录
   */
  async asyncCopyApplication(appPath, mountPoint, dmgPath, resolve, reject) {
    try {
      const appsDir = '/Applications';
      const appName = path.basename(appPath);
      const targetPath = path.join(appsDir, appName);

      // 如果目标已存在，先删除
      if (fs.existsSync(targetPath)) {
        this.log(`🗑️ 删除旧版本: ${targetPath}`);

        // 使用异步方式删除旧版本
        const { spawn } = require('child_process');
        const rmProcess = spawn('rm', ['-rf', targetPath], {
          stdio: ['pipe', 'pipe', 'pipe']
        });

        await new Promise((rmResolve, rmReject) => {
          rmProcess.on('close', (code) => {
            if (code === 0) {
              this.log('✅ 旧版本删除成功');
              rmResolve();
            } else {
              rmReject(new Error('删除旧版本失败'));
            }
          });
        });
      }

      this.log(`📁 复制应用到: ${targetPath}`);

      // 使用异步方式复制应用
      const cpProcess = spawn('cp', ['-R', appPath, appsDir], {
        stdio: ['pipe', 'pipe', 'pipe']
      });

      await new Promise((cpResolve, cpReject) => {
        cpProcess.on('close', (code) => {
          if (code === 0) {
            this.log('✅ 应用复制成功');
            cpResolve();
          } else {
            cpReject(new Error('应用复制失败'));
          }
        });
      });

      // 验证复制是否成功
      if (!fs.existsSync(targetPath)) {
        throw new Error('应用复制失败');
      }

      // 异步卸载DMG和清理文件
      await this.asyncCleanup(mountPoint, dmgPath);

      // 通知安装完成
      if (this.updateWindow && !this.updateWindow.isDestroyed()) {
        this.updateWindow.webContents.send('install-complete');
        this.updateWindow.webContents.send('download-progress', {
          percent: 100,
          downloaded: 0,
          total: 0,
          status: '更新中 100%'
        });
      }

      this.log('✅ 应用安装成功，准备重启');
      this.isDownloading = false;

      // 标记待处理的更新完成（在重启前）
      if (this.updateInfo && this.updateInfo.version) {
        this.markPendingUpdateCompletion(this.updateInfo.version);
        this.log(`📝 已标记待处理更新: ${this.updateInfo.version}，重启后将验证`);
      }

      // 立即重启应用
      this.restartApp(targetPath);

      resolve();

    } catch (error) {
      this.log(`❌ 复制应用失败: ${error.message}`);
      reject(error);
    }
  }

  /**
   * 异步清理DMG和临时文件
   */
  async asyncCleanup(mountPoint, dmgPath) {
    try {
      // 卸载DMG
      this.log(`🔄 卸载DMG: ${mountPoint}`);
      const { spawn } = require('child_process');

      const detachProcess = spawn('hdiutil', ['detach', mountPoint, '-quiet'], {
        stdio: ['pipe', 'pipe', 'pipe']
      });

      await new Promise((detachResolve) => {
        detachProcess.on('close', (code) => {
          if (code === 0) {
            this.log('✅ DMG卸载成功');
          } else {
            this.log('⚠️ DMG卸载失败，但继续');
          }
          detachResolve();
        });
      });

      // 删除下载的DMG文件
      try {
        fs.unlinkSync(dmgPath);
        this.log('🗑️ 清理下载文件完成');
      } catch (unlinkError) {
        this.log(`⚠️ 清理文件失败: ${unlinkError.message}`);
      }

    } catch (error) {
      this.log(`⚠️ 清理过程失败: ${error.message}`);
    }
  }












  restartApp(appPath = null) {
    try {
      this.log('准备重启应用程序');

      // 关闭更新窗口
      this.closeUpdateWindow();

      if (process.platform === 'darwin' && appPath) {
        // macOS: 使用简化的重启方式，避免多实例问题
        this.log(`重启到新版本应用: ${appPath}`);
        this.performSimpleRestart(appPath);
      } else {
        // 其他平台或没有指定路径时直接退出
        this.log('退出当前应用');
        app.exit(0);
      }

    } catch (error) {
      this.log(`重启应用失败: ${error.message}`);
      // 确保无论如何都要退出应用
      setTimeout(() => {
        app.exit(0);
      }, 500);
    }
  }

  // 执行简化的应用重启，避免多实例问题
  performSimpleRestart(appPath) {
    try {
      this.log('开始执行简化的应用重启');
      
      // 验证新应用是否存在
      if (!fs.existsSync(appPath)) {
        throw new Error(`新应用不存在: ${appPath}`);
      }

      // 使用延迟启动机制，确保当前应用完全退出后再启动新应用
      this.scheduleDelayedRestart(appPath);
      
      // 立即关闭当前应用
      this.log('立即关闭当前应用');
      setTimeout(() => {
        app.exit(0);
      }, 100);

    } catch (error) {
      this.log(`简化重启失败: ${error.message}`);
      // 最后的保险措施
      setTimeout(() => {
        app.exit(0);
      }, 500);
    }
  }

  // 安排延迟重启，确保当前应用完全退出后再启动新应用
  scheduleDelayedRestart(appPath) {
    const { spawn } = require('child_process');
    
    try {
      this.log(`安排延迟重启: ${appPath}`);
      
      // 创建一个独立的脚本来延迟启动新应用
      const restartScript = `
        #!/bin/bash
        sleep 2
        open "${appPath}"
      `;
      
      const scriptPath = path.join(app.getPath('userData'), 'restart.sh');
      fs.writeFileSync(scriptPath, restartScript);
      fs.chmodSync(scriptPath, '755');
      
      // 启动独立的重启脚本
      const restartProcess = spawn('bash', [scriptPath], {
        detached: true,
        stdio: 'ignore'
      });
      
      restartProcess.unref();
      this.log('✅ 延迟重启脚本已启动');
      
    } catch (error) {
      this.log(`安排延迟重启失败: ${error.message}`);
      // 备用方案：直接使用系统命令
      try {
        const { spawn } = require('child_process');
        const delayedOpen = spawn('bash', ['-c', `sleep 2 && open "${appPath}"`], {
          detached: true,
          stdio: 'ignore'
        });
        delayedOpen.unref();
        this.log('✅ 备用延迟重启已启动');
      } catch (backupError) {
        this.log(`备用延迟重启也失败: ${backupError.message}`);
      }
    }
  }

  // 已删除launchNewAppImmediately方法，使用新的延迟重启机制

  // 立即关闭所有窗口
  closeAllWindowsImmediately() {
    try {
      this.log('立即关闭所有窗口...');
      const allWindows = require('electron').BrowserWindow.getAllWindows();
      allWindows.forEach(window => {
        if (!window.isDestroyed()) {
          try {
            window.destroy(); // 使用destroy而不是close，立即销毁窗口
          } catch (destroyError) {
            this.log(`销毁窗口失败: ${destroyError.message}`);
          }
        }
      });
      this.updateWindow = null;
      this.log('所有窗口已立即关闭');
    } catch (error) {
      this.log(`关闭窗口失败: ${error.message}`);
    }
  }

  // 已删除setupBackupLaunchMechanism方法，避免多实例问题



  // 强制退出当前应用
  forceExitCurrentApp() {
    this.log('🔄 强制退出当前应用，新应用应该已经启动');

    try {
      // 确保所有窗口都已关闭
      const allWindows = require('electron').BrowserWindow.getAllWindows();
      if (allWindows.length > 0) {
        this.log(`还有 ${allWindows.length} 个窗口未关闭，立即关闭`);
        allWindows.forEach(window => {
          if (!window.isDestroyed()) {
            window.destroy();
          }
        });
      }

      // 立即退出，不等待
      this.log('执行 app.exit(0)');
      app.exit(0);
    } catch (exitError) {
      this.log(`app.exit()失败: ${exitError.message}`);
      try {
        this.log('执行 process.exit(0)');
        process.exit(0);
      } catch (processExitError) {
        this.log(`process.exit()失败: ${processExitError.message}`);
      }
    }
  }



  // 安全退出当前应用（保留原有方法，用于非更新场景）
  exitCurrentApp() {
    this.log('开始安全退出当前应用');

    try {
      // 关闭所有窗口
      const allWindows = require('electron').BrowserWindow.getAllWindows();
      allWindows.forEach(window => {
        if (!window.isDestroyed()) {
          try {
            window.close();
          } catch (closeError) {
            this.log(`关闭窗口失败: ${closeError.message}`);
          }
        }
      });

      // 延迟退出，确保窗口完全关闭
      setTimeout(() => {
        this.log('执行应用退出');
        try {
          app.quit();
        } catch (quitError) {
          this.log(`app.quit()失败，使用app.exit(): ${quitError.message}`);
          app.exit(0);
        }
      }, 500);

    } catch (error) {
      this.log(`退出应用失败: ${error.message}`);
      // 最后的保险措施
      setTimeout(() => {
        process.exit(0);
      }, 1000);
    }
  }

  closeUpdateWindow() {
    if (this.updateWindow && !this.updateWindow.isDestroyed()) {
      this.updateWindow.close();
      this.updateWindow = null;

      // 通知主进程更新窗口已关闭，可以启动弹窗管理器
      this.notifyUpdateWindowClosed();
    }
  }

  /**
   * 通知主进程更新窗口已关闭
   */
  notifyUpdateWindowClosed() {
    try {
      // 直接访问全局弹窗管理器实例并启动
      if (global.popupManager && !global.popupManager.isStarted()) {
        console.log('🚀 更新窗口关闭，启动弹窗管理器');
        global.popupManager.start({ clearHistory: false });
      }
    } catch (error) {
      console.error('❌ 通知弹窗管理器启动失败:', error);
    }
  }

  // 获取下载进度
  getDownloadProgress() {
    return this.downloadProgress;
  }

  // 检查是否正在下载
  isUpdateDownloading() {
    return this.isDownloading;
  }

  /**
   * 【新增】增强的文件完整性验证，更宽松但仍然有效
   */
  validateFileIntegrityEnhanced(filePath, platformInfo) {
    try {
      // 读取文件的前32个字节来检查文件头
      const fd = fs.openSync(filePath, 'r');
      const buffer = Buffer.alloc(32);
      fs.readSync(fd, buffer, 0, 32, 0);
      fs.closeSync(fd);

      // 首先检查是否是明显的错误文件（HTML错误页面等）
      const headerStr = buffer.toString('ascii', 0, 16);
      const commonErrors = ['<html', '<!doc', 'error', '404', '403', '500', 'not found'];
      for (const errorPattern of commonErrors) {
        if (headerStr.toLowerCase().includes(errorPattern.toLowerCase())) {
          this.log(`❌ 检测到错误页面内容: ${errorPattern}`);
          return false;
        }
      }

      if (platformInfo.platform === 'macos') {
        // DMG文件验证：更宽松的检查
        // DMG文件格式复杂，只需排除明显错误即可
        this.log('✅ macOS DMG文件基本验证通过（使用宽松模式）');
        return true;
      } else if (platformInfo.platform === 'windows') {
        // EXE文件魔术字节检查：开头应该是 "MZ"
        if (buffer.length >= 2 && buffer[0] === 0x4D && buffer[1] === 0x5A) {
          this.log('✅ 检测到有效的Windows EXE文件头');
          return true;
        } else {
          // 如果不是标准EXE头，检查是否至少不是文本文件
          const isTextFile = buffer.toString('ascii', 0, 8).match(/^[a-zA-Z0-9\s<>]+$/);
          if (!isTextFile) {
            this.log('✅ Windows文件似乎是二进制格式，通过验证');
            return true;
          } else {
            this.log('❌ 检测到文本格式，可能不是有效的EXE文件');
            return false;
          }
        }
      }

      this.log('✅ 文件格式验证通过（通用模式）');
      return true;
      
    } catch (error) {
      this.log(`文件完整性验证失败: ${error.message}`);
      // 验证失败时采用宽松策略：如果无法验证，默认通过
      this.log('⚠️ 验证失败，采用宽松模式通过');
      return true;
    }
  }

  /**
   * 【新增】验证文件完整性和格式
   */
  validateFileIntegrity(filePath, platformInfo) {
    try {
      // 读取文件的前16个字节来检查文件头
      const fd = fs.openSync(filePath, 'r');
      const buffer = Buffer.alloc(16);
      fs.readSync(fd, buffer, 0, 16, 0);
      fs.closeSync(fd);

      if (platformInfo.platform === 'macos') {
        // DMG文件魔术字节检查：开头应该是 "koly" 或其他DMG标识符
        // 或者检查是否包含典型的DMG结构
        const headerStr = buffer.toString('ascii', 0, 8);
        // DMG文件可能有多种格式，这里进行基本检查
        if (buffer.length >= 4) {
          // 检查是否是合法的二进制文件（不是HTML或文本错误页面）
          const firstBytes = buffer.toString('ascii', 0, 4);
          if (firstBytes.includes('<html') || firstBytes.includes('<!DOC') || 
              firstBytes.includes('Error') || firstBytes.includes('404')) {
            this.log('❌ 检测到HTML错误页面，不是有效的DMG文件');
            return false;
          }
          return true; // DMG文件格式较复杂，基本验证通过即可
        }
      } else if (platformInfo.platform === 'windows') {
        // EXE文件魔术字节检查：开头应该是 "MZ"
        if (buffer.length >= 2 && buffer[0] === 0x4D && buffer[1] === 0x5A) {
          this.log('✅ 检测到有效的Windows EXE文件头');
          return true;
        } else {
          // 检查是否是HTML错误页面
          const headerStr = buffer.toString('ascii', 0, 8);
          if (headerStr.includes('<html') || headerStr.includes('<!DOC')) {
            this.log('❌ 检测到HTML错误页面，不是有效的EXE文件');
            return false;
          }
        }
      }

      this.log('⚠️ 无法确定文件格式，但通过基本检查');
      return true;
      
    } catch (error) {
      this.log(`文件完整性验证失败: ${error.message}`);
      return false;
    }
  }

  /**
   * 【新增】生成详细的文件大小错误信息
   */
  generateDetailedSizeError(actualSize, expectedMinSize, platformInfo) {
    const actualMB = (actualSize / 1024 / 1024).toFixed(2);
    const expectedMB = (expectedMinSize / 1024 / 1024).toFixed(2);
    
    let platformDesc = '';
    if (platformInfo.platform === 'macos') {
      platformDesc = 'macOS安装包(DMG)';
    } else if (platformInfo.platform === 'windows') {
      platformDesc = 'Windows安装包(EXE)';
    }
    
    return `下载的${platformDesc}文件大小异常：实际${actualMB}MB，期望至少${expectedMB}MB。` +
           `这可能是由于网络中断、服务器错误或下载链接问题导致的。请检查网络连接后重试。`;
  }






  // 取消下载
  cancelUpdate() {
    this.isDownloading = false;
    this.downloadProgress = 0;
    
    // 【新增】清理可能存在的临时下载文件
    try {
      if (this.downloadPath && fs.existsSync(this.downloadPath)) {
        fs.unlinkSync(this.downloadPath);
        this.log(`🗑️ 已清理临时下载文件: ${this.downloadPath}`);
      }
    } catch (cleanupError) {
      this.log(`⚠️ 清理临时文件失败: ${cleanupError.message}`);
    }
    
    this.closeUpdateWindow();
    this.log('用户取消更新');
  }

  /**
   * 【新增】清理所有临时下载文件
   */
  cleanupTemporaryFiles() {
    try {
      if (fs.existsSync(this.downloadDir)) {
        const files = fs.readdirSync(this.downloadDir);
        let cleanedCount = 0;
        
        files.forEach(file => {
          if (file.startsWith('update_') && (file.endsWith('.dmg') || file.endsWith('.exe'))) {
            try {
              const filePath = path.join(this.downloadDir, file);
              const stats = fs.statSync(filePath);
              const ageInMinutes = (Date.now() - stats.mtime.getTime()) / (1000 * 60);
              
              // 清理超过30分钟的临时文件
              if (ageInMinutes > 30) {
                fs.unlinkSync(filePath);
                cleanedCount++;
                this.log(`🗑️ 清理过期临时文件: ${file}`);
              }
            } catch (fileError) {
              this.log(`⚠️ 清理文件失败 ${file}: ${fileError.message}`);
            }
          }
        });
        
        if (cleanedCount > 0) {
          this.log(`✅ 已清理 ${cleanedCount} 个过期临时文件`);
        }
      }
    } catch (error) {
      this.log(`临时文件清理失败: ${error.message}`);
    }
  }
}

module.exports = AppUpdater;