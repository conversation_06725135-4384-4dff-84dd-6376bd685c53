// 纯浏览器环境脚本，绝不使用任何Node.js模块
console.log('[小梅花 Preload] 脚本已加载，准备在纯浏览器环境拦截链接...');

// 添加IPC通信支持
window.ipcRenderer = {
  send: function(channel, data) {
    // 使用特殊前缀和JSON编码来传递IPC消息
    const ipcCommand = {
      channel: channel,
      data: data
    };
    console.log(`[小梅花 IPC] 发送消息: ${channel}`, data);
    // 使用特殊前缀标记IPC消息
    document.title = `XMH_IPC_MSG::${JSON.stringify(ipcCommand)}`;
    // 恢复原始标题
    setTimeout(() => {
      if (document.title.startsWith('XMH_IPC_MSG::')) {
        document.title = data.title || document.title.replace('XMH_IPC_MSG::', '');
      }
    }, 100);
  }
};

/**
 * 核心功能：拦截页面中的所有链接点击和window.open调用。
 */
function activateLinkInterceptor() {
  
  // 1. 拦截<a>标签的点击事件
  document.body.addEventListener('click', (event) => {
    let target = event.target;
    // 确保我们找到的是一个<a>标签
    while (target && target.tagName !== 'A') {
      target = target.parentElement;
    }

    if (target && target.href) {
      const url = target.href;

      // 忽略JavaScript伪协议
      if (url.startsWith('javascript:')) {
        return;
      }
      
      // 修复URL格式错误问题，特别是店铺页面的链接
      let fixedUrl = url;
      // 修复 https:///shop 这样的错误URL
      if (url.includes('///')) {
        fixedUrl = url.replace('///', '//');
      }
      
      // 特殊处理店铺链接
      if (url.includes('/shop/home') || url.includes('/shop') && !url.includes('weixin.qq.com')) {
        fixedUrl = 'https://store.weixin.qq.com/shop/home';
      }

      // 阻止事件的默认行为和进一步传播
      event.preventDefault();
      event.stopImmediatePropagation();

      console.log(`[小梅花 Preload] 已拦截<a>标签点击，原始URL: ${url}, 修复后URL: ${fixedUrl}`);
      
      // 【最终修复方案】通过修改标题来发送命令，绕开postMessage的不可靠性
      const command = {
        url: fixedUrl,
        title: target.innerText || '新页面'
      };
      // 使用特殊前缀和JSON编码来传递信息
      document.title = `XMH_NAV_CMD::${JSON.stringify(command)}`;
      
      return false;
    }
  }, true); // 使用捕获模式以确保最高优先级

  // 2. 覆盖window.open方法
  const originalWindowOpen = window.open;
  window.open = (url, target, features) => {
    console.log(`[小梅花 Preload] 已拦截window.open调用, URL: ${url}`);
    
    // 修复URL格式错误问题
    let fixedUrl = url;
    // 修复 https:///shop 这样的错误URL
    if (url && url.includes('///')) {
      fixedUrl = url.replace('///', '//');
    }
    
    // 特殊处理店铺链接
    if (url && (url.includes('/shop/home') || url.includes('/shop') && !url.includes('weixin.qq.com'))) {
      fixedUrl = 'https://store.weixin.qq.com/shop/home';
    }
    
    // 【最终修复方案】同样通过修改标题来发送命令
    const command = {
      url: fixedUrl,
      title: '新页面'
    };
    document.title = `XMH_NAV_CMD::${JSON.stringify(command)}`;
    
    // 返回一个模拟的窗口对象，避免页面脚本因无法打开窗口而报错
    return {
      closed: false,
      close: function() { this.closed = true; },
      focus: function() {},
      postMessage: function() {}
    };
  };

  console.log('[小梅花 Preload] 链接拦截器已成功部署。');
}

// 确保在DOM加载完成后再执行脚本
if (document.readyState === 'complete' || document.readyState === 'interactive') {
    activateLinkInterceptor();
} else {
    document.addEventListener('DOMContentLoaded', activateLinkInterceptor, { once: true });
}

// 添加防止登录失效的脚本
document.addEventListener('DOMContentLoaded', () => {
  // 检测是否是微信相关页面
  const isWeixinPage = window.location.hostname.includes('weixin.qq.com') ||
                      window.location.hostname.includes('store.weixin.qq.com') ||
                      window.location.hostname.includes('shop.weixin.qq.com') ||
                      window.location.hostname.includes('channels.weixin.qq.com');

  if (isWeixinPage) {
    console.log('[小梅花] 检测到微信页面，注入增强的防止登录失效脚本');

    // 增强的防止自动登出
    const preventLogout = () => {
      // 保存原始的登录状态检查函数
      const originalLoginCheck = window.checkLoginStatus;
      if (originalLoginCheck) {
        window.checkLoginStatus = function() {
          console.log('[小梅花] 拦截登录状态检查，强制返回已登录');
          return true;
        };
      }
      // 拦截localStorage操作，防止清除登录状态
      const originalLocalStorageSetItem = localStorage.setItem;
      localStorage.setItem = function(key, value) {
        // 阻止清除登录相关的信息
        if (key && (key.includes('login') || key.includes('token') || key.includes('ticket') || 
            key.includes('session') || key.includes('logout') || key.includes('expire'))) {
          console.log(`[小梅花] 拦截localStorage设置: ${key}`);
          // 如果是设置过期时间或登出标记，则阻止
          if (value && (value.includes('expire') || value.includes('logout'))) {
            console.log(`[小梅花] 阻止设置过期值: ${key}=${value}`);
            return;
          }
        }
        originalLocalStorageSetItem.call(this, key, value);
      };
      
      // 拦截localStorage清除操作
      const originalLocalStorageClear = localStorage.clear;
      localStorage.clear = function() {
        console.log('[小梅花] 拦截localStorage.clear()操作');
        // 不执行清除，而是保存当前所有非登录相关的项
        const itemsToKeep = {};
        for (let i = 0; i < localStorage.length; i++) {
          const key = localStorage.key(i);
          if (key && !key.includes('login') && !key.includes('token') && 
              !key.includes('ticket') && !key.includes('session')) {
            itemsToKeep[key] = localStorage.getItem(key);
          }
        }
        
        // 执行原始清除
        originalLocalStorageClear.call(this);
        
        // 恢复保存的项
        for (const key in itemsToKeep) {
          originalLocalStorageSetItem.call(this, key, itemsToKeep[key]);
        }
      };
      
      // 拦截localStorage移除操作
      const originalLocalStorageRemoveItem = localStorage.removeItem;
      localStorage.removeItem = function(key) {
        if (key && (key.includes('login') || key.includes('token') || key.includes('ticket') || 
            key.includes('session') || key.includes('auth'))) {
          console.log(`[小梅花] 阻止移除关键localStorage项: ${key}`);
          return;
        }
        originalLocalStorageRemoveItem.call(this, key);
      };
      
      // 拦截cookie操作
      Object.defineProperty(document, 'cookie', {
        get: function() {
          return this._cookie || '';
        },
        set: function(value) {
          // 检查是否是设置过期的cookie
          if (value && (value.includes('expires') || value.includes('max-age'))) {
            // 如果是关键cookie且设置了过期，则阻止
            if (value.includes('login') || value.includes('token') || value.includes('ticket') || 
                value.includes('session') || value.includes('auth')) {
              console.log(`[小梅花] 阻止设置过期Cookie: ${value}`);
              // 移除过期时间，保留cookie值
              value = value.replace(/; expires=[^;]+/gi, '')
                          .replace(/; max-age=[^;]+/gi, '');
            }
          }
          this._cookie = value;
        }
      });
      
      // 拦截自动登出的定时器
      const originalSetTimeout = window.setTimeout;
      window.setTimeout = function(callback, timeout, ...args) {
        // 检查超时时间，如果是较长时间（可能是登出定时器），则延长时间
        if (typeof callback === 'function' && timeout > 60000) { // 大于1分钟的定时器
          const callbackStr = callback.toString().toLowerCase();
          // 检查回调函数是否包含登出相关关键词
          if (callbackStr.includes('logout') || callbackStr.includes('login') || 
              callbackStr.includes('expire') || callbackStr.includes('timeout')) {
            console.log(`[小梅花] 检测到可能的登出定时器，延长时间: ${timeout}ms -> ${timeout * 100}ms`);
            timeout = timeout * 100; // 将超时时间延长100倍
          }
        }
        return originalSetTimeout(callback, timeout, ...args);
      };
      
      // 监听页面可见性变化，在页面重新获得焦点时刷新登录状态
      document.addEventListener('visibilitychange', () => {
        if (document.visibilityState === 'visible') {
          console.log('[小梅花] 页面重新获得焦点，刷新登录状态');
          // 通知主进程刷新Cookie
          if (window.xiaomeihuaAPI && window.xiaomeihuaAPI.refreshCookies) {
            window.xiaomeihuaAPI.refreshCookies();
          }
        }
      });
      
        // 【增强】登录状态监控 - 优化版本
        let lastLoginState = null;
        let loginStateCheckCount = 0;
        let consecutiveFailureCount = 0; // 连续失败计数
        let loginSuccessDetected = false; // 登录成功检测标志
        let lastLoginCheckTime = 0; // 上次检测时间

        const checkLoginState = () => {
          loginStateCheckCount++;

          // 获取当前页面URL
          const currentUrl = window.location.href;
          console.log(`[小梅花] 检查登录状态 (#${loginStateCheckCount})，当前URL: ${currentUrl}`);

          // 【关键修复】如果是扫码登录页面，不要进行登录状态检测，避免误判
          if (currentUrl.includes('login') || currentUrl.includes('scan') ||
              currentUrl.includes('qrcode') || currentUrl.includes('auth') ||
              document.title.includes('登录') || document.title.includes('扫码')) {
            console.log(`[小梅花] 检测到扫码登录页面，跳过登录状态检测，避免误判`);
            lastLoginState = false;
            return;
          }

          // 检查页面是否完全加载
          const isPageReady = document.readyState === 'complete';
          if (!isPageReady) {
            console.log(`[小梅花] 页面仍在加载中 (${document.readyState})，等待完成...`);
            return; // 页面还在加载，暂不检测
          }

          // 针对特定网页的登录状态检测 - 更精确的检测逻辑
          let hasSpecificLoginIndicator = false;
          let specificWebsiteType = 'unknown';

          if (currentUrl.includes('store.weixin.qq.com')) {
            specificWebsiteType = 'wechat-store';
            // 微信小店特定检测 - 更精确的登录指示器
            hasSpecificLoginIndicator = !!(
              document.querySelector('.weui-desktop-account') ||
              document.querySelector('.account-info') ||
              document.querySelector('.shop-info') ||
              document.querySelector('[class*="shop-name"]') ||
              document.querySelector('[class*="merchant"]') ||
              document.querySelector('.kf-container') ||
              document.querySelector('.customer-service') ||
              document.querySelector('.shop-home') ||
              document.querySelector('.order-list') ||
              // 检查是否没有登录相关的UI元素
              (!document.querySelector('.login-container') &&
               !document.querySelector('.qr-code-login') &&
               !document.querySelector('.scan-qr'))
            );
          } else if (currentUrl.includes('filehelper.weixin.qq.com')) {
            specificWebsiteType = 'file-helper';
            // 文件传输助手特定检测
            hasSpecificLoginIndicator = !!(
              document.querySelector('.file-list') ||
              document.querySelector('.upload-area') ||
              document.querySelector('.file-item') ||
              document.querySelector('[class*="file-helper"]') ||
              document.querySelector('.chat-container') ||
              document.querySelector('.file-transfer') ||
              // 检查是否没有登录相关的UI元素
              (!document.querySelector('.login-qr') &&
               !document.querySelector('.scan-login') &&
               !document.querySelector('.qr-login'))
            );
          } else if (currentUrl.includes('channels.weixin.qq.com')) {
            specificWebsiteType = 'channels';
            // 视频号助手特定检测
            hasSpecificLoginIndicator = !!(
              document.querySelector('.channels-container') ||
              document.querySelector('.video-list') ||
              document.querySelector('.creator-center') ||
              document.querySelector('[class*="channels"]') ||
              document.querySelector('.dashboard') ||
              document.querySelector('.creator-dashboard') ||
              // 检查是否没有登录相关的UI元素
              (!document.querySelector('.login-page') &&
               !document.querySelector('.qrcode-login') &&
               !document.querySelector('.scan-code'))
            );
          }

          console.log(`[小梅花] 网站类型: ${specificWebsiteType}, 特定登录指示器: ${hasSpecificLoginIndicator}`);

          // 通用登录状态指示器检测 - 扩展检测范围
          const loginIndicators = [
            // 通用登录指示器
            document.querySelector('.login-info'),
            document.querySelector('.user-info'),
            document.querySelector('.avatar'),
            document.querySelector('[class*="user"]'),
            document.querySelector('[class*="login"]'),
            document.querySelector('[id*="user"]'),
            document.querySelector('[id*="login"]'),

            // 微信特定的登录指示器
            document.querySelector('.weui-desktop-account'),
            document.querySelector('.account-info'),
            document.querySelector('.account-avatar'),
            document.querySelector('.user-avatar'),
            document.querySelector('.profile-info'),
            document.querySelector('.nickname'),
            document.querySelector('[class*="nickname"]'),
            document.querySelector('[class*="avatar"]'),
            document.querySelector('[class*="profile"]'),
            document.querySelector('[class*="account"]'),

            // 检查是否有退出登录按钮（表示已登录）
            document.querySelector('[class*="logout"]'),
            document.querySelector('[class*="signout"]'),
            document.querySelector('a[href*="logout"]'),
            document.querySelector('button[onclick*="logout"]'),

            // 检查是否有主要功能区域（表示已登录）
            document.querySelector('.main-content'),
            document.querySelector('.workspace'),
            document.querySelector('.dashboard'),
            document.querySelector('.content-area')
          ];

          const hasLoginIndicator = loginIndicators.some(indicator => indicator !== null) || hasSpecificLoginIndicator;

          // 增强的Cookie检测 - 更全面的Cookie检查
          let hasLoginCookie = false;
          const cookieString = document.cookie;

          // 通用微信登录Cookie检测
          const generalCookieKeywords = [
            'wxticket', 'login_ticket', 'session', 'token', 'openid', 'unionid',
            'access_token', 'refresh_token', 'wx_', 'weixin', 'auth', 'login',
            'user', 'uid', 'sid', 'sessionid', 'jsessionid', 'phpsessid'
          ];

          const generalCookieCheck = generalCookieKeywords.some(keyword => 
            cookieString.toLowerCase().includes(keyword.toLowerCase())
          );

          // 针对特定网页的Cookie检测
          let specificCookieCheck = false;
          if (currentUrl.includes('store.weixin.qq.com')) {
            const storeCookieKeywords = ['store_', 'shop_', 'merchant_', 'kf_', 'wechat_store'];
            specificCookieCheck = storeCookieKeywords.some(keyword => 
              cookieString.toLowerCase().includes(keyword.toLowerCase())
            );
          } else if (currentUrl.includes('filehelper.weixin.qq.com')) {
            const helperCookieKeywords = ['filehelper_', 'file_', 'helper_', 'upload_'];
            specificCookieCheck = helperCookieKeywords.some(keyword => 
              cookieString.toLowerCase().includes(keyword.toLowerCase())
            );
          } else if (currentUrl.includes('channels.weixin.qq.com')) {
            const channelsCookieKeywords = ['channels_', 'video_', 'creator_', 'dashboard_'];
            specificCookieCheck = channelsCookieKeywords.some(keyword => 
              cookieString.toLowerCase().includes(keyword.toLowerCase())
            );
          }

          hasLoginCookie = generalCookieCheck || specificCookieCheck;

          // 检查localStorage中的登录状态 - 更精确的检查
          let hasLoginStorage = false;
          try {
            const storageKeys = [];
            for (let i = 0; i < localStorage.length; i++) {
              storageKeys.push(localStorage.key(i));
            }

            // 通用登录相关键名检测
            const generalStorageKeywords = [
              'login', 'token', 'user', 'auth', 'session', 'wx', 'weixin',
              'openid', 'unionid', 'account', 'profile', 'userid', 'username'
            ];

            const generalStorageCheck = storageKeys.some(key => key && 
              generalStorageKeywords.some(keyword => 
                key.toLowerCase().includes(keyword.toLowerCase())
              )
            );

            // 针对特定网页的localStorage检测
            let specificStorageCheck = false;
            if (currentUrl.includes('store.weixin.qq.com')) {
              const storeStorageKeywords = ['store', 'shop', 'merchant', 'kf', 'customer'];
              specificStorageCheck = storageKeys.some(key => key && 
                storeStorageKeywords.some(keyword => 
                  key.toLowerCase().includes(keyword.toLowerCase())
                )
              );
            } else if (currentUrl.includes('filehelper.weixin.qq.com')) {
              const helperStorageKeywords = ['filehelper', 'file', 'helper', 'upload'];
              specificStorageCheck = storageKeys.some(key => key && 
                helperStorageKeywords.some(keyword => 
                  key.toLowerCase().includes(keyword.toLowerCase())
                )
              );
            } else if (currentUrl.includes('channels.weixin.qq.com')) {
              const channelsStorageKeywords = ['channels', 'video', 'creator', 'dashboard'];
              specificStorageCheck = storageKeys.some(key => key && 
                channelsStorageKeywords.some(keyword => 
                  key.toLowerCase().includes(keyword.toLowerCase())
                )
              );
            }

            hasLoginStorage = generalStorageCheck || specificStorageCheck;

            if (hasLoginStorage) {
              console.log(`[小梅花] 在localStorage中发现登录相关数据，键名: ${storageKeys.slice(0, 5).join(', ')}${storageKeys.length > 5 ? '...' : ''}`);
            }
          } catch (e) {
            console.error('[小梅花] 检查localStorage失败:', e);
          }

          // 检查URL中的登录状态指示 - 更精确的URL检查
          const hasLoginUrl = !currentUrl.includes('login') && 
                             !currentUrl.includes('signin') && 
                             !currentUrl.includes('auth') &&
                             (currentUrl.includes('dashboard') ||
                              currentUrl.includes('profile') ||
                              currentUrl.includes('account') ||
                              currentUrl.includes('home') ||
                              currentUrl.includes('main'));

          // 【关键修复】更严格的登录状态判断，避免误判
          const loginIndicatorCount = [hasLoginIndicator, hasLoginCookie, hasLoginStorage, hasLoginUrl].filter(Boolean).length;

          // 【修复】提高判断标准，需要更多指标才认为已登录，并且排除登录页面
          const currentLoginState = (loginIndicatorCount >= 3 ||
                                   (hasSpecificLoginIndicator && hasLoginCookie && hasLoginStorage)) &&
                                   !currentUrl.includes('login') &&
                                   !currentUrl.includes('scan') &&
                                   !document.title.includes('登录') &&
                                   !document.title.includes('扫码');

          console.log(`[小梅花] 登录状态检查结果: 页面指示器=${hasLoginIndicator}, Cookie=${hasLoginCookie}, Storage=${hasLoginStorage}, URL=${hasLoginUrl}, 综合判断=${currentLoginState} (指标数量: ${loginIndicatorCount})`);

          // 如果登录状态发生变化
          if (currentLoginState !== lastLoginState) {
            console.log(`[小梅花] 登录状态变化: ${lastLoginState} -> ${currentLoginState}`);
            lastLoginState = currentLoginState;
            consecutiveFailureCount = 0; // 重置失败计数

            // 通知主进程登录状态变化
            if (window.ipcRenderer) {
              window.ipcRenderer.send('login-state-changed', currentLoginState);
            }

            if (currentLoginState && !loginSuccessDetected) {
              loginSuccessDetected = true;
              console.log('[小梅花] 🎉 首次检测到登录成功，启动完整保存和检测流程');

              // 【新增】标记最近登录时间到localStorage
              try {
                localStorage.setItem('xmh_recent_login_time', Date.now().toString());
                console.log('[小梅花] ✅ 已标记最近登录时间');
              } catch (err) {
                console.error('[小梅花] 标记最近登录时间失败:', err);
              }

              // 【增强】保存登录状态并触发店铺检测
              const saveLoginStateAndTriggerDetection = () => {
                console.log('[小梅花] 执行登录状态保存和店铺检测触发...');

                // 通知主进程保存Cookie
                if (window.ipcRenderer) {
                  console.log('[小梅花] 发送IPC消息: save-session-state');
                  window.ipcRenderer.send('save-session-state');

                  console.log('[小梅花] 发送IPC消息: force-save-login-state');
                  window.ipcRenderer.send('force-save-login-state');

                  console.log('[小梅花] 发送IPC消息: login-state-changed');
                  window.ipcRenderer.send('login-state-changed', true);

                  // 【增强】触发店铺检测
                  console.log('[小梅花] 发送IPC消息: trigger-shop-detection');
                  window.ipcRenderer.send('trigger-shop-detection', {
                    timestamp: Date.now(),
                    url: window.location.href,
                    title: document.title,
                    loginMethod: 'scan_code',
                    isNewLogin: true,
                    forceDetection: true
                  });
                } else {
                  // 使用标题通信方式
                  sendIpcMessage('save-session-state', {});
                  sendIpcMessage('force-save-login-state', {});
                  sendIpcMessage('login-state-changed', true);
                  sendIpcMessage('trigger-shop-detection', {
                    timestamp: Date.now(),
                    url: window.location.href,
                    title: document.title,
                    loginMethod: 'scan_code',
                    isNewLogin: true,
                    forceDetection: true
                  });
                }

                // 强制保存Cookie确保登录状态被记录
                console.log('[小梅花] 已通知主进程强制保存Cookie和触发店铺检测');
              };

              // 立即执行一次
              saveLoginStateAndTriggerDetection();

              // 延迟再执行一次，确保所有数据都已加载
              setTimeout(saveLoginStateAndTriggerDetection, 3000);

              // 再次延迟执行，确保店铺信息完全加载
              setTimeout(saveLoginStateAndTriggerDetection, 6000);
            } else if (currentLoginState && loginSuccessDetected) {
              // 已经检测过登录成功，只做常规保存
              if (window.ipcRenderer) {
                window.ipcRenderer.send('force-save-login-state');
              }
            }
          } else if (!currentLoginState) {
            consecutiveFailureCount++;
          }

          // 如果长时间没有登录状态且连续失败次数较多，尝试恢复
          if (!currentLoginState && consecutiveFailureCount > 3 && loginStateCheckCount > 5) {
            console.log('[小梅花] 连续多次未检测到登录状态，尝试恢复Cookie');
            if (window.ipcRenderer) {
              window.ipcRenderer.send('restore-key-cookies');
            }
            consecutiveFailureCount = 0; // 重置计数器
            loginStateCheckCount = 0; // 重置检查计数器
          }
        };

      // 立即检查一次
      setTimeout(checkLoginState, 2000);

      // 定期检查登录状态 - 更频繁的检查
      setInterval(checkLoginState, 30000); // 每30秒检查一次
    };
    
    // 【新增】检查是否是扫码登录页面
    function isQRCodeLoginPage() {
      const currentUrl = window.location.href;
      const currentTitle = document.title;

      return currentUrl.includes('login') ||
             currentUrl.includes('scan') ||
             currentUrl.includes('qrcode') ||
             currentUrl.includes('auth') ||
             currentTitle.includes('登录') ||
             currentTitle.includes('扫码') ||
             document.querySelector('canvas') || // 二维码canvas
             document.querySelector('[class*="qr"]') || // 二维码相关元素
             document.querySelector('[id*="qr"]');
    }

    // 【新增】扫码登录页面专用处理
    function handleQRCodeLoginPage() {
      if (isQRCodeLoginPage()) {
        console.log('[小梅花] 检测到扫码登录页面，清理可能的残留登录状态');

        // 清理可能的残留登录状态
        lastLoginState = false;
        loginSuccessDetected = false;
        consecutiveFailureCount = 0;

        // 清理本地存储的登录标记
        try {
          localStorage.removeItem('xiaomeihua_login_detected');
          localStorage.removeItem('login_method');
          localStorage.removeItem('session_token');
          localStorage.removeItem('xmh_recent_login_time');
        } catch (e) {
          console.log('[小梅花] 清理本地存储失败:', e);
        }

        console.log('[小梅花] 扫码登录页面状态清理完成');
        return true;
      }
      return false;
    }

    // 执行防止登出脚本
    preventLogout();

    // 在页面加载完成后再次执行，确保覆盖所有情况
    window.addEventListener('load', () => {
      preventLogout();

      // 【新增】检查是否是扫码登录页面
      handleQRCodeLoginPage();

      // 页面加载完成后检查登录状态
      setTimeout(() => {
        console.log('[小梅花] 页面加载完成，检查登录状态');

        // 【关键修复】如果是登录页面，不要检测登录状态，避免误判
        const currentUrl = window.location.href;
        if (currentUrl.includes('login') || currentUrl.includes('scan') ||
            currentUrl.includes('qrcode') || currentUrl.includes('auth') ||
            document.title.includes('登录') || document.title.includes('扫码')) {
          console.log('[小梅花] 页面加载完成，但检测到是登录页面，跳过登录状态检测');
          return;
        }

        checkLoginState();

        // 【修复】更严格的登录状态检测，避免误判
        const hasLogin = (document.cookie.includes('wxticket') ||
                        document.cookie.includes('login_ticket') ||
                        document.cookie.includes('session') ||
                        document.cookie.includes('token')) &&
                        !currentUrl.includes('login') &&
                        !currentUrl.includes('scan') &&
                        !document.title.includes('登录') &&
                        !document.title.includes('扫码');

        if (hasLogin) {
          console.log('[小梅花] 页面加载完成后检测到有效登录状态，立即保存');
          if (window.ipcRenderer) {
            window.ipcRenderer.send('login-state-changed', true);
            window.ipcRenderer.send('force-save-login-state');
          }
        }
      }, 2000);
    });

    // 【彻底重写】实现最精准的页面退出检测机制
    let isPageExiting = false;
    let exitDetectionTimeout = null;
    let lastExitEventTime = 0;
    let exitDetectionCount = 0;

    // 页面退出检测函数
    function handlePageExit(eventType) {
      const now = Date.now();

      // 防止短时间内重复触发，但允许多次确认
      if (now - lastExitEventTime < 500) {
        return;
      }

      exitDetectionCount++;
      lastExitEventTime = now;
      console.log(`[小梅花] 🚪 检测到页面退出事件 #${exitDetectionCount}: ${eventType}`);

      // 【增强】检查是否是真正的退出登录
      const isLogoutExit = checkIfLogoutExit();

      // 【新增】检查页面状态变化
      const pageStateInfo = getPageStateInfo();

      // 立即通知主进程页面即将退出
      if (window.ipcRenderer) {
        window.ipcRenderer.send('page-exit-detected', {
          eventType: eventType,
          timestamp: now,
          url: window.location.href,
          isLogoutExit: isLogoutExit,
          title: document.title,
          exitCount: exitDetectionCount,
          pageState: pageStateInfo,
          userAgent: navigator.userAgent
        });
      }

      // 【新增】如果是退出登录，立即执行额外的清理
      if (isLogoutExit) {
        console.log('[小梅花] 🔄 检测到退出登录，立即执行本地清理...');
        performImmediateCleanup();
      }

      // 设置超时重置标志，防止误判
      if (exitDetectionTimeout) {
        clearTimeout(exitDetectionTimeout);
      }
      exitDetectionTimeout = setTimeout(() => {
        isPageExiting = false;
        console.log('[小梅花] 重置页面退出检测标志');
      }, 2000); // 缩短重置时间
    }

    // 【增强】检查是否是退出登录
    function checkIfLogoutExit() {
      try {
        // 检查URL变化
        const url = window.location.href;
        if (url.includes('login') ||
            url.includes('scan') ||
            url.includes('qrcode') ||
            url.includes('auth') ||
            url.includes('signin') ||
            url.includes('logout') ||
            url.includes('exit')) {
          console.log('[小梅花] URL检测到退出登录:', url);
          return true;
        }

        // 检查页面标题变化
        const title = document.title;
        if (title.includes('登录') ||
            title.includes('扫码') ||
            title.includes('二维码') ||
            title.includes('退出') ||
            title.includes('注销') ||
            title.includes('登出')) {
          console.log('[小梅花] 标题检测到退出登录:', title);
          return true;
        }

        // 检查页面内容
        const loginElements = document.querySelectorAll('[class*="login"], [class*="scan"], [id*="login"], [id*="scan"], [class*="logout"], [id*="logout"]');
        if (loginElements.length > 0) {
          console.log('[小梅花] 页面元素检测到退出登录，元素数量:', loginElements.length);
          return true;
        }

        // 检查是否有二维码元素
        const qrElements = document.querySelectorAll('img[src*="qrcode"], canvas, [class*="qr"], [id*="qr"]');
        if (qrElements.length > 0) {
          console.log('[小梅花] 二维码元素检测到退出登录，元素数量:', qrElements.length);
          return true;
        }

        return false;
      } catch (e) {
        console.log('[小梅花] 检查退出登录状态失败:', e);
        return false;
      }
    }

    // 【新增】获取页面状态信息
    function getPageStateInfo() {
      try {
        return {
          url: window.location.href,
          title: document.title,
          readyState: document.readyState,
          hasLoginElements: document.querySelectorAll('[class*="login"], [id*="login"]').length > 0,
          hasQRElements: document.querySelectorAll('img[src*="qrcode"], canvas, [class*="qr"]').length > 0,
          cookieCount: document.cookie.split(';').length,
          localStorageKeys: Object.keys(localStorage).length,
          sessionStorageKeys: Object.keys(sessionStorage).length
        };
      } catch (e) {
        console.log('[小梅花] 获取页面状态信息失败:', e);
        return {};
      }
    }

    // 【新增】立即执行本地清理
    function performImmediateCleanup() {
      try {
        console.log('[小梅花] 🧹 执行立即本地清理...');

        // 清理localStorage
        const localKeys = Object.keys(localStorage);
        localKeys.forEach(key => {
          if (key.includes('shop') ||
              key.includes('store') ||
              key.includes('login') ||
              key.includes('auth') ||
              key.includes('token') ||
              key.includes('session') ||
              key.includes('cookie')) {
            localStorage.removeItem(key);
            console.log(`[小梅花] 清理localStorage: ${key}`);
          }
        });

        // 清理sessionStorage
        const sessionKeys = Object.keys(sessionStorage);
        sessionKeys.forEach(key => {
          if (key.includes('shop') ||
              key.includes('store') ||
              key.includes('login') ||
              key.includes('auth') ||
              key.includes('token') ||
              key.includes('session') ||
              key.includes('cookie')) {
            sessionStorage.removeItem(key);
            console.log(`[小梅花] 清理sessionStorage: ${key}`);
          }
        });

        // 清理cookies
        if (document.cookie) {
          document.cookie.split(";").forEach(function(c) {
            document.cookie = c.replace(/^ +/, "").replace(/=.*/, "=;expires=" + new Date().toUTCString() + ";path=/");
          });
          console.log('[小梅花] 已清理所有cookies');
        }

        console.log('[小梅花] ✅ 立即本地清理完成');
      } catch (e) {
        console.log('[小梅花] 立即本地清理失败:', e);
      }
    }

    // 1. beforeunload 事件 - 页面即将卸载
    window.addEventListener('beforeunload', (event) => {
      handlePageExit('beforeunload');
    });

    // 2. unload 事件 - 页面正在卸载
    window.addEventListener('unload', (event) => {
      handlePageExit('unload');
    });

    // 3. pagehide 事件 - 页面隐藏（包括后退/前进）
    window.addEventListener('pagehide', (event) => {
      handlePageExit('pagehide');
    });

    // 4. visibilitychange 事件 - 页面可见性变化
    document.addEventListener('visibilitychange', () => {
      if (document.visibilityState === 'hidden') {
        // 页面变为隐藏状态，可能是退出
        handlePageExit('visibilitychange-hidden');
      } else if (document.visibilityState === 'visible') {
        // 页面重新可见
        isPageExiting = false; // 重置退出标志
        console.log('[小梅花] 页面重新获得焦点，检查登录状态');
        setTimeout(() => {
          // 【关键修复】如果是扫码登录页面，处理并跳过登录状态检测
          if (handleQRCodeLoginPage()) {
            console.log('[小梅花] 页面重新获得焦点，检测到是扫码登录页面，已处理并跳过登录状态检测');
            return;
          }

          checkLoginState();

          // 【修复】更严格的登录状态检测
          const hasLogin = (document.cookie.includes('wxticket') ||
                          document.cookie.includes('login_ticket') ||
                          document.cookie.includes('session') ||
                          document.cookie.includes('token')) &&
                          !currentUrl.includes('login') &&
                          !currentUrl.includes('scan') &&
                          !document.title.includes('登录') &&
                          !document.title.includes('扫码');

          if (hasLogin) {
            console.log('[小梅花] 页面重新获得焦点时检测到有效登录状态，保存状态');
            if (window.ipcRenderer) {
              window.ipcRenderer.send('force-save-login-state');
            }
          }
        }, 1000);
      }
    });

    // 5. 监听页面导航变化
    let lastUrl = window.location.href;
    const urlChangeObserver = new MutationObserver(() => {
      if (window.location.href !== lastUrl) {
        console.log(`[小梅花] 检测到URL变化: ${lastUrl} -> ${window.location.href}`);

        // 如果是导航到登录页面，说明可能退出了
        if (window.location.href.includes('login') ||
            window.location.href.includes('scan') ||
            window.location.href.includes('qrcode')) {
          handlePageExit('navigation-to-login');
        }

        lastUrl = window.location.href;
      }
    });

    // 开始观察URL变化
    urlChangeObserver.observe(document, { subtree: true, childList: true });

    // 【新增】6. 监听DOM变化，检测登录页面元素
    const loginElementObserver = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'childList') {
          // 检查是否出现了登录相关元素
          const addedNodes = Array.from(mutation.addedNodes);
          const hasLoginElements = addedNodes.some(node => {
            if (node.nodeType === Node.ELEMENT_NODE) {
              const element = node;
              return element.className && (
                element.className.includes('login') ||
                element.className.includes('scan') ||
                element.className.includes('qr') ||
                element.className.includes('auth') ||
                element.className.includes('logout')
              );
            }
            return false;
          });

          if (hasLoginElements) {
            console.log('[小梅花] DOM变化检测到登录元素');
            handlePageExit('dom-change-login');
          }
        }
      });
    });

    // 开始观察DOM变化
    if (document.body) {
      loginElementObserver.observe(document.body, {
        childList: true,
        subtree: true
      });
    }

    // 【新增】7. 定期检查URL变化（备用方案）
    const urlCheckInterval = setInterval(() => {
      const currentUrl = window.location.href;
      if (currentUrl !== lastUrl) {
        console.log(`[小梅花] 定期检查发现URL变化: ${lastUrl} -> ${currentUrl}`);

        // 检查是否是退出登录的URL变化
        if (currentUrl.includes('login') ||
            currentUrl.includes('scan') ||
            currentUrl.includes('qrcode') ||
            currentUrl.includes('auth') ||
            currentUrl.includes('logout') ||
            currentUrl.includes('signin')) {
          handlePageExit('url-check-logout');
        }

        lastUrl = currentUrl;
      }
    }, 2000);

    // 8. 监听网络状态变化
    window.addEventListener('online', () => {
      console.log('[小梅花] 网络重新连接');
      isPageExiting = false;
    });

    window.addEventListener('offline', () => {
      console.log('[小梅花] 网络断开连接');
      handlePageExit('network-offline');
    });

    // 【新增】9. 页面卸载时清理所有监听器
    window.addEventListener('beforeunload', () => {
      clearInterval(urlCheckInterval);
      urlChangeObserver.disconnect();
      loginElementObserver.disconnect();
      console.log('[小梅花] 已清理所有监听器');
    });
  }
});