// ==UserScript==
// @name         小梅花AI智能客服系统
// @namespace    http://xiaomeihuakefu.cn/
// @version      1.0.0
// @description  小梅花AI智能客服助手，只在微信商店客服页面执行
// <AUTHOR>
// @match        https://store.weixin.qq.com/shop/kf*
// @match        https://store.weixin.qq.com/shop/home*
// @grant        GM_setValue
// @grant        GM_getValue
// @grant        GM_deleteValue
// @grant        GM_xmlhttpRequest
// @grant        GM_addStyle
// @grant        GM_openInTab
// @connect      xiaomeihuakefu.cn
// @connect      api.xiaomeihuakefu.cn
// @run-at       document-end
// ==/UserScript==

(function() {
    'use strict';
    
    console.log('🎯 小梅花AI智能客服系统已加载');
    console.log('📍 当前页面URL:', window.location.href);
    console.log('✅ 这个脚本只应该在微信商店页面执行');
    
    // 检查是否在正确的页面
    const currentUrl = window.location.href;
    const isCorrectPage = currentUrl.includes('store.weixin.qq.com/shop/kf') || 
                         currentUrl.includes('store.weixin.qq.com/shop/home');
    
    if (!isCorrectPage) {
        console.error('❌ 错误！脚本在错误的页面执行了！');
        console.error('❌ 当前URL:', currentUrl);
        console.error('❌ 这表明URL匹配功能没有正常工作！');
        
        // 创建错误提示
        const errorDiv = document.createElement('div');
        errorDiv.id = 'xiaomeihua-error-indicator';
        errorDiv.style.cssText = `
            position: fixed;
            top: 10px;
            right: 10px;
            background: #f44336;
            color: white;
            padding: 15px;
            border-radius: 5px;
            z-index: 999999;
            font-size: 14px;
            font-weight: bold;
            border: 2px solid #d32f2f;
            box-shadow: 0 4px 8px rgba(0,0,0,0.3);
        `;
        errorDiv.innerHTML = `
            ❌ 脚本执行错误！<br>
            不应该在此页面执行<br>
            <small>URL: ${currentUrl.substring(0, 50)}...</small>
        `;
        document.body.appendChild(errorDiv);
        
        // 5秒后自动移除提示
        setTimeout(() => {
            if (document.getElementById('xiaomeihua-error-indicator')) {
                document.getElementById('xiaomeihua-error-indicator').remove();
            }
        }, 5000);
        
        return; // 停止执行
    }
    
    console.log('✅ 脚本在正确的页面执行');
    
    // 创建成功提示
    const successDiv = document.createElement('div');
    successDiv.id = 'xiaomeihua-success-indicator';
    successDiv.style.cssText = `
        position: fixed;
        top: 10px;
        left: 10px;
        background: #4caf50;
        color: white;
        padding: 15px;
        border-radius: 5px;
        z-index: 999999;
        font-size: 14px;
        font-weight: bold;
        border: 2px solid #388e3c;
        box-shadow: 0 4px 8px rgba(0,0,0,0.3);
    `;
    successDiv.innerHTML = `
        ✅ 脚本正确执行！<br>
        URL匹配功能正常<br>
        <small>页面: 微信商店</small>
    `;
    document.body.appendChild(successDiv);
    
    // 3秒后自动移除提示
    setTimeout(() => {
        if (document.getElementById('xiaomeihua-success-indicator')) {
            document.getElementById('xiaomeihua-success-indicator').remove();
        }
    }, 3000);
    
    // 模拟一些基本的客服功能
    function initCustomerService() {
        console.log('🤖 初始化客服功能...');
        
        // 检查是否有卡密
        if (window.xiaomeihuaLicenseKey) {
            console.log('🔑 卡密已设置:', window.xiaomeihuaLicenseKey.substring(0, 8) + '...');
        } else {
            console.warn('⚠️ 未找到卡密信息');
        }
        
        // 检查店铺信息
        if (window.xiaomeihuaShopInfo) {
            console.log('🏪 店铺信息:', window.xiaomeihuaShopInfo);
        } else {
            console.warn('⚠️ 未找到店铺信息');
        }
        
        console.log('✅ 客服功能初始化完成');
    }
    
    // 等待页面完全加载后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initCustomerService);
    } else {
        initCustomerService();
    }
    
    // 添加一些样式
    GM_addStyle(`
        #xiaomeihua-success-indicator,
        #xiaomeihua-error-indicator {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            transition: all 0.3s ease;
        }
        
        #xiaomeihua-success-indicator:hover,
        #xiaomeihua-error-indicator:hover {
            transform: scale(1.05);
        }
    `);
    
})();
