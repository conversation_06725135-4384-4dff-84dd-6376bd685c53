#!/bin/bash

# 小梅花AI智能客服 - 修复已损坏应用程序脚本
# 此脚本用于修复macOS上"应用程序已损坏"的问题

echo "🔧 小梅花AI智能客服 - 修复工具"
echo "================================"
echo ""

# 获取应用程序路径
APP_NAME="小梅花AI智能客服.app"
APP_PATH="/Applications/$APP_NAME"

# 检查应用程序是否存在
if [ ! -d "$APP_PATH" ]; then
    echo "❌ 未找到应用程序: $APP_PATH"
    echo "请确保应用程序已正确安装到应用程序文件夹中。"
    echo ""
    read -p "按回车键退出..."
    exit 1
fi

echo "📍 找到应用程序: $APP_PATH"
echo ""

# 移除扩展属性（隔离标记）
echo "🧹 正在移除隔离标记..."
sudo xattr -rd com.apple.quarantine "$APP_PATH"

if [ $? -eq 0 ]; then
    echo "✅ 隔离标记已移除"
else
    echo "⚠️  移除隔离标记时出现问题，但可能不影响使用"
fi

echo ""

# 重新签名应用程序（使用临时签名）
echo "✍️  正在重新签名应用程序..."
sudo codesign --force --deep --sign - "$APP_PATH"

if [ $? -eq 0 ]; then
    echo "✅ 应用程序已重新签名"
else
    echo "⚠️  重新签名时出现问题，但应用程序可能仍然可用"
fi

echo ""
echo "🎉 修复完成！"
echo ""
echo "现在您可以尝试启动小梅花AI智能客服应用程序。"
echo "如果仍然遇到问题，请联系技术支持。"
echo ""

read -p "按回车键退出..."
