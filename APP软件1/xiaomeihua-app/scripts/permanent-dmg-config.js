const fs = require('fs');
const path = require('path');

/**
 * 永久DMG配置锁定脚本
 * 确保签名修复、窗口设置和图片包含始终生效
 */
class PermanentDMGConfig {
  constructor() {
    this.projectRoot = path.dirname(__dirname);
    this.packageJsonPath = path.join(this.projectRoot, 'package.json');
    this.buildDir = path.join(this.projectRoot, 'build');
    this.scriptsDir = path.join(this.projectRoot, 'scripts');
    
    console.log('🔒 启动DMG配置永久锁定...');
  }

  async lockConfiguration() {
    try {
      // 1. 验证必要文件存在
      await this.validateRequiredFiles();
      
      // 2. 锁定package.json配置
      await this.lockPackageJsonConfig();
      
      // 3. 验证签名脚本完整性
      await this.validateSigningScripts();
      
      // 4. 创建配置保护机制
      await this.createConfigProtection();
      
      console.log('✅ DMG配置永久锁定完成');
      
    } catch (error) {
      console.error('❌ DMG配置锁定失败:', error.message);
      throw error;
    }
  }

  async validateRequiredFiles() {
    console.log('🔍 验证必要文件...');
    
    const requiredFiles = [
      path.join(this.buildDir, 'Mac安装教程.png'),
      path.join(this.buildDir, 'icon.icns'),
      path.join(this.scriptsDir, 'after-pack-sign.js'),
      path.join(this.scriptsDir, 'after-dmg-fix.js')
    ];
    
    for (const file of requiredFiles) {
      if (!fs.existsSync(file)) {
        throw new Error(`必要文件缺失: ${path.basename(file)}`);
      }
    }
    
    console.log('✅ 所有必要文件验证通过');
  }

  async lockPackageJsonConfig() {
    console.log('🔧 锁定package.json配置...');
    
    const packageJson = JSON.parse(fs.readFileSync(this.packageJsonPath, 'utf8'));
    
    // 确保DMG配置正确
    const requiredDMGConfig = {
      title: "${productName}-${version}",
      icon: "build/icon.icns",
      iconSize: 100,
      contents: [
        {
          x: 300,
          y: 100,
          type: "file",
          path: "Mac安装教程.png"
        },
        {
          x: 160,
          y: 280
        },
        {
          x: 480,
          y: 280,
          type: "link",
          path: "/Applications"
        }
      ],
      window: {
        width: 600,
        height: 480,
        x: 438,
        y: 230
      },
      backgroundColor: "#ffffff",
      artifactName: "${productName}-${version}-${arch}.${ext}",
      sign: false
    };

    // 确保构建钩子正确
    const requiredHooks = {
      afterPack: "scripts/after-pack-sign.js",
      afterAllArtifactBuild: "scripts/after-dmg-fix.js"
    };

    // 更新配置
    packageJson.build.dmg = requiredDMGConfig;
    Object.assign(packageJson.build, requiredHooks);
    
    // 添加配置锁定标记（在根级别，不在build内）
    packageJson.configLockInfo = {
      timestamp: new Date().toISOString(),
      version: "1.0.0",
      description: "此配置已永久锁定，包含签名修复和图片布局",
      dmgOptimized: true,
      errorHandlingImproved: true
    };

    // 写回文件
    fs.writeFileSync(this.packageJsonPath, JSON.stringify(packageJson, null, 2), 'utf8');
    
    console.log('✅ package.json配置已锁定');
  }

  async validateSigningScripts() {
    console.log('🔍 验证签名脚本完整性...');
    
    const afterPackScript = path.join(this.scriptsDir, 'after-pack-sign.js');
    const afterDMGScript = path.join(this.scriptsDir, 'after-dmg-fix.js');
    
    // 验证after-pack-sign.js关键功能
    const afterPackContent = fs.readFileSync(afterPackScript, 'utf8');
    const hasCleanAttributes = afterPackContent.includes('cleanExtendedAttributes');
    const hasDeepSigning = afterPackContent.includes('performDeepSigning');
    const hasVerification = afterPackContent.includes('verifySignature');
    
    if (!hasCleanAttributes || !hasDeepSigning || !hasVerification) {
      throw new Error('after-pack-sign.js 脚本不完整');
    }
    
    // 验证after-dmg-fix.js关键功能
    const afterDMGContent = fs.readFileSync(afterDMGScript, 'utf8');
    const hasFixDMG = afterDMGContent.includes('fixDMG');
    const hasRecreateDMG = afterDMGContent.includes('recreateDMG');
    const hasTutorialImage = afterDMGContent.includes('Mac安装教程.png');
    
    if (!hasFixDMG || !hasRecreateDMG || !hasTutorialImage) {
      throw new Error('after-dmg-fix.js 脚本不完整');
    }
    
    console.log('✅ 签名脚本完整性验证通过');
  }

  async createConfigProtection() {
    console.log('🛡️ 创建配置保护机制...');
    
    // 创建配置验证脚本
    const configValidatorPath = path.join(this.scriptsDir, 'validate-config.js');
    const validatorContent = `// 配置验证脚本 - 自动生成，请勿手动修改
const fs = require('fs');
const path = require('path');

function validateConfig() {
  const packageJsonPath = path.join(__dirname, '..', 'package.json');
  const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
  
  // 验证DMG配置
  const dmgConfig = packageJson.build.dmg;
  if (!dmgConfig || !dmgConfig.contents || dmgConfig.contents.length < 3) {
    throw new Error('DMG配置不完整');
  }
  
  // 验证构建钩子
  if (!packageJson.build.afterPack || !packageJson.build.afterAllArtifactBuild) {
    throw new Error('构建钩子配置缺失');
  }
  
  console.log('✅ 配置验证通过');
}

if (require.main === module) {
  validateConfig();
}

module.exports = validateConfig;
`;

    fs.writeFileSync(configValidatorPath, validatorContent, 'utf8');
    
    // 更新package.json添加预构建验证
    const packageJson = JSON.parse(fs.readFileSync(this.packageJsonPath, 'utf8'));
    
    // 为所有构建命令添加配置验证
    const buildCommands = Object.keys(packageJson.scripts).filter(cmd => cmd.startsWith('build:'));
    
    for (const cmd of buildCommands) {
      const originalCommand = packageJson.scripts[cmd];
      if (!originalCommand.includes('validate-config')) {
        packageJson.scripts[cmd] = `node scripts/validate-config.js && ${originalCommand}`;
      }
    }
    
    fs.writeFileSync(this.packageJsonPath, JSON.stringify(packageJson, null, 2), 'utf8');
    
    console.log('✅ 配置保护机制已创建');
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  const config = new PermanentDMGConfig();
  config.lockConfiguration().catch(console.error);
}

module.exports = PermanentDMGConfig;