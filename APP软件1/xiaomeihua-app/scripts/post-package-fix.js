#!/usr/bin/env node

/**
 * 简洁的后处理签名脚本 
 * 目标：让应用显示"无法验证"而不是"已损坏"
 * 通过正确的adhoc签名实现资源封装和Info.plist绑定
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

class PostPackageFix {
    constructor() {
        this.projectRoot = path.join(__dirname, '..');
        this.distDir = path.join(this.projectRoot, 'dist');
        this.packageJson = require(path.join(this.projectRoot, 'package.json'));
        this.productName = this.packageJson.productName;
    }

    log(message, type = 'info') {
        const colors = {
            info: '\x1b[36m',    // 青色
            success: '\x1b[32m', // 绿色  
            warning: '\x1b[33m', // 黄色
            error: '\x1b[31m'    // 红色
        };
        const reset = '\x1b[0m';
        const timestamp = new Date().toLocaleTimeString();
        console.log(`${colors[type]}[${timestamp}] ${message}${reset}`);
    }

    exec(command, options = {}) {
        try {
            return execSync(command, {
                encoding: 'utf8',
                stdio: options.silent ? 'pipe' : 'inherit',
                cwd: this.projectRoot,
                ...options
            });
        } catch (error) {
            if (options.ignoreError) {
                this.log(`命令失败但忽略: ${error.message}`, 'warning');
                return null;
            }
            throw error;
        }
    }

    /**
     * 查找需要修复的应用程序
     */
    findApps() {
        if (!fs.existsSync(this.distDir)) {
            this.log('dist目录不存在', 'warning');
            return [];
        }

        const apps = [];
        const items = fs.readdirSync(this.distDir);
        
        for (const item of items) {
            const itemPath = path.join(this.distDir, item);
            if (item.endsWith('.app')) {
                apps.push(itemPath);
            } else if (fs.statSync(itemPath).isDirectory()) {
                // 检查子目录中的.app文件
                const subItems = fs.readdirSync(itemPath);
                for (const subItem of subItems) {
                    if (subItem.endsWith('.app')) {
                        apps.push(path.join(itemPath, subItem));
                    }
                }
            }
        }

        return apps;
    }

    /**
     * 修复单个应用程序
     */
    fixApp(appPath) {
        this.log(`修复应用程序: ${path.basename(appPath)}`, 'info');

        try {
            // 1. 检查当前状态
            const beforeInfo = this.exec(`codesign -dv --verbose=4 "${appPath}" 2>&1`, { silent: true, ignoreError: true });
            const beforeHasResources = beforeInfo && beforeInfo.includes('Sealed Resources version=');
            const beforeHasPlist = beforeInfo && beforeInfo.includes('Info.plist entries=');
            
            this.log(`修复前 - 资源封装: ${beforeHasResources ? '有' : '无'}, Info.plist: ${beforeHasPlist ? '绑定' : '未绑定'}`, 'info');

            // 2. 移除现有签名
            this.exec(`codesign --remove-signature "${appPath}" 2>/dev/null || true`, { ignoreError: true });
            
            // 3. 清理扩展属性
            this.exec(`xattr -cr "${appPath}"`, { ignoreError: true });

            // 4. 验证Info.plist存在且格式正确
            const infoPlistPath = path.join(appPath, 'Contents/Info.plist');
            if (!fs.existsSync(infoPlistPath)) {
                throw new Error('Info.plist不存在');
            }
            
            this.exec(`plutil -lint "${infoPlistPath}"`);

            // 5. 执行adhoc签名 - 关键步骤
            // 先进行深度签名以封装所有资源
            this.exec(`codesign --force --deep --sign - "${appPath}"`);
            
            // 再次签名以确保Info.plist正确绑定
            this.exec(`codesign --force --sign - --preserve-metadata=entitlements "${appPath}"`);

            // 6. 验证修复结果
            const afterInfo = this.exec(`codesign -dv --verbose=4 "${appPath}" 2>&1`, { silent: true });
            const afterHasResources = afterInfo.includes('Sealed Resources version=');
            const afterHasPlist = afterInfo.includes('Info.plist entries=');
            
            this.log(`修复后 - 资源封装: ${afterHasResources ? '有' : '无'}, Info.plist: ${afterHasPlist ? '绑定' : '未绑定'}`, 'info');

            // 7. 验证签名有效性
            this.exec(`codesign --verify --deep --strict "${appPath}"`);
            
            if (afterHasResources && afterHasPlist) {
                this.log(`✅ ${path.basename(appPath)} 修复成功`, 'success');
                return true;
            } else if (afterHasResources) {
                this.log(`⚠️  ${path.basename(appPath)} 部分修复成功`, 'warning');
                return true;
            } else {
                this.log(`❌ ${path.basename(appPath)} 修复失败`, 'error');
                return false;
            }

        } catch (error) {
            this.log(`修复失败: ${error.message}`, 'error');
            return false;
        }
    }

    /**
     * 处理DMG文件中的应用程序
     */
    async fixDMGApps() {
        const dmgFiles = fs.readdirSync(this.distDir).filter(f => f.endsWith('.dmg'));
        
        for (const dmgFile of dmgFiles) {
            const dmgPath = path.join(this.distDir, dmgFile);
            this.log(`处理DMG文件: ${dmgFile}`, 'info');
            
            try {
                // 挂载DMG
                const mountResult = this.exec(`hdiutil attach "${dmgPath}" -nobrowse`, { silent: true });
                const mountPoint = mountResult.split('\n').find(line => line.includes('/Volumes/'))?.split('\t').pop()?.trim();
                
                if (!mountPoint) {
                    this.log('无法获取挂载点', 'warning');
                    continue;
                }

                try {
                    // 查找并修复应用程序
                    const appPath = path.join(mountPoint, `${this.productName}.app`);
                    if (fs.existsSync(appPath)) {
                        // 检查当前状态
                        const beforeInfo = this.exec(`codesign -dv --verbose=4 "${appPath}" 2>&1`, { silent: true, ignoreError: true });
                        const needsFix = !beforeInfo || 
                                       beforeInfo.includes('Sealed Resources=none') || 
                                       beforeInfo.includes('Info.plist=not bound');
                        
                        if (needsFix) {
                            // 移除现有签名
                            this.exec(`codesign --remove-signature "${appPath}" 2>/dev/null || true`, { ignoreError: true });
                            
                            // 执行签名修复
                            this.exec(`codesign --force --deep --sign - "${appPath}"`);
                            this.exec(`codesign --force --sign - --preserve-metadata=entitlements "${appPath}"`);
                            
                            // 验证结果
                            const afterInfo = this.exec(`codesign -dv --verbose=4 "${appPath}" 2>&1`, { silent: true });
                            const fixed = !afterInfo.includes('Sealed Resources=none');
                            
                            this.log(`DMG中的应用程序修复${fixed ? '成功' : '失败'}`, fixed ? 'success' : 'warning');
                        } else {
                            this.log('DMG中的应用程序无需修复', 'info');
                        }
                    }
                } finally {
                    // 卸载DMG
                    this.exec(`hdiutil detach "${mountPoint}" -force`, { silent: true, ignoreError: true });
                }
                
            } catch (error) {
                this.log(`处理DMG失败: ${error.message}`, 'error');
            }
        }
    }

    /**
     * 主修复流程
     */
    async fix() {
        try {
            this.log('🔧 开始后处理签名修复...', 'info');
            
            // 1. 修复本地应用程序
            const apps = this.findApps();
            if (apps.length > 0) {
                this.log(`找到 ${apps.length} 个应用程序`, 'info');
                let successCount = 0;
                
                for (const app of apps) {
                    if (this.fixApp(app)) {
                        successCount++;
                    }
                }
                
                this.log(`本地应用程序修复完成: ${successCount}/${apps.length}`, 'success');
            } else {
                this.log('未找到本地应用程序', 'warning');
            }

            // 2. 修复DMG中的应用程序
            await this.fixDMGApps();

            this.log('🎉 后处理签名修复完成!', 'success');
            this.log('📋 预期效果: 应用程序现在应该显示"无法验证"而不是"已损坏"', 'info');
            
        } catch (error) {
            this.log(`修复失败: ${error.message}`, 'error');
            process.exit(1);
        }
    }
}

// 只在macOS上运行
if (process.platform !== 'darwin') {
    console.log('此脚本只能在macOS上运行');
    process.exit(1);
}

// 运行修复
if (require.main === module) {
    const fixer = new PostPackageFix();
    fixer.fix();
}

module.exports = PostPackageFix;