const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

/**
 * 健壮的集成签名脚本 - after-pack 钩子
 * 在 electron-builder 打包完成后自动执行签名和资源封装
 */
class RobustSigner {
  constructor(context) {
    this.context = context;
    this.baseDir = context.appOutDir;
    this.arch = context.arch;
    this.platform = context.packager.platform;
    
    // 查找实际的.app文件路径
    this.appPath = this.findAppPath();
    console.log(`🚀 启动健壮的签名流程 - ${this.arch} 架构`);
    console.log(`📁 基础目录: ${this.baseDir}`);
    console.log(`📱 应用路径: ${this.appPath}`);
  }

  findAppPath() {
    try {
      // 在输出目录中查找.app文件
      const files = fs.readdirSync(this.baseDir);
      const appFile = files.find(file => file.endsWith('.app'));
      
      if (appFile) {
        return path.join(this.baseDir, appFile);
      }
      
      throw new Error('未找到.app文件');
    } catch (error) {
      console.error('查找.app文件失败:', error);
      throw error;
    }
  }

  async execute() {
    try {
      console.log(`📦 处理应用: ${path.basename(this.appPath)}`);
      
      // 1. 清理扩展属性
      await this.cleanExtendedAttributes();
      
      // 2. 执行深度签名
      await this.performDeepSigning();
      
      // 3. 验证签名结果
      const isValid = await this.verifySignature();
      
      if (isValid) {
        console.log(`✅ ${this.arch} 架构签名完成`);
      } else {
        console.log(`⚠️ ${this.arch} 架构签名可能不完整，但继续处理`);
      }
      
    } catch (error) {
      console.error(`❌ ${this.arch} 架构签名失败:`, error.message);
      // 不抛出错误，允许构建继续
      console.log(`⚠️ 签名失败但允许构建继续`);
    }
  }

  async cleanExtendedAttributes() {
    console.log('🧹 清理扩展属性...');
    
    const suspiciousAttributes = [
      'com.apple.provenance',
      'com.apple.quarantine',
      'com.apple.metadata:_kMDItemUserTags',
      'com.apple.FinderInfo',
      'com.apple.lastuseddate#PS',
      'com.apple.macl',
      'com.apple.diskimages.recentcksum'
    ];
    
    try {
      // 递归清理所有文件的扩展属性
      for (const attr of suspiciousAttributes) {
        try {
          execSync(`xattr -rd "${attr}" "${this.appPath}" 2>/dev/null || true`, 
            { stdio: 'pipe', timeout: 30000 });
        } catch (e) {
          // 忽略不存在的属性
        }
      }
      
      // 彻底清理所有扩展属性
      try {
        execSync(`xattr -rc "${this.appPath}" 2>/dev/null || true`, 
          { stdio: 'pipe', timeout: 30000 });
      } catch (e) {
        // 忽略错误
      }
      
      console.log('✅ 扩展属性清理完成');
    } catch (error) {
      console.error('⚠️ 扩展属性清理失败:', error.message);
    }
  }

  async performDeepSigning() {
    console.log('🔐 执行adhoc深度签名...');
    
    try {
      // 第一步：深度签名封装所有资源
      console.log('  📝 步骤1: 深度签名封装资源');
      execSync(
        `codesign --force --deep --sign - "${this.appPath}"`,
        { stdio: 'pipe', timeout: 60000 }
      );
      
      // 第二步：重新签名确保Info.plist绑定
      console.log('  📝 步骤2: 重新签名绑定Info.plist');
      execSync(
        `codesign --force --sign - --preserve-metadata=entitlements "${this.appPath}"`,
        { stdio: 'pipe', timeout: 60000 }
      );
      
      // 第三步：最终验证性签名
      console.log('  📝 步骤3: 最终验证性签名');
      execSync(
        `codesign --force --sign - "${this.appPath}"`,
        { stdio: 'pipe', timeout: 60000 }
      );
      
      console.log('✅ adhoc签名完成');
    } catch (error) {
      console.error('❌ adhoc签名失败:', error.message);
      throw error;
    }
  }

  async verifySignature() {
    console.log('🔍 验证签名结果...');
    
    try {
      // 基础签名验证
      const verifyResult = execSync(
        `codesign --verify --verbose=1 "${this.appPath}" 2>&1`,
        { encoding: 'utf8', stdio: 'pipe', timeout: 30000 }
      );
      
      if (verifyResult.includes('valid on disk') || verifyResult.trim() === '') {
        console.log('✅ 基础签名验证通过');
      }
      
      // 详细签名信息检查
      const detailResult = execSync(
        `codesign -dv --verbose=4 "${this.appPath}" 2>&1`,
        { encoding: 'utf8', stdio: 'pipe', timeout: 30000 }
      );
      
      // 检查关键签名特征
      const hasResources = detailResult.includes('Sealed Resources');
      const hasInfoPlist = detailResult.includes('Info.plist entries=');
      const isAdhoc = detailResult.includes('adhoc');
      
      console.log(`  📊 签名分析:`);
      console.log(`    - Sealed Resources: ${hasResources ? '✅' : '❌'}`);
      console.log(`    - Info.plist绑定: ${hasInfoPlist ? '✅' : '❌'}`);
      console.log(`    - Adhoc签名: ${isAdhoc ? '✅' : '❌'}`);
      
      if (hasResources && hasInfoPlist && isAdhoc) {
        console.log('🎯 签名完整性验证通过');
        return true;
      } else {
        console.log('⚠️ 签名可能不完整，但继续处理');
        return false;
      }
      
    } catch (error) {
      console.error('❌ 签名验证失败:', error.message);
      return false;
    }
  }
}

// electron-builder afterPack 钩子导出
module.exports = async function(context) {
  // 只在 macOS 平台执行
  if (context.packager.platform.name !== 'mac') {
    console.log('跳过非macOS平台的签名处理');
    return;
  }

  const signer = new RobustSigner(context);
  await signer.execute();
};