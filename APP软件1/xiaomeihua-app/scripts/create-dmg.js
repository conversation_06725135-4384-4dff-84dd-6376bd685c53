#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 开始创建 DMG 安装包...');

// 读取 package.json 获取应用信息
const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
const { productName, version } = packageJson;

// 定义路径
const distDir = path.join(__dirname, '..', 'dist');
const macDir = path.join(distDir, 'mac');
const appPath = path.join(macDir, `${productName}.app`);

// 检查应用是否存在
if (!fs.existsSync(appPath)) {
    console.error('❌ 应用文件不存在，请先运行 npm run build:mac');
    process.exit(1);
}

console.log('✅ 找到应用文件:', appPath);

// DMG 配置
const dmgConfig = {
    title: `${productName}-${version}`,
    background: path.join(__dirname, '..', 'build', 'dmg-background.png'),
    icon: path.join(__dirname, '..', 'build', 'icon.icns'),
    iconSize: 100,
    window: {
        width: 600,
        height: 480
    },
    contents: [
        { x: 160, y: 280, type: 'file', path: appPath },
        { x: 480, y: 280, type: 'link', path: '/Applications' },
        { x: 300, y: 100, type: 'file', path: path.join(__dirname, '..', 'build', 'Mac安装教程.png') }
    ]
};

// 创建 DMG 的函数
function createDMG(arch) {
    console.log(`📦 正在为 ${arch} 架构创建 DMG...`);
    
    const dmgName = `${productName}-${version}-${arch}.dmg`;
    const dmgPath = path.join(distDir, dmgName);
    
    // 如果 DMG 已存在，删除它
    if (fs.existsSync(dmgPath)) {
        fs.unlinkSync(dmgPath);
        console.log(`🗑️  删除旧的 DMG: ${dmgName}`);
    }
    
    try {
        // 使用 hdiutil 创建 DMG
        const tempDmgPath = path.join(distDir, `temp-${arch}.dmg`);
        const mountPoint = path.join(distDir, `mount-${arch}`);
        
        // 创建临时 DMG
        execSync(`hdiutil create -size 200m -fs HFS+ -volname "${dmgConfig.title}" "${tempDmgPath}"`, { stdio: 'inherit' });
        
        // 挂载 DMG
        execSync(`hdiutil attach "${tempDmgPath}" -mountpoint "${mountPoint}"`, { stdio: 'inherit' });
        
        // 复制应用到 DMG
        execSync(`cp -R "${appPath}" "${mountPoint}/"`, { stdio: 'inherit' });
        
        // 创建 Applications 链接
        execSync(`ln -s /Applications "${mountPoint}/Applications"`, { stdio: 'inherit' });
        
        // 复制安装教程图片
        const tutorialPath = path.join(__dirname, '..', 'build', 'Mac安装教程.png');
        if (fs.existsSync(tutorialPath)) {
            execSync(`cp "${tutorialPath}" "${mountPoint}/"`, { stdio: 'inherit' });
        }
        
        // 卸载 DMG
        execSync(`hdiutil detach "${mountPoint}"`, { stdio: 'inherit' });
        
        // 转换为最终的压缩 DMG
        execSync(`hdiutil convert "${tempDmgPath}" -format UDZO -o "${dmgPath}"`, { stdio: 'inherit' });
        
        // 删除临时文件
        if (fs.existsSync(tempDmgPath)) {
            fs.unlinkSync(tempDmgPath);
        }
        
        console.log(`✅ DMG 创建成功: ${dmgName}`);
        console.log(`📍 位置: ${dmgPath}`);
        
        // 显示文件大小
        const stats = fs.statSync(dmgPath);
        const fileSizeInMB = (stats.size / (1024 * 1024)).toFixed(2);
        console.log(`📊 文件大小: ${fileSizeInMB} MB`);
        
    } catch (error) {
        console.error(`❌ 创建 ${arch} DMG 失败:`, error.message);
        return false;
    }
    
    return true;
}

// 主执行函数
async function main() {
    try {
        // 检查是否在 macOS 上运行
        if (process.platform !== 'darwin') {
            console.error('❌ DMG 只能在 macOS 上创建');
            process.exit(1);
        }
        
        // 检查必要的工具
        try {
            execSync('which hdiutil', { stdio: 'pipe' });
        } catch (error) {
            console.error('❌ 未找到 hdiutil 工具，请确保在 macOS 上运行');
            process.exit(1);
        }
        
        console.log('🔍 检查构建产物...');
        
        // 查找所有架构的应用
        const architectures = [];
        if (fs.existsSync(path.join(macDir, `${productName}.app`))) {
            // 检查应用支持的架构
            try {
                const archInfo = execSync(`lipo -archs "${path.join(macDir, `${productName}.app`, 'Contents', 'MacOS', productName)}"`, { encoding: 'utf8' }).trim();
                console.log(`📋 应用支持的架构: ${archInfo}`);
                
                if (archInfo.includes('arm64')) architectures.push('arm64');
                if (archInfo.includes('x86_64')) architectures.push('x64');
            } catch (error) {
                console.log('⚠️  无法检测架构，使用通用构建');
                architectures.push('universal');
            }
        }
        
        if (architectures.length === 0) {
            console.error('❌ 未找到可用的应用构建');
            process.exit(1);
        }
        
        // 为每个架构创建 DMG
        let successCount = 0;
        for (const arch of architectures) {
            if (createDMG(arch)) {
                successCount++;
            }
        }
        
        console.log(`\n🎉 DMG 创建完成！成功创建 ${successCount}/${architectures.length} 个安装包`);
        console.log(`📁 输出目录: ${distDir}`);
        
        // 列出创建的文件
        const dmgFiles = fs.readdirSync(distDir).filter(file => file.endsWith('.dmg'));
        if (dmgFiles.length > 0) {
            console.log('\n📦 创建的 DMG 文件:');
            dmgFiles.forEach(file => {
                const filePath = path.join(distDir, file);
                const stats = fs.statSync(filePath);
                const fileSizeInMB = (stats.size / (1024 * 1024)).toFixed(2);
                console.log(`  • ${file} (${fileSizeInMB} MB)`);
            });
        }
        
    } catch (error) {
        console.error('❌ 创建 DMG 过程中发生错误:', error.message);
        process.exit(1);
    }
}

// 运行主函数
if (require.main === module) {
    main();
}

module.exports = { createDMG };
