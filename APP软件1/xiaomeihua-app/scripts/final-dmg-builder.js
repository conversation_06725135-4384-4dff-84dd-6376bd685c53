#!/usr/bin/env node

/**
 * 最终DMG重建脚本 - 彻底解决"已损坏"问题
 * 
 * 策略：
 * 1. 完全重新构建应用程序（不使用electron-builder的DMG）
 * 2. 手动清理所有扩展属性
 * 3. 手动创建干净的DMG
 * 4. 验证最终效果
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

class FinalDMGBuilder {
    constructor() {
        this.projectRoot = path.join(__dirname, '..');
        this.distDir = path.join(this.projectRoot, 'dist');
        this.tempDir = path.join(this.projectRoot, 'temp-clean-build');
        this.packageJson = require(path.join(this.projectRoot, 'package.json'));
        this.productName = this.packageJson.productName;
        this.version = this.packageJson.version;
    }

    log(message, type = 'info') {
        const colors = {
            info: '\x1b[36m',     // 青色
            success: '\x1b[32m',  // 绿色  
            warning: '\x1b[33m',  // 黄色
            error: '\x1b[31m',    // 红色
            step: '\x1b[35m'      // 紫色
        };
        const reset = '\x1b[0m';
        const timestamp = new Date().toLocaleTimeString();
        console.log(`${colors[type]}[${timestamp}] ${message}${reset}`);
    }

    exec(command, options = {}) {
        try {
            return execSync(command, {
                encoding: 'utf8',
                stdio: options.silent ? 'pipe' : 'inherit',
                cwd: this.projectRoot,
                ...options
            });
        } catch (error) {
            if (options.ignoreError) {
                this.log(`命令失败但继续: ${error.message}`, 'warning');
                return null;
            }
            throw error;
        }
    }

    /**
     * 彻底清理扩展属性的超强方法
     */
    absoluteCleanAttributes(targetPath) {
        this.log(`超强清理扩展属性: ${path.basename(targetPath)}`, 'step');
        
        try {
            // 方法1: 使用xattr -c清理所有属性
            this.exec(`xattr -c "${targetPath}"`, { ignoreError: true });
            
            // 方法2: 递归清理所有子项
            this.exec(`find "${targetPath}" -exec xattr -c {} \\; 2>/dev/null || true`, { silent: true, ignoreError: true });
            
            // 方法3: 针对性清理已知问题属性
            const problemAttributes = [
                'com.apple.provenance',
                'com.apple.quarantine', 
                'com.apple.metadata:_kMDItemUserTags',
                'com.apple.FinderInfo',
                'com.apple.lastuseddate#PS',
                'com.apple.macl',
                'com.apple.diskimages.recentcksum'
            ];
            
            for (const attr of problemAttributes) {
                this.exec(`xattr -d "${attr}" "${targetPath}" 2>/dev/null || true`, { silent: true, ignoreError: true });
                this.exec(`find "${targetPath}" -exec xattr -d "${attr}" {} \\; 2>/dev/null || true`, { silent: true, ignoreError: true });
            }
            
            // 方法4: 特殊处理 - 复制到新位置来去除顽固属性
            if (fs.statSync(targetPath).isDirectory() && targetPath.endsWith('.app')) {
                const cleanPath = targetPath + '.clean';
                this.exec(`cp -R "${targetPath}" "${cleanPath}"`, { ignoreError: true });
                this.exec(`rm -rf "${targetPath}"`, { ignoreError: true });
                this.exec(`mv "${cleanPath}" "${targetPath}"`, { ignoreError: true });
                
                // 再次清理新复制的版本
                this.exec(`xattr -cr "${targetPath}"`, { ignoreError: true });
            }
            
            // 验证清理结果
            const result = this.exec(`xattr -l "${targetPath}" 2>/dev/null || true`, { silent: true, ignoreError: true });
            if (result && result.trim()) {
                this.log(`⚠️ 仍有顽固属性: ${result.trim()}`, 'warning');
                // 最后的杀手锏 - 重新创建目录结构
                if (targetPath.endsWith('.app')) {
                    this.recreateAppStructure(targetPath);
                }
            } else {
                this.log(`✅ 扩展属性完全清理`, 'success');
            }
            
        } catch (error) {
            this.log(`清理过程出错: ${error.message}`, 'warning');
        }
    }

    /**
     * 重新创建应用结构（去除顽固属性的最后手段）
     */
    recreateAppStructure(appPath) {
        this.log(`重新创建应用结构: ${path.basename(appPath)}`, 'step');
        
        try {
            const backupPath = appPath + '.backup';
            const newPath = appPath + '.new';
            
            // 备份原应用
            this.exec(`cp -R "${appPath}" "${backupPath}"`);
            
            // 创建新的干净目录结构
            fs.mkdirSync(newPath, { recursive: true });
            fs.mkdirSync(path.join(newPath, 'Contents'), { recursive: true });
            fs.mkdirSync(path.join(newPath, 'Contents/MacOS'), { recursive: true });
            fs.mkdirSync(path.join(newPath, 'Contents/Resources'), { recursive: true });
            fs.mkdirSync(path.join(newPath, 'Contents/Frameworks'), { recursive: true });
            
            // 复制内容到新结构（不带属性）
            this.exec(`cp -R "${backupPath}/Contents/"* "${newPath}/Contents/"`);
            
            // 替换原应用
            this.exec(`rm -rf "${appPath}"`);
            this.exec(`mv "${newPath}" "${appPath}"`);
            
            // 清理备份
            this.exec(`rm -rf "${backupPath}"`);
            
            // 最后检查
            const finalCheck = this.exec(`xattr -l "${appPath}" 2>/dev/null || true`, { silent: true, ignoreError: true });
            if (!finalCheck || !finalCheck.trim()) {
                this.log(`✅ 应用结构重建成功，属性完全清理`, 'success');
            }
            
        } catch (error) {
            this.log(`重建失败: ${error.message}`, 'error');
        }
    }

    /**
     * 手动创建DMG
     */
    createCleanDMG(appPath, dmgPath) {
        this.log(`创建干净的DMG: ${path.basename(dmgPath)}`, 'step');
        
        try {
            // 创建临时DMG目录
            const dmgContentDir = path.join(this.tempDir, 'dmg-content');
            if (fs.existsSync(dmgContentDir)) {
                this.exec(`rm -rf "${dmgContentDir}"`);
            }
            fs.mkdirSync(dmgContentDir, { recursive: true });
            
            // 复制应用到DMG内容目录
            const appInDMG = path.join(dmgContentDir, path.basename(appPath));
            this.exec(`cp -R "${appPath}" "${appInDMG}"`);
            
            // 清理DMG内应用的属性
            this.absoluteCleanAttributes(appInDMG);
            
            // 创建Applications链接
            this.exec(`ln -s /Applications "${dmgContentDir}/Applications"`);
            
            // 删除已存在的DMG
            if (fs.existsSync(dmgPath)) {
                this.exec(`rm -f "${dmgPath}"`);
            }
            
            // 创建DMG
            const volumeName = `${this.productName}-${this.version}`;
            this.exec(`hdiutil create -srcfolder "${dmgContentDir}" -volname "${volumeName}" -format UDZO "${dmgPath}"`);
            
            // 清理DMG文件的属性
            this.absoluteCleanAttributes(dmgPath);
            
            this.log(`✅ 干净DMG创建完成`, 'success');
            return true;
            
        } catch (error) {
            this.log(`DMG创建失败: ${error.message}`, 'error');
            return false;
        }
    }

    /**
     * 终极签名和清理
     */
    ultimateSignAndClean(appPath) {
        this.log(`终极签名处理: ${path.basename(appPath)}`, 'step');
        
        try {
            // 1. 彻底清理属性
            this.absoluteCleanAttributes(appPath);
            
            // 2. 验证Info.plist
            const infoPlistPath = path.join(appPath, 'Contents/Info.plist');
            if (!fs.existsSync(infoPlistPath)) {
                throw new Error('Info.plist不存在');
            }
            this.exec(`plutil -lint "${infoPlistPath}"`);
            
            // 3. 修复权限
            this.exec(`chmod -R 755 "${appPath}"`);
            
            // 4. 移除所有现有签名
            this.exec(`codesign --remove-signature "${appPath}" 2>/dev/null || true`, { ignoreError: true });
            this.exec(`find "${appPath}" -name "*.dylib" -exec codesign --remove-signature {} \\; 2>/dev/null || true`, { ignoreError: true });
            this.exec(`find "${appPath}" -name "*.framework" -exec codesign --remove-signature {} \\; 2>/dev/null || true`, { ignoreError: true });
            
            // 5. 执行签名
            this.exec(`codesign --force --deep --sign - "${appPath}"`);
            this.exec(`codesign --force --sign - --preserve-metadata=entitlements "${appPath}"`);
            
            // 6. 再次清理可能产生的新属性
            this.absoluteCleanAttributes(appPath);
            
            // 7. 验证签名
            this.exec(`codesign --verify --deep --strict "${appPath}"`);
            
            const signInfo = this.exec(`codesign -dv --verbose=4 "${appPath}" 2>&1`, { silent: true });
            const hasResources = signInfo.includes('Sealed Resources version=');
            const hasPlist = signInfo.includes('Info.plist entries=');
            
            this.log(`签名结果 - 资源: ${hasResources ? '✅' : '❌'}, Info.plist: ${hasPlist ? '✅' : '❌'}`, hasResources && hasPlist ? 'success' : 'warning');
            
            return hasResources && hasPlist;
            
        } catch (error) {
            this.log(`签名失败: ${error.message}`, 'error');
            return false;
        }
    }

    /**
     * 主构建流程
     */
    async buildFinalDMG() {
        try {
            this.log('🚀 开始最终DMG重建...', 'step');
            this.log('🎯 目标: 创建完全干净、无"已损坏"问题的DMG', 'info');
            
            // 1. 创建临时目录
            if (fs.existsSync(this.tempDir)) {
                this.exec(`rm -rf "${this.tempDir}"`);
            }
            fs.mkdirSync(this.tempDir, { recursive: true });
            
            // 2. 查找现有应用程序
            const apps = [];
            if (fs.existsSync(path.join(this.distDir, 'mac'))) {
                apps.push({
                    path: path.join(this.distDir, 'mac', `${this.productName}.app`),
                    arch: 'x64'
                });
            }
            if (fs.existsSync(path.join(this.distDir, 'mac-arm64'))) {
                apps.push({
                    path: path.join(this.distDir, 'mac-arm64', `${this.productName}.app`),
                    arch: 'arm64'
                });
            }
            
            if (apps.length === 0) {
                throw new Error('未找到构建的应用程序');
            }
            
            // 3. 处理每个应用程序并创建DMG
            let successCount = 0;
            for (const app of apps) {
                if (!fs.existsSync(app.path)) {
                    this.log(`应用不存在: ${app.path}`, 'warning');
                    continue;
                }
                
                this.log(`处理 ${app.arch} 架构应用`, 'info');
                
                // 终极签名和清理
                if (this.ultimateSignAndClean(app.path)) {
                    // 创建干净的DMG
                    const dmgName = `${this.productName}-${this.version}-${app.arch}-FIXED.dmg`;
                    const dmgPath = path.join(this.distDir, dmgName);
                    
                    if (this.createCleanDMG(app.path, dmgPath)) {
                        successCount++;
                        this.log(`✅ ${app.arch} 版本DMG创建成功`, 'success');
                    }
                } else {
                    this.log(`❌ ${app.arch} 版本处理失败`, 'error');
                }
            }
            
            // 4. 清理临时目录
            this.exec(`rm -rf "${this.tempDir}"`);
            
            // 5. 生成最终报告
            this.generateFinalReport(apps.length, successCount);
            
            this.log('🎉 最终DMG重建完成!', 'success');
            return successCount > 0;
            
        } catch (error) {
            this.log(`构建失败: ${error.message}`, 'error');
            return false;
        }
    }

    /**
     * 生成最终报告
     */
    generateFinalReport(totalApps, successCount) {
        console.log('\n' + '='.repeat(60));
        console.log('🏆 最终DMG重建报告');
        console.log('='.repeat(60));
        console.log(`📱 处理应用: ${totalApps}`);
        console.log(`✅ 成功创建: ${successCount}`);
        console.log(`📊 成功率: ${Math.round((successCount/totalApps)*100)}%`);
        console.log('');
        console.log('🔧 处理步骤:');
        console.log('   • 彻底清理所有扩展属性（包括顽固属性）');
        console.log('   • 重建应用结构去除属性污染');
        console.log('   • 多重adhoc签名确保兼容性');
        console.log('   • 手动创建干净DMG文件');
        console.log('   • 清理DMG文件本身的属性');
        console.log('');
        console.log('✨ 最终效果:');
        console.log('   • 应用现在应该显示"无法验证"而不是"已损坏"');
        console.log('   • 用户可以右键选择"打开"来运行');
        console.log('   • 或在"系统偏好设置 > 安全性与隐私"中允许');
        console.log('');
        console.log('📁 输出文件: *-FIXED.dmg');
        console.log('='.repeat(60));
    }
}

// 只在macOS上运行
if (process.platform !== 'darwin') {
    console.log('❌ 此脚本只能在macOS上运行');
    process.exit(1);
}

// 运行构建
if (require.main === module) {
    const builder = new FinalDMGBuilder();
    builder.buildFinalDMG().then(success => {
        process.exit(success ? 0 : 1);
    });
}

module.exports = FinalDMGBuilder;