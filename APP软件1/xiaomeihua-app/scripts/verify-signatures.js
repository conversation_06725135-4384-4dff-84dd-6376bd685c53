const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

/**
 * 签名验证脚本
 * 验证构建完成的DMG文件的签名状态
 */
class SignatureVerifier {
  constructor() {
    this.distPath = path.join(__dirname, '../dist');
    this.results = [];
  }

  async verifyAll() {
    console.log('🔍 开始验证所有DMG文件的签名状态...');
    
    try {
      // 查找所有DMG文件
      const dmgFiles = this.findDMGFiles();
      
      if (dmgFiles.length === 0) {
        console.log('❌ 未找到任何DMG文件');
        return false;
      }

      console.log(`📦 找到 ${dmgFiles.length} 个DMG文件:`);
      dmgFiles.forEach(file => console.log(`  - ${path.basename(file)}`));

      // 验证每个DMG文件
      for (const dmgFile of dmgFiles) {
        await this.verifyDMG(dmgFile);
      }

      // 输出汇总结果
      this.printSummary();
      
      // 返回是否全部验证通过
      return this.results.every(result => result.isValid);
      
    } catch (error) {
      console.error('❌ 验证过程失败:', error);
      return false;
    }
  }

  findDMGFiles() {
    const dmgFiles = [];
    
    if (!fs.existsSync(this.distPath)) {
      return dmgFiles;
    }
    
    const files = fs.readdirSync(this.distPath);
    for (const file of files) {
      if (file.endsWith('.dmg') && !file.includes('FIXED')) {
        dmgFiles.push(path.join(this.distPath, file));
      }
    }
    
    return dmgFiles;
  }

  async verifyDMG(dmgFile) {
    const fileName = path.basename(dmgFile);
    console.log(`\\n🔍 验证: ${fileName}`);
    
    try {
      // 挂载DMG
      const mountResult = execSync(
        `hdiutil attach "${dmgFile}" -quiet -nobrowse`,
        { encoding: 'utf8', stdio: 'pipe' }
      );
      
      // 查找挂载点
      const volumeName = this.findVolumeName(fileName);
      if (!volumeName) {
        throw new Error('无法找到挂载点');
      }
      
      const appPath = path.join('/Volumes', volumeName, '小梅花AI智能客服.app');
      
      if (!fs.existsSync(appPath)) {
        throw new Error('应用文件不存在');
      }

      // 验证签名
      const result = await this.verifyAppSignature(appPath, fileName);
      this.results.push(result);

      // 卸载DMG
      try {
        execSync(`hdiutil detach "/Volumes/${volumeName}" -quiet`, { stdio: 'pipe' });
      } catch (e) {
        console.log('⚠️ DMG卸载失败，但不影响验证结果');
      }

    } catch (error) {
      console.error(`❌ ${fileName} 验证失败:`, error.message);
      this.results.push({
        fileName,
        isValid: false,
        error: error.message,
        hasResources: false,
        hasInfoPlist: false,
        isAdhoc: false
      });
    }
  }

  findVolumeName(fileName) {
    // 从文件名推断卷名
    if (fileName.includes('小梅花AI智能客服-1.0.0')) {
      return '小梅花AI智能客服-1.0.0';
    }
    
    // 尝试查找所有挂载的卷
    try {
      const volumes = fs.readdirSync('/Volumes');
      for (const vol of volumes) {
        if (vol.includes('小梅花') || vol.includes('xiaomeihua')) {
          return vol;
        }
      }
    } catch (e) {
      // 忽略错误
    }
    
    return null;
  }

  async verifyAppSignature(appPath, fileName) {
    console.log(`  🔐 检查签名: ${path.basename(appPath)}`);
    
    try {
      // 基础验证
      const verifyCmd = `codesign --verify --verbose=4 "${appPath}" 2>&1`;
      const verifyResult = execSync(verifyCmd, { encoding: 'utf8', stdio: 'pipe' });
      
      const isValid = verifyResult.includes('valid on disk') && 
                     verifyResult.includes('satisfies its Designated Requirement');

      // 详细信息检查
      const detailCmd = `codesign -dv --verbose=4 "${appPath}" 2>&1`;
      const detailResult = execSync(detailCmd, { encoding: 'utf8', stdio: 'pipe' });
      
      const hasResources = detailResult.includes('Sealed Resources');
      const hasInfoPlist = detailResult.includes('Info.plist entries=');
      const isAdhoc = detailResult.includes('adhoc');

      // 架构检测
      let arch = 'unknown';
      if (detailResult.includes('thin (x86_64)')) {
        arch = 'x64';
      } else if (detailResult.includes('thin (arm64)')) {
        arch = 'arm64';
      }

      const result = {
        fileName,
        arch,
        isValid,
        hasResources,
        hasInfoPlist,
        isAdhoc,
        verifyOutput: verifyResult,
        detailOutput: detailResult
      };

      // 输出验证结果
      console.log(`    📊 架构: ${arch}`);
      console.log(`    📊 签名有效: ${isValid ? '✅' : '❌'}`);
      console.log(`    📊 Sealed Resources: ${hasResources ? '✅' : '❌'}`);
      console.log(`    📊 Info.plist绑定: ${hasInfoPlist ? '✅' : '❌'}`);
      console.log(`    📊 Adhoc签名: ${isAdhoc ? '✅' : '❌'}`);

      if (isValid && hasResources && hasInfoPlist) {
        console.log(`    🎯 ${fileName} 签名完整 ✅`);
      } else {
        console.log(`    ⚠️ ${fileName} 签名不完整`);
      }

      return result;

    } catch (error) {
      console.error(`    ❌ 签名检查失败:`, error.message);
      return {
        fileName,
        isValid: false,
        error: error.message,
        hasResources: false,
        hasInfoPlist: false,
        isAdhoc: false
      };
    }
  }

  printSummary() {
    console.log(`\\n📋 签名验证汇总报告`);
    console.log(`===================`);
    
    const totalFiles = this.results.length;
    const validFiles = this.results.filter(r => r.isValid).length;
    const completeFiles = this.results.filter(r => r.hasResources && r.hasInfoPlist).length;
    
    console.log(`📱 总文件数: ${totalFiles}`);
    console.log(`✅ 签名有效: ${validFiles} / ${totalFiles}`);
    console.log(`🎯 签名完整: ${completeFiles} / ${totalFiles}`);
    console.log(`📊 成功率: ${Math.round(completeFiles / totalFiles * 100)}%`);
    
    if (completeFiles === totalFiles) {
      console.log(`\\n🎉 所有DMG文件签名验证通过！`);
      console.log(`   应用将显示"无法验证"而非"已损坏"`);
    } else {
      console.log(`\\n⚠️ 部分文件签名不完整，可能仍显示"已损坏"`);
    }

    // 详细结果
    console.log(`\\n📋 详细结果:`);
    this.results.forEach(result => {
      const status = result.isValid ? '✅' : '❌';
      const complete = (result.hasResources && result.hasInfoPlist) ? '🎯' : '⚠️';
      console.log(`  ${status} ${complete} ${result.fileName} (${result.arch || 'unknown'})`);
      
      if (result.error) {
        console.log(`    错误: ${result.error}`);
      }
    });
  }
}

// 主执行函数
async function main() {
  const verifier = new SignatureVerifier();
  const success = await verifier.verifyAll();
  
  process.exit(success ? 0 : 1);
}

// 如果直接运行此脚本
if (require.main === module) {
  main().catch(error => {
    console.error('验证脚本执行失败:', error);
    process.exit(1);
  });
}

module.exports = SignatureVerifier;