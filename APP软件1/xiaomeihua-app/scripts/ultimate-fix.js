#!/usr/bin/env node

/**
 * 强化版应用修复脚本 - 彻底解决"已损坏"问题
 * 
 * 解决方案：
 * 1. 彻底清理所有可疑的扩展属性
 * 2. 递归处理所有文件和子目录
 * 3. 多重签名策略确保Gatekeeper兼容
 * 4. 验证修复效果
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

class UltimateAppFixer {
    constructor() {
        this.projectRoot = path.join(__dirname, '..');
        this.distDir = path.join(this.projectRoot, 'dist');
        this.packageJson = require(path.join(this.projectRoot, 'package.json'));
        this.productName = this.packageJson.productName;
        
        // 需要清理的扩展属性列表
        this.suspiciousAttributes = [
            'com.apple.provenance',
            'com.apple.quarantine',
            'com.apple.metadata:_kMDItemUserTags',
            'com.apple.FinderInfo',
            'com.apple.lastuseddate#PS',
            'com.apple.macl',
            'com.apple.diskimages.recentcksum'
        ];
    }

    log(message, type = 'info') {
        const colors = {
            info: '\x1b[36m',     // 青色
            success: '\x1b[32m',  // 绿色  
            warning: '\x1b[33m',  // 黄色
            error: '\x1b[31m',    // 红色
            step: '\x1b[35m'      // 紫色
        };
        const reset = '\x1b[0m';
        const timestamp = new Date().toLocaleTimeString();
        console.log(`${colors[type]}[${timestamp}] ${message}${reset}`);
    }

    exec(command, options = {}) {
        try {
            return execSync(command, {
                encoding: 'utf8',
                stdio: options.silent ? 'pipe' : 'inherit',
                cwd: this.projectRoot,
                ...options
            });
        } catch (error) {
            if (options.ignoreError) {
                this.log(`命令失败但继续: ${error.message}`, 'warning');
                return null;
            }
            throw error;
        }
    }

    /**
     * 递归清理目录中所有文件的扩展属性
     */
    cleanExtendedAttributes(targetPath) {
        this.log(`清理扩展属性: ${path.basename(targetPath)}`, 'step');
        
        try {
            // 清理目标本身的属性
            for (const attr of this.suspiciousAttributes) {
                this.exec(`xattr -d "${attr}" "${targetPath}" 2>/dev/null || true`, { silent: true, ignoreError: true });
            }
            
            // 递归清理所有子文件和目录
            this.exec(`find "${targetPath}" -exec xattr -c {} \\; 2>/dev/null || true`, { silent: true, ignoreError: true });
            
            // 额外的深度清理
            for (const attr of this.suspiciousAttributes) {
                this.exec(`find "${targetPath}" -exec xattr -d "${attr}" {} \\; 2>/dev/null || true`, { silent: true, ignoreError: true });
            }
            
            this.log(`✅ 扩展属性清理完成`, 'success');
            return true;
        } catch (error) {
            this.log(`清理扩展属性失败: ${error.message}`, 'error');
            return false;
        }
    }

    /**
     * 验证并显示当前扩展属性状态
     */
    checkExtendedAttributes(targetPath) {
        try {
            const result = this.exec(`xattr -l "${targetPath}"`, { silent: true, ignoreError: true });
            if (result && result.trim()) {
                this.log(`⚠️ 仍有扩展属性: ${result.trim()}`, 'warning');
                return false;
            } else {
                this.log(`✅ 扩展属性已完全清理`, 'success');
                return true;
            }
        } catch (error) {
            this.log(`✅ 扩展属性已完全清理`, 'success');
            return true;
        }
    }

    /**
     * 强化版应用签名
     */
    performUltimateCodesigning(appPath) {
        this.log(`开始强化版签名: ${path.basename(appPath)}`, 'step');
        
        try {
            // 1. 移除所有现有签名
            this.log('步骤1: 移除现有签名', 'info');
            this.exec(`codesign --remove-signature "${appPath}" 2>/dev/null || true`, { ignoreError: true });
            
            // 递归移除所有子组件的签名
            this.exec(`find "${appPath}" -name "*.app" -exec codesign --remove-signature {} \\; 2>/dev/null || true`, { ignoreError: true });
            this.exec(`find "${appPath}" -name "*.framework" -exec codesign --remove-signature {} \\; 2>/dev/null || true`, { ignoreError: true });
            this.exec(`find "${appPath}" -name "*.dylib" -exec codesign --remove-signature {} \\; 2>/dev/null || true`, { ignoreError: true });

            // 2. 验证Info.plist
            this.log('步骤2: 验证Info.plist', 'info');
            const infoPlistPath = path.join(appPath, 'Contents/Info.plist');
            if (!fs.existsSync(infoPlistPath)) {
                throw new Error('Info.plist不存在');
            }
            this.exec(`plutil -lint "${infoPlistPath}"`);
            
            // 3. 修复权限
            this.log('步骤3: 修复权限', 'info');
            this.exec(`chmod -R 755 "${appPath}"`);
            this.exec(`find "${appPath}" -type f -name "*.dylib" -exec chmod 755 {} \\;`);
            this.exec(`find "${appPath}" -type f -perm +111 -exec chmod 755 {} \\;`);
            
            // 4. 多重签名策略
            this.log('步骤4: 执行多重签名', 'info');
            
            // 策略1: 深度签名（封装资源）
            this.exec(`codesign --force --deep --sign - "${appPath}"`);
            
            // 策略2: 重新签名（绑定Info.plist）
            this.exec(`codesign --force --sign - --preserve-metadata=entitlements "${appPath}"`);
            
            // 策略3: 最终签名（确保兼容性）
            this.exec(`codesign --force --sign - --options=runtime "${appPath}" 2>/dev/null || codesign --force --sign - "${appPath}"`);
            
            this.log('✅ 多重签名完成', 'success');
            return true;
            
        } catch (error) {
            this.log(`签名失败: ${error.message}`, 'error');
            return false;
        }
    }

    /**
     * 验证签名状态
     */
    verifySigningStatus(appPath) {
        this.log(`验证签名状态: ${path.basename(appPath)}`, 'step');
        
        try {
            // 1. 基本签名验证
            this.exec(`codesign --verify --deep --strict "${appPath}"`);
            this.log('✅ 签名验证通过', 'success');
            
            // 2. 详细信息检查
            const signInfo = this.exec(`codesign -dv --verbose=4 "${appPath}" 2>&1`, { silent: true });
            
            const hasResources = signInfo.includes('Sealed Resources version=');
            const hasPlist = signInfo.includes('Info.plist entries=');
            const isAdhoc = signInfo.includes('Signature=adhoc');
            
            this.log(`📋 签名详情:`, 'info');
            this.log(`   资源封装: ${hasResources ? '✅' : '❌'}`, hasResources ? 'success' : 'warning');
            this.log(`   Info.plist: ${hasPlist ? '✅' : '❌'}`, hasPlist ? 'success' : 'warning');
            this.log(`   Adhoc签名: ${isAdhoc ? '✅' : '❌'}`, isAdhoc ? 'success' : 'warning');
            
            return { hasResources, hasPlist, isAdhoc };
            
        } catch (error) {
            this.log(`签名验证失败: ${error.message}`, 'error');
            return { hasResources: false, hasPlist: false, isAdhoc: false };
        }
    }

    /**
     * Gatekeeper兼容性测试
     */
    testGatekeeperCompatibility(appPath) {
        this.log(`测试Gatekeeper兼容性: ${path.basename(appPath)}`, 'step');
        
        try {
            // 测试Gatekeeper评估
            this.exec(`spctl --assess --type execute "${appPath}"`);
            this.log('✅ Gatekeeper接受此应用', 'success');
            return true;
        } catch (error) {
            // 这是预期的，adhoc签名的应用会被拒绝
            this.log('⚠️ Gatekeeper拒绝 (正常，adhoc签名)', 'warning');
            
            // 检查拒绝原因
            const result = this.exec(`spctl --assess --type execute "${appPath}" 2>&1`, { silent: true, ignoreError: true });
            if (result && result.includes('rejected')) {
                this.log('📋 预期行为: adhoc签名应用被Gatekeeper拒绝', 'info');
                return true; // 这实际上是正确的行为
            }
            return false;
        }
    }

    /**
     * 修复单个应用程序 - 完整流程
     */
    async fixApplicationCompletely(appPath) {
        this.log(`🔧 开始完整修复: ${path.basename(appPath)}`, 'step');
        
        let success = true;
        
        try {
            // 步骤1: 清理扩展属性
            if (!this.cleanExtendedAttributes(appPath)) {
                success = false;
            }
            
            // 步骤2: 验证属性清理
            this.checkExtendedAttributes(appPath);
            
            // 步骤3: 强化签名
            if (!this.performUltimateCodesigning(appPath)) {
                success = false;
            }
            
            // 步骤4: 验证签名
            const signStatus = this.verifySigningStatus(appPath);
            if (!signStatus.hasResources || !signStatus.hasPlist) {
                this.log('⚠️ 签名不完整但可能仍可使用', 'warning');
            }
            
            // 步骤5: Gatekeeper测试
            this.testGatekeeperCompatibility(appPath);
            
            if (success) {
                this.log(`✅ ${path.basename(appPath)} 修复完成`, 'success');
            } else {
                this.log(`⚠️ ${path.basename(appPath)} 部分修复`, 'warning');
            }
            
            return success;
            
        } catch (error) {
            this.log(`修复失败: ${error.message}`, 'error');
            return false;
        }
    }

    /**
     * 处理DMG文件
     */
    async fixDMGFiles() {
        this.log('🔧 开始修复DMG文件', 'step');
        
        const dmgFiles = fs.readdirSync(this.distDir).filter(f => f.endsWith('.dmg')); 
        
        for (const dmgFile of dmgFiles) {
            const dmgPath = path.join(this.distDir, dmgFile);
            this.log(`处理DMG: ${dmgFile}`, 'info');
            
            // 清理DMG本身的扩展属性
            this.cleanExtendedAttributes(dmgPath);
            this.checkExtendedAttributes(dmgPath);
        }
    }

    /**
     * 查找所有需要修复的应用程序
     */
    findAllApplications() {
        const apps = [];
        
        if (!fs.existsSync(this.distDir)) {
            return apps;
        }
        
        // 查找.app文件
        const findApps = (dir) => {
            const items = fs.readdirSync(dir);
            for (const item of items) {
                const itemPath = path.join(dir, item);
                const stat = fs.statSync(itemPath);
                
                if (item.endsWith('.app')) {
                    apps.push(itemPath);
                } else if (stat.isDirectory() && !item.endsWith('.dmg')) {
                    findApps(itemPath);
                }
            }
        };
        
        findApps(this.distDir);
        return apps;
    }

    /**
     * 主修复流程
     */
    async ultimateFix() {
        try {
            this.log('🚀 开始终极修复流程...', 'step');
            this.log('🎯 目标: 彻底解决"已损坏"问题', 'info');
            
            // 1. 查找所有应用程序
            const apps = this.findAllApplications();
            if (apps.length === 0) {
                this.log('❌ 未找到应用程序', 'error');
                return false;
            }
            
            this.log(`📱 找到 ${apps.length} 个应用程序`, 'info');
            
            // 2. 修复每个应用程序
            let successCount = 0;
            for (const app of apps) {
                if (await this.fixApplicationCompletely(app)) {
                    successCount++;
                }
            }
            
            // 3. 修复DMG文件
            await this.fixDMGFiles();
            
            // 4. 生成报告
            this.generateFixReport(apps.length, successCount);
            
            this.log('🎉 终极修复完成!', 'success');
            return successCount === apps.length;
            
        } catch (error) {
            this.log(`修复失败: ${error.message}`, 'error');
            return false;
        }
    }

    /**
     * 生成修复报告
     */
    generateFixReport(totalApps, successCount) {
        this.log('📋 生成修复报告', 'step');
        
        console.log('\n' + '='.repeat(60));
        console.log('🛠️  应用程序修复报告');
        console.log('='.repeat(60));
        console.log(`📱 处理应用数量: ${totalApps}`);
        console.log(`✅ 修复成功数量: ${successCount}`);
        console.log(`📊 成功率: ${Math.round((successCount/totalApps)*100)}%`);
        console.log('');
        console.log('🔧 修复内容:');
        console.log('   • 彻底清理所有可疑扩展属性'); 
        console.log('   • 递归处理所有子文件和目录');
        console.log('   • 多重签名策略确保兼容性');
        console.log('   • Gatekeeper兼容性验证');
        console.log('');
        console.log('✨ 预期效果:');
        console.log('   • 应用应该显示"无法验证"而不是"已损坏"');
        console.log('   • 用户可以右键选择"打开"来运行应用');
        console.log('   • 或在系统偏好设置中允许来自任何来源的应用');
        console.log('='.repeat(60));
    }
}

// 只在macOS上运行
if (process.platform !== 'darwin') {
    console.log('❌ 此脚本只能在macOS上运行');
    process.exit(1);
}

// 运行修复
if (require.main === module) {
    const fixer = new UltimateAppFixer();
    fixer.ultimateFix().then(success => {
        process.exit(success ? 0 : 1);
    });
}

module.exports = UltimateAppFixer;