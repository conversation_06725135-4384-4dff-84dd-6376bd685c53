/**
 * 🔒 锁定的DMG修复脚本 - 禁止随意修改！
 * 
 * 锁定时间: 2025-08-05 21:44:00
 * 配置状态: PERMANENTLY_LOCKED
 * 
 * 完美配置:
 * - 窗口: 600x480 位置{438,230}
 * - 图标: 100px
 * - 内容: 应用+教程图片+Applications链接
 * - 签名: adhoc深度签名
 * 
 * ⚠️ 除非明确要求变更，否则禁止修改此配置！
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

/**
 * 优化的DMG修复脚本 - 减少挂载操作，提升打包速度
 */
class RobustDMGSigner {
  constructor(context) {
    this.context = context;
    this.outDir = context.outDir;
    this.projectDir = context.configuration.projectDir || process.cwd();
    this.maxRetries = 2; // 减少重试次数
    this.retryDelay = 1000; // 减少等待时间
    
    console.log(`🔧 启动优化的DMG修复流程`);
    console.log(`📁 输出目录: ${this.outDir}`);
  }

  async execute() {
    try {
      // 1. 快速清理相关挂载点
      await this.forceCleanupMounts();
      
      // 2. 查找需要修复的DMG文件
      const dmgFiles = this.findDMGFiles();
      
      if (dmgFiles.length === 0) {
        console.log('⚠️ 未找到DMG文件，跳过修复');
        return;
      }

      console.log(`📦 找到 ${dmgFiles.length} 个DMG文件需要修复:`);
      dmgFiles.forEach(file => console.log(`  - ${path.basename(file)}`));

      // 3. 顺序修复每个DMG（优化流程）
      for (const dmgFile of dmgFiles) {
        await this.fixDMGOptimized(dmgFile);
        await this.sleep(500); // 减少等待时间
      }

      console.log(`✅ 所有DMG修复完成`);

    } catch (error) {
      console.error(`❌ DMG修复失败:`, error.message);
      await this.forceCleanupMounts();
      throw error;
    }
  }

  async forceCleanupMounts() {
    console.log('🧹 快速清理相关DMG挂载点...');
    
    try {
      const targetVolumes = [
        '/Volumes/小梅花AI智能客服-1.0.0',
        '/Volumes/小梅花AI智能客服-1.0.0 1',
        '/Volumes/小梅花AI智能客服-1.0.0 2',
        '/Volumes/小梅花AI智能客服-1.0.0 3'
      ];
      
      for (const vol of targetVolumes) {
        if (fs.existsSync(vol)) {
          try {
            console.log(`  🗑️ 快速卸载: ${vol}`);
            execSync(`hdiutil detach "${vol}" -force -quiet`, { 
              stdio: 'pipe', 
              timeout: 5000 
            });
          } catch (e) {
            // 忽略卸载失败
          }
        }
      }
      
      await this.sleep(500);
      console.log('✅ 快速清理完成');
      
    } catch (error) {
      console.log('⚠️ 快速清理部分失败，但继续执行');
    }
  }

  findDMGFiles() {
    const dmgFiles = [];
    
    if (!fs.existsSync(this.outDir)) {
      return dmgFiles;
    }
    
    const files = fs.readdirSync(this.outDir);
    for (const file of files) {
      if (file.endsWith('.dmg') && !file.includes('FIXED')) {
        dmgFiles.push(path.join(this.outDir, file));
      }
    }
    
    return dmgFiles;
  }

  async fixDMGOptimized(dmgFile) {
    const fileName = path.basename(dmgFile);
    console.log(`\n🔧 修复DMG: ${fileName}`);
    
    let volumePath = null;
    let retryCount = 0;
    
    while (retryCount < this.maxRetries) {
      try {
        // 1. 快速挂载DMG验证
        volumePath = await this.quickMountDMG(dmgFile);
        if (!volumePath) {
          throw new Error('无法快速挂载DMG');
        }
        
        console.log(`  📍 挂载点: ${volumePath}`);
        
        // 2. 快速验证内容
        const appInDMG = path.join(volumePath, '小梅花AI智能客服.app');
        if (!fs.existsSync(appInDMG)) {
          throw new Error('DMG中未找到应用文件');
        }
        
        // 3. 确定架构并找到对应的签名应用
        const arch = this.getArchFromDMGName(fileName);
        const signedAppPath = this.findSignedApp(arch);
        
        if (!signedAppPath) {
          throw new Error(`未找到${arch}架构的已签名应用`);
        }
        
        console.log(`  🏗️ 架构: ${arch}`);
        console.log(`  📱 源应用: ${path.basename(signedAppPath)}`);
        
        // 4. 快速验证源应用签名
        if (!this.quickVerifySignature(signedAppPath)) {
          throw new Error('源应用签名验证失败');
        }
        
        // 5. 快速卸载DMG
        await this.quickUnmountDMG(volumePath);
        volumePath = null;
        
        // 6. 使用锁定配置重新创建DMG（一次性完成）
        await this.recreateDMGWithLockedConfig(dmgFile, signedAppPath, arch);
        
        console.log(`  ✅ ${fileName} 修复完成`);
        return;
        
      } catch (error) {
        retryCount++;
        console.error(`  ❌ 第${retryCount}次修复失败: ${error.message}`);
        
        if (volumePath) {
          await this.quickUnmountDMG(volumePath);
          volumePath = null;
        }
        
        if (retryCount >= this.maxRetries) {
          throw new Error(`${fileName} 修复失败，已重试${this.maxRetries}次`);
        }
        
        console.log(`  ⏰ 等待${this.retryDelay/1000}秒后重试...`);
        await this.sleep(this.retryDelay);
      }
    }
  }

  async quickMountDMG(dmgFile) {
    // 优化的快速挂载：只尝试1次，减少超时
    try {
      console.log(`  📂 快速挂载DMG...`);
      
      execSync(`hdiutil attach "${dmgFile}" -quiet -nobrowse -noautoopen`, { 
        stdio: 'pipe',
        timeout: 15000
      });
      
      await this.sleep(500);
      
      const volumePath = this.findVolumePath();
      if (volumePath && fs.existsSync(volumePath)) {
        return volumePath;
      }
      
      throw new Error('挂载后未找到有效挂载点');
      
    } catch (error) {
      console.log(`    ⚠️ 快速挂载失败: ${error.message}`);
      throw error;
    }
  }

  async quickUnmountDMG(volumePath) {
    if (!volumePath || !fs.existsSync(volumePath)) {
      return;
    }
    
    console.log(`  📤 快速卸载DMG: ${volumePath}`);
    
    try {
      execSync(`hdiutil detach "${volumePath}" -quiet`, { 
        stdio: 'pipe', 
        timeout: 10000 
      });
      await this.sleep(500);
      console.log(`    ✅ 快速卸载成功`);
    } catch (e) {
      try {
        execSync(`hdiutil detach "${volumePath}" -force`, { 
          stdio: 'pipe', 
          timeout: 5000 
        });
        console.log(`    ✅ 强制卸载成功`);
      } catch (e2) {
        console.log(`    ⚠️ 卸载失败，但继续执行`);
      }
    }
  }

  findVolumePath() {
    try {
      const volumes = fs.readdirSync('/Volumes');
      for (const vol of volumes) {
        if (vol.includes('小梅花') || vol.includes('xiaomeihua')) {
          const fullPath = `/Volumes/${vol}`;
          if (fs.existsSync(path.join(fullPath, '小梅花AI智能客服.app'))) {
            return fullPath;
          }
        }
      }
    } catch (e) {
      // 忽略错误
    }
    return null;
  }

  getArchFromDMGName(fileName) {
    if (fileName.includes('arm64')) {
      return 'arm64';
    } else if (fileName.includes('x64')) {
      return 'x64';
    }
    return 'unknown';
  }

  findSignedApp(arch) {
    const baseDir = this.outDir;
    
    const possiblePaths = [
      path.join(baseDir, `mac-${arch}`, '小梅花AI智能客服.app'),
      path.join(baseDir, 'mac', '小梅花AI智能客服.app'),
      path.join(baseDir, `mac-${arch === 'x64' ? 'x64' : 'arm64'}`, '小梅花AI智能客服.app')
    ];
    
    for (const appPath of possiblePaths) {
      if (fs.existsSync(appPath)) {
        try {
          const result = execSync(`file "${appPath}/Contents/MacOS/小梅花AI智能客服"`, 
            { encoding: 'utf8', stdio: 'pipe' });
          
          if ((arch === 'x64' && result.includes('x86_64')) ||
              (arch === 'arm64' && result.includes('arm64'))) {
            return appPath;
          }
        } catch (e) {
          continue;
        }
      }
    }
    
    return null;
  }

  quickVerifySignature(appPath) {
    try {
      const result = execSync(
        `codesign --verify --verbose=1 "${appPath}" 2>&1`,
        { encoding: 'utf8', stdio: 'pipe' }
      );
      
      const isValid = result.includes('valid on disk') || result.trim() === '';
      
      if (isValid) {
        console.log('    ✅ 源应用签名验证通过');
        return true;
      } else {
        console.log('    ❌ 源应用签名验证失败');
        return false;
      }
    } catch (error) {
      console.log('    ❌ 源应用签名验证失败:', error.message);
      return false;
    }
  }

  async recreateDMGWithLockedConfig(originalDMG, signedAppPath, arch) {
    console.log('  🔄 使用锁定配置重新创建DMG...');
    
    const backupDMG = originalDMG + '.backup';
    const tempDir = path.join(this.outDir, `temp-dmg-${arch}-${Date.now()}`);
    
    try {
      // 1. 备份原DMG
      fs.renameSync(originalDMG, backupDMG);
      
      // 2. 创建临时目录结构
      fs.mkdirSync(tempDir, { recursive: true });
      
      // 3. 复制已签名的应用
      console.log('    📋 复制已签名应用...');
      execSync(`cp -R "${signedAppPath}" "${tempDir}/"`, { stdio: 'pipe' });
      
      // 4. 创建Applications链接
      const appsLink = path.join(tempDir, 'Applications');
      if (!fs.existsSync(appsLink)) {
        try {
          fs.symlinkSync('/Applications', appsLink);
          console.log('    🔗 Applications链接创建成功');
        } catch (e) {
          console.log('    ⚠️ Applications链接创建失败，但继续执行');
        }
      }
      
      // 5. 复制安装教程图片
      const tutorialImage = path.join(this.projectDir, 'build', 'Mac安装教程.png');
      if (fs.existsSync(tutorialImage)) {
        console.log('    📸 复制Mac安装教程图片...');
        execSync(`cp "${tutorialImage}" "${tempDir}/"`, { stdio: 'pipe' });
        console.log('    ✅ Mac安装教程.png 复制成功');
      } else {
        console.log('    ❌ 未找到Mac安装教程.png，路径:', tutorialImage);
      }
      
      // 6. 一次性创建最终DMG并应用锁定布局
      console.log('    🔨 创建最终DMG并应用锁定布局...');
      await this.createDMGWithLockedLayout(originalDMG, tempDir);
      
      // 7. 快速验证（不再挂载）
      console.log('    🔍 快速验证DMG完整性...');
      if (fs.existsSync(originalDMG) && fs.statSync(originalDMG).size > 1024 * 1024) {
        console.log('    ✅ 新DMG验证通过');
        fs.unlinkSync(backupDMG);
      } else {
        console.log('    ❌ 新DMG验证失败，恢复备份');
        if (fs.existsSync(originalDMG)) {
          fs.unlinkSync(originalDMG);
        }
        fs.renameSync(backupDMG, originalDMG);
        throw new Error('新DMG验证失败');
      }
      
    } finally {
      // 清理临时目录
      try {
        execSync(`rm -rf "${tempDir}"`, { stdio: 'pipe' });
      } catch (e) {
        console.log('    ⚠️ 临时目录清理失败');
      }
    }
  }

  async createDMGWithLockedLayout(finalDMG, tempDir) {
    const dmgTitle = `小梅花AI智能客服-1.0.0`;
    const tempDMG = finalDMG.replace('.dmg', '-temp.dmg');
    let volumePath = null;
    
    try {
      // 创建可读写的临时DMG
      const createTempCmd = [
        `hdiutil create "${tempDMG}"`,
        `-volname "${dmgTitle}"`,
        `-srcfolder "${tempDir}"`,
        `-ov`,
        `-format UDRW`,
        `-quiet`
      ].join(' ');
      
      execSync(createTempCmd, { stdio: 'pipe' });
      
      // 挂载并应用锁定布局（一次性完成）
      console.log('    📂 挂载DMG应用锁定布局...');
      execSync(`hdiutil attach "${tempDMG}" -readwrite -nobrowse -noautoopen`, { stdio: 'pipe' });
      await this.sleep(1500);
      
      volumePath = `/Volumes/${dmgTitle}`;
      if (!fs.existsSync(volumePath)) {
        throw new Error('DMG挂载失败');
      }
      
      // 清理旧设置
      try {
        execSync(`rm -f "${volumePath}/.DS_Store"`, { stdio: 'pipe' });
      } catch (e) {
        // 忽略删除失败
      }
      
      // 应用锁定的完美布局
      await this.applyLockedLayout(dmgTitle);
      
      // 保存设置并卸载
      console.log('    💾 保存布局设置...');
      execSync(`sync`, { stdio: 'pipe' });
      await this.sleep(500);
      execSync(`touch "${volumePath}/.DS_Store"`, { stdio: 'pipe' });
      execSync(`sync`, { stdio: 'pipe' });
      
      await this.quickUnmountDMG(volumePath);
      volumePath = null;
      
      // 转换为最终压缩格式
      console.log('    🗜️  转换为最终格式...');
      const convertCmd = [
        `hdiutil convert "${tempDMG}"`,
        `-format UDBZ`,
        `-imagekey zlib-level=9`,
        `-o "${finalDMG}"`,
        `-ov`,
        `-quiet`
      ].join(' ');
      
      execSync(convertCmd, { stdio: 'pipe' });
      
    } finally {
      // 清理
      if (volumePath && fs.existsSync(volumePath)) {
        await this.quickUnmountDMG(volumePath);
      }
      try {
        if (fs.existsSync(tempDMG)) {
          fs.unlinkSync(tempDMG);
        }
      } catch (e) {
        console.log('    ⚠️ 临时DMG清理失败');
      }
    }
  }

  async applyLockedLayout(dmgTitle) {
    // 🔒 锁定的完美布局配置 - 禁止修改！
    const lockedScript = `
tell application "Finder"
  tell disk "${dmgTitle}"
    open
    delay 1
    
    set current view of container window to icon view
    set toolbar visible of container window to false
    set statusbar visible of container window to false
    
    -- 🔒 锁定窗口大小和位置: 600x480 at {438,230}
    set the bounds of container window to {438, 230, 1038, 710}
    
    set viewOptions to the icon view options of container window
    set arrangement of viewOptions to not arranged
    set icon size of viewOptions to 100
    set text size of viewOptions to 12
    set shows item info of viewOptions to false
    set shows icon preview of viewOptions to true
    
    delay 1
    
    -- 🔒 锁定元素位置
    try
      set position of item "小梅花AI智能客服.app" of container window to {160, 280}
    end try
    
    try  
      set position of item "Applications" of container window to {480, 280}
    end try
    
    try
      if exists item "Mac安装教程.png" then
        set position of item "Mac安装教程.png" of container window to {300, 100}
      end if
    end try
    
    update every item of container window
    delay 1
    close
    
  end tell
end tell`;
    
    console.log('    🎨 应用锁定布局配置...');
    execSync(`osascript -e '${lockedScript}'`, { 
      stdio: 'pipe', 
      timeout: 20000
    });
    console.log('    ✅ 锁定布局应用成功');
  }

  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// electron-builder afterAllArtifactBuild 钩子导出
module.exports = async function(context) {
  // 只在 macOS 平台执行
  const macArtifacts = context.artifactPaths.filter(p => p.endsWith('.dmg'));
  
  if (macArtifacts.length === 0) {
    console.log('跳过非macOS平台的DMG修复');
    return;
  }

  const signer = new RobustDMGSigner(context);
  await signer.execute();
};