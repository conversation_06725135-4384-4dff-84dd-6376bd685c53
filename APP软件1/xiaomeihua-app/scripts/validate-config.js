// 配置验证脚本 - 自动生成，请勿手动修改
const fs = require('fs');
const path = require('path');

function validateConfig() {
  const packageJsonPath = path.join(__dirname, '..', 'package.json');
  const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
  
  // 验证DMG配置
  const dmgConfig = packageJson.build.dmg;
  if (!dmgConfig || !dmgConfig.contents || dmgConfig.contents.length < 3) {
    throw new Error('DMG配置不完整');
  }
  
  // 验证构建钩子
  if (!packageJson.build.afterPack || !packageJson.build.afterAllArtifactBuild) {
    throw new Error('构建钩子配置缺失');
  }
  
  console.log('✅ 配置验证通过');
}

if (require.main === module) {
  validateConfig();
}

module.exports = validateConfig;
