// ==UserScript==
// @name         小梅花URL匹配演示脚本
// @namespace    https://xiaomeihuakefu.cn/
// @version      1.0.0
// @description  演示URL匹配功能的示例脚本
// <AUTHOR>
// @match        *://store.weixin.qq.com/*
// @match        *://example.com/*
// @grant        GM_setValue
// @grant        GM_getValue
// @grant        GM_addStyle
// ==/UserScript==

(function() {
    'use strict';
    
    console.log('小梅花URL匹配演示脚本已加载');
    console.log('当前页面URL:', window.location.href);
    
    // 添加样式
    GM_addStyle(`
        .xiaomeihua-demo-banner {
            position: fixed;
            top: 10px;
            right: 10px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 20px;
            border-radius: 8px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
            z-index: 10000;
            font-family: Arial, sans-serif;
            font-size: 14px;
            max-width: 300px;
            animation: slideIn 0.5s ease-out;
        }
        
        @keyframes slideIn {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }
        
        .xiaomeihua-demo-banner h3 {
            margin: 0 0 10px 0;
            font-size: 16px;
        }
        
        .xiaomeihua-demo-banner p {
            margin: 5px 0;
            font-size: 12px;
            opacity: 0.9;
        }
        
        .xiaomeihua-demo-close {
            position: absolute;
            top: 5px;
            right: 10px;
            background: none;
            border: none;
            color: white;
            font-size: 18px;
            cursor: pointer;
            opacity: 0.7;
        }
        
        .xiaomeihua-demo-close:hover {
            opacity: 1;
        }
    `);
    
    // 创建演示横幅
    function createDemoBanner() {
        const banner = document.createElement('div');
        banner.className = 'xiaomeihua-demo-banner';
        banner.innerHTML = `
            <button class="xiaomeihua-demo-close" onclick="this.parentElement.remove()">×</button>
            <h3>🎉 URL匹配功能演示</h3>
            <p><strong>脚本名称:</strong> 小梅花URL匹配演示脚本</p>
            <p><strong>当前URL:</strong> ${window.location.href}</p>
            <p><strong>匹配规则:</strong> 此脚本只在指定URL页面加载</p>
            <p><strong>加载时间:</strong> ${new Date().toLocaleTimeString()}</p>
        `;
        
        document.body.appendChild(banner);
        
        // 5秒后自动隐藏
        setTimeout(() => {
            if (banner.parentElement) {
                banner.style.animation = 'slideIn 0.5s ease-out reverse';
                setTimeout(() => banner.remove(), 500);
            }
        }, 5000);
    }
    
    // 页面特定功能
    function initPageSpecificFeatures() {
        const hostname = window.location.hostname;
        const pathname = window.location.pathname;
        
        if (hostname.includes('store.weixin.qq.com')) {
            console.log('检测到微信小商店页面，初始化小商店功能...');
            initWechatStoreFeatures();
        } else if (hostname.includes('example.com')) {
            console.log('检测到示例网站页面，初始化示例功能...');
            initExampleFeatures();
        } else {
            console.log('未知页面类型，使用通用功能...');
            initGenericFeatures();
        }
    }
    
    // 微信小商店功能
    function initWechatStoreFeatures() {
        console.log('微信小商店功能已初始化');
        
        // 添加小商店特定的功能
        const storeInfo = {
            type: '微信小商店',
            features: ['商品管理', '订单处理', '客户服务'],
            initialized: new Date().toISOString()
        };
        
        GM_setValue('store_info', storeInfo);
        
        // 在控制台显示店铺信息
        console.table(storeInfo);
    }
    
    // 示例网站功能
    function initExampleFeatures() {
        console.log('示例网站功能已初始化');
        
        // 添加示例网站特定的功能
        const exampleInfo = {
            type: '示例网站',
            features: ['演示功能', '测试工具', '开发辅助'],
            initialized: new Date().toISOString()
        };
        
        GM_setValue('example_info', exampleInfo);
        
        // 在控制台显示网站信息
        console.table(exampleInfo);
    }
    
    // 通用功能
    function initGenericFeatures() {
        console.log('通用功能已初始化');
        
        // 添加通用功能
        const genericInfo = {
            type: '通用页面',
            features: ['基础功能', '通用工具'],
            initialized: new Date().toISOString()
        };
        
        GM_setValue('generic_info', genericInfo);
        
        // 在控制台显示通用信息
        console.table(genericInfo);
    }
    
    // 主初始化函数
    function init() {
        console.log('开始初始化小梅花URL匹配演示脚本...');
        
        // 等待页面加载完成
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                setTimeout(init, 100);
            });
            return;
        }
        
        // 创建演示横幅
        createDemoBanner();
        
        // 初始化页面特定功能
        initPageSpecificFeatures();
        
        // 记录脚本运行信息
        const runInfo = {
            url: window.location.href,
            timestamp: new Date().toISOString(),
            userAgent: navigator.userAgent,
            scriptVersion: '1.0.0'
        };
        
        GM_setValue('last_run_info', runInfo);
        
        console.log('小梅花URL匹配演示脚本初始化完成');
        console.log('运行信息:', runInfo);
    }
    
    // 启动脚本
    init();
    
})();
