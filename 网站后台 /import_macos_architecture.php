<?php
/**
 * macOS架构支持功能导入脚本
 * 用于安全导入11_macOS_architecture_support_safe.sql
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 数据库配置
$db_config = [
    'host' => '127.0.0.1',
    'port' => '3306',
    'dbname' => 'xiaomeihuakefu_c',
    'username' => 'xiaomeihuakefu_c',
    'password' => '7Da5F1Xx995cxYz8',
    'charset' => 'utf8mb4'
];

try {
    // 创建数据库连接
    $dsn = "mysql:host={$db_config['host']};port={$db_config['port']};dbname={$db_config['dbname']};charset={$db_config['charset']}";
    $pdo = new PDO($dsn, $db_config['username'], $db_config['password'], [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_EMULATE_PREPARES => false,
    ]);
    
    echo "✅ 数据库连接成功\n";
    
    // 读取SQL文件
    $sqlFile = __DIR__ . '/11_macOS_architecture_support_safe.sql';
    if (!file_exists($sqlFile)) {
        throw new Exception("SQL文件不存在: $sqlFile");
    }
    
    $sql = file_get_contents($sqlFile);
    echo "✅ SQL文件读取成功\n";
    
    // 分割SQL语句
    $statements = array_filter(array_map('trim', explode(';', $sql)));
    
    echo "📝 开始执行 " . count($statements) . " 条SQL语句...\n\n";
    
    $successCount = 0;
    $errorCount = 0;
    
    foreach ($statements as $index => $statement) {
        if (empty($statement) || strpos($statement, '--') === 0) {
            continue;
        }
        
        try {
            $pdo->exec($statement);
            $successCount++;
            echo "✅ 语句 " . ($index + 1) . " 执行成功\n";
            
            // 显示语句的前50个字符
            $preview = substr(str_replace(["\n", "\r", "\t"], ' ', $statement), 0, 50);
            echo "   预览: $preview...\n\n";
            
        } catch (PDOException $e) {
            $errorCount++;
            echo "❌ 语句 " . ($index + 1) . " 执行失败\n";
            echo "   错误: " . $e->getMessage() . "\n";
            echo "   SQL: " . substr($statement, 0, 100) . "...\n\n";
            
            // 记录详细错误信息
            error_log("SQL执行失败: " . $e->getMessage() . " - SQL: " . $statement);
        }
    }
    
    echo "\n" . str_repeat("=", 50) . "\n";
    echo "📊 导入结果统计:\n";
    echo "✅ 成功: $successCount 条\n";
    echo "❌ 失败: $errorCount 条\n";
    echo "📝 总计: " . ($successCount + $errorCount) . " 条\n";
    
    if ($errorCount === 0) {
        echo "\n🎉 所有SQL语句执行成功！macOS架构支持功能已安装完成。\n";
    } else {
        echo "\n⚠️  部分SQL语句执行失败，请检查错误信息。\n";
    }
    
    // 验证安装结果
    echo "\n" . str_repeat("=", 50) . "\n";
    echo "🔍 验证安装结果:\n";
    
    // 检查表是否创建成功
    $tables = ['macos_architecture_logs', 'download_stats_by_architecture', 'macos_architecture_config'];
    foreach ($tables as $table) {
        try {
            $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
            if ($stmt->rowCount() > 0) {
                echo "✅ 表 '$table' 创建成功\n";
            } else {
                echo "❌ 表 '$table' 未找到\n";
            }
        } catch (Exception $e) {
            echo "❌ 检查表 '$table' 时出错: " . $e->getMessage() . "\n";
        }
    }
    
    // 检查配置数据
    try {
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM macos_architecture_config");
        $result = $stmt->fetch();
        echo "✅ 配置数据: " . $result['count'] . " 条记录\n";
    } catch (Exception $e) {
        echo "❌ 检查配置数据时出错: " . $e->getMessage() . "\n";
    }
    
    // 检查测试版本数据
    try {
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM app_updates WHERE version_code = 103");
        $result = $stmt->fetch();
        echo "✅ 测试版本数据: " . $result['count'] . " 条记录\n";
    } catch (Exception $e) {
        echo "❌ 检查测试版本数据时出错: " . $e->getMessage() . "\n";
    }
    
} catch (Exception $e) {
    echo "❌ 导入失败: " . $e->getMessage() . "\n";
    error_log("macOS架构支持功能导入失败: " . $e->getMessage());
    exit(1);
}

echo "\n✨ 导入脚本执行完成！\n";
?>
