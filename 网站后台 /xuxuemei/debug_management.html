<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI客服设置管理功能调试</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }
        
        .debug-container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 30px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .debug-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .debug-header h1 {
            font-size: 32px;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
        }
        
        .debug-section {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .debug-section h3 {
            color: #4CAF50;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .btn {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
            margin: 10px 10px 10px 0;
        }
        
        .btn:hover {
            background: linear-gradient(135deg, #45a049, #4CAF50);
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
        }
        
        .btn-secondary {
            background: linear-gradient(135deg, #2196F3, #1976D2);
        }
        
        .btn-secondary:hover {
            background: linear-gradient(135deg, #1976D2, #2196F3);
            box-shadow: 0 4px 15px rgba(33, 150, 243, 0.3);
        }
        
        .btn-danger {
            background: linear-gradient(135deg, #F44336, #D32F2F);
        }
        
        .btn-danger:hover {
            background: linear-gradient(135deg, #D32F2F, #F44336);
            box-shadow: 0 4px 15px rgba(244, 67, 54, 0.3);
        }
        
        .debug-output {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 8px;
            padding: 15px;
            margin-top: 15px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .status-badge {
            background: rgba(76, 175, 80, 0.2);
            color: #4CAF50;
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 14px;
            font-weight: 600;
            border: 1px solid rgba(76, 175, 80, 0.3);
        }
        
        .status-badge.error {
            background: rgba(244, 67, 54, 0.2);
            color: #F44336;
            border-color: rgba(244, 67, 54, 0.3);
        }
    </style>
</head>
<body>
    <div class="debug-container">
        <div class="debug-header">
            <h1>
                <i class="fas fa-bug"></i>
                AI客服设置管理功能调试
            </h1>
            <p>调试和测试DeepSeek和豆包管理模块的编辑删除功能</p>
            <span class="status-badge">调试模式</span>
        </div>

        <div class="debug-section">
            <h3><i class="fas fa-play"></i> 快速测试</h3>
            <button class="btn" onclick="openMainPage()">
                <i class="fas fa-external-link-alt"></i>
                打开AI客服设置页面
            </button>
            <button class="btn btn-secondary" onclick="runDebugTests()">
                <i class="fas fa-vial"></i>
                运行调试测试
            </button>
        </div>

        <div class="debug-section">
            <h3><i class="fas fa-list"></i> 修复内容</h3>
            <ul style="list-style: none; padding: 0;">
                <li style="padding: 8px 0; display: flex; align-items: center; gap: 10px;">
                    <i class="fas fa-check" style="color: #4CAF50;"></i>
                    修复了按钮onclick事件无响应问题（改用事件委托）
                </li>
                <li style="padding: 8px 0; display: flex; align-items: center; gap: 10px;">
                    <i class="fas fa-check" style="color: #4CAF50;"></i>
                    修复了CSS排版问题（justify-content: space-between）
                </li>
                <li style="padding: 8px 0; display: flex; align-items: center; gap: 10px;">
                    <i class="fas fa-check" style="color: #4CAF50;"></i>
                    重新实现了编辑和删除功能
                </li>
                <li style="padding: 8px 0; display: flex; align-items: center; gap: 10px;">
                    <i class="fas fa-check" style="color: #4CAF50;"></i>
                    添加了详细的调试信息和错误处理
                </li>
                <li style="padding: 8px 0; display: flex; align-items: center; gap: 10px;">
                    <i class="fas fa-check" style="color: #4CAF50;"></i>
                    优化了管理模块的渲染条件
                </li>
            </ul>
        </div>

        <div class="debug-section">
            <h3><i class="fas fa-clipboard-list"></i> 测试步骤</h3>
            <ol style="padding-left: 20px;">
                <li>点击"打开AI客服设置页面"</li>
                <li>在DeepSeek标签页中配置一些设置并保存</li>
                <li>查看底部的"DeepSeek管理"模块是否显示</li>
                <li>点击"编辑"按钮，检查是否跳转到设置页面顶部</li>
                <li>点击"删除"按钮，检查是否弹出确认对话框</li>
                <li>确认删除后，检查设置是否被重置</li>
                <li>在豆包标签页中重复相同测试</li>
            </ol>
        </div>

        <div class="debug-section">
            <h3><i class="fas fa-terminal"></i> 调试输出</h3>
            <div class="debug-output" id="debugOutput">
                等待调试信息...
            </div>
            <button class="btn btn-secondary" onclick="clearDebugOutput()">
                <i class="fas fa-trash"></i>
                清空输出
            </button>
        </div>
    </div>

    <script>
        function openMainPage() {
            window.open('templates/ai_service_settings.php', '_blank');
        }

        function runDebugTests() {
            const output = document.getElementById('debugOutput');
            output.innerHTML = `
<div style="color: #4CAF50;">[${new Date().toLocaleTimeString()}] 开始调试测试...</div>
<div style="color: #2196F3;">[INFO] 检查修复内容：</div>
<div style="color: #FFF;">✓ 按钮事件：使用data-action和data-type属性</div>
<div style="color: #FFF;">✓ 事件处理：使用事件委托机制</div>
<div style="color: #FFF;">✓ 函数实现：handleEditSettings 和 handleDeleteSettings</div>
<div style="color: #FFF;">✓ 错误处理：添加了try-catch和详细日志</div>
<div style="color: #FFF;">✓ CSS修复：justify-content: space-between</div>
<div style="color: #4CAF50;">[SUCCESS] 所有修复已应用</div>
<div style="color: #FF9800;">[NOTICE] 请在实际页面中测试功能</div>
            `;
        }

        function clearDebugOutput() {
            document.getElementById('debugOutput').innerHTML = '等待调试信息...';
        }

        // 监听来自主页面的消息
        window.addEventListener('message', function(event) {
            const output = document.getElementById('debugOutput');
            const timestamp = new Date().toLocaleTimeString();
            output.innerHTML += `<div style="color: #4CAF50;">[${timestamp}] ${event.data}</div>`;
            output.scrollTop = output.scrollHeight;
        });
    </script>
</body>
</html>
