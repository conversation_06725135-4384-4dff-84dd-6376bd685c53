<?php
// 检查用户是否已登录
if (!isset($_SESSION['admin_user_id'])) {
    header('Location: login.php');
    exit;
}

// 加载数据库连接
require_once '../includes/db.php';
require_once '../includes/functions.php';

// 获取API安全配置
function getApiSecurityConfig() {
    global $pdo;
    $config = [];
    
    try {
        // 获取黑名单IP
        $stmt = $pdo->query("SELECT ip_address, reason, expires_at FROM api_blacklist WHERE expires_at > NOW() OR expires_at IS NULL");
        $blacklistIps = $stmt->fetchAll();
        
        // 获取安全配置
        $stmt = $pdo->query("SELECT config_key, config_value, is_encrypted FROM api_security_config");
        while ($row = $stmt->fetch()) {
            $config[$row['config_key']] = $row['is_encrypted'] ? '******' : $row['config_value'];
        }
        
        // 获取安全统计信息
        $stmt = $pdo->query("SELECT COUNT(*) FROM api_logs WHERE response_status = 'error' AND created_at > DATE_SUB(NOW(), INTERVAL 24 HOUR)");
        $config['error_count_24h'] = $stmt->fetchColumn();
        
        $stmt = $pdo->query("SELECT COUNT(*) FROM api_logs WHERE created_at > DATE_SUB(NOW(), INTERVAL 24 HOUR)");
        $config['total_requests_24h'] = $stmt->fetchColumn();
        
        $stmt = $pdo->query("SELECT COUNT(DISTINCT ip_address) FROM api_logs WHERE created_at > DATE_SUB(NOW(), INTERVAL 24 HOUR)");
        $config['unique_ips_24h'] = $stmt->fetchColumn();
        
        $stmt = $pdo->query("SELECT ip_address, COUNT(*) as count FROM api_logs WHERE response_status = 'error' AND created_at > DATE_SUB(NOW(), INTERVAL 24 HOUR) GROUP BY ip_address ORDER BY count DESC LIMIT 5");
        $config['top_error_ips'] = $stmt->fetchAll();
        
        $config['blacklist_ips'] = $blacklistIps;
    } catch (PDOException $e) {
        error_log("获取API安全配置失败: " . $e->getMessage());
    }
    
    return $config;
}

// 添加IP到黑名单
function addToBlacklist($ip, $reason, $expiryHours) {
    global $pdo;
    
    try {
        // 计算过期时间
        $expiryDate = $expiryHours > 0 
            ? date('Y-m-d H:i:s', strtotime("+{$expiryHours} hours")) 
            : null;
        
        // 生成指纹
        $fingerprint = md5($ip . time());
        
        // 添加到黑名单
        $stmt = $pdo->prepare("
            INSERT INTO api_blacklist (fingerprint, ip_address, reason, expires_at, created_at) 
            VALUES (?, ?, ?, ?, NOW())
            ON DUPLICATE KEY UPDATE reason = ?, expires_at = ?, created_at = NOW()
        ");
        
        return $stmt->execute([$fingerprint, $ip, $reason, $expiryDate, $reason, $expiryDate]);
    } catch (PDOException $e) {
        error_log("添加IP到黑名单失败: " . $e->getMessage());
        return false;
    }
}

// 从黑名单中移除IP
function removeFromBlacklist($ip) {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("DELETE FROM api_blacklist WHERE ip_address = ?");
        return $stmt->execute([$ip]);
    } catch (PDOException $e) {
        error_log("从黑名单中移除IP失败: " . $e->getMessage());
        return false;
    }
}

// 更新安全配置
function updateSecurityConfig($data) {
    global $pdo;
    
    try {
        $pdo->beginTransaction();
        
        foreach ($data as $key => $value) {
            // 跳过加密字段的空值或掩码值
            if (in_array($key, ['encryption_key', 'signature_salt', 'token_secret']) && ($value === '******' || empty($value))) {
                continue;
            }
            
            // 判断是否需要加密
            $isEncrypted = in_array($key, ['encryption_key', 'signature_salt', 'token_secret']) ? 1 : 0;
            
            $stmt = $pdo->prepare("
                INSERT INTO api_security_config (config_key, config_value, is_encrypted) 
                VALUES (?, ?, ?) 
                ON DUPLICATE KEY UPDATE config_value = ?, is_encrypted = ?
            ");
            
            $stmt->execute([$key, $value, $isEncrypted, $value, $isEncrypted]);
        }
        
        $pdo->commit();
        return true;
    } catch (PDOException $e) {
        $pdo->rollBack();
        error_log("更新安全配置失败: " . $e->getMessage());
        return false;
    }
}

// 处理表单提交
$message = '';
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['add_to_blacklist'])) {
        $ip = $_POST['ip_address'];
        $reason = $_POST['reason'];
        $expiryHours = (int)$_POST['expiry_hours'];
        
        if (filter_var($ip, FILTER_VALIDATE_IP)) {
            if (addToBlacklist($ip, $reason, $expiryHours)) {
                $message = '<div class="message success"><i class="fas fa-check-circle"></i> IP已添加到黑名单</div>';
            } else {
                $message = '<div class="message error"><i class="fas fa-exclamation-triangle"></i> 添加IP到黑名单失败</div>';
            }
        } else {
            $message = '<div class="message error"><i class="fas fa-exclamation-triangle"></i> 无效的IP地址</div>';
        }
    } elseif (isset($_POST['remove_from_blacklist'])) {
        $ip = $_POST['ip'];
        
        if (removeFromBlacklist($ip)) {
            $message = '<div class="message success"><i class="fas fa-check-circle"></i> IP已从黑名单中移除</div>';
        } else {
            $message = '<div class="message error"><i class="fas fa-exclamation-triangle"></i> 从黑名单中移除IP失败</div>';
        }
    } elseif (isset($_POST['update_security_config'])) {
        $securityConfig = [
            'max_failed_attempts' => $_POST['max_failed_attempts'],
            'lockout_time' => $_POST['lockout_time'],
            'whitelist_ips' => $_POST['whitelist_ips'],
            'secure_headers' => $_POST['secure_headers']
        ];
        
        if (updateSecurityConfig($securityConfig)) {
            $message = '<div class="message success"><i class="fas fa-check-circle"></i> 安全配置已更新</div>';
        } else {
            $message = '<div class="message error"><i class="fas fa-exclamation-triangle"></i> 更新安全配置失败</div>';
        }
    }
}

// 获取当前配置
$apiSecurityConfig = getApiSecurityConfig();
?>

<style>
.api-security-container {
    max-width: 100%;
    margin: 0;
    padding: 20px;
    box-sizing: border-box;
}

.card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.card h2 {
    color: white;
    margin-bottom: 20px;
    font-size: 18px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
}

.form-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    color: rgba(255, 255, 255, 0.8);
    font-weight: 500;
}

.form-group input[type="text"],
.form-group input[type="password"],
.form-group input[type="number"],
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 10px;
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    background: rgba(255, 255, 255, 0.05);
    color: white;
    font-size: 14px;
    transition: all 0.3s ease;
}

.form-group input[type="text"]:focus,
.form-group input[type="password"]:focus,
.form-group input[type="number"]:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: rgba(102, 126, 234, 0.5);
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.25);
}

.form-group textarea {
    min-height: 100px;
    font-family: monospace;
    font-size: 12px;
}

.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 20px;
}

.btn {
    padding: 10px 20px;
    border-radius: 8px;
    border: none;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.btn-danger {
    background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
    color: white;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.message {
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.message.success {
    background: rgba(40, 167, 69, 0.2);
    border: 1px solid rgba(40, 167, 69, 0.3);
    color: #28a745;
}

.message.error {
    background: rgba(220, 53, 69, 0.2);
    border: 1px solid rgba(220, 53, 69, 0.3);
    color: #dc3545;
}

.message i {
    font-size: 18px;
}

.section-title {
    color: white;
    margin: 30px 0 15px;
    font-size: 16px;
    font-weight: 600;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding-bottom: 10px;
}

.help-text {
    color: rgba(255, 255, 255, 0.5);
    font-size: 12px;
    margin-top: 5px;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.stat-card {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    padding: 15px;
    text-align: center;
}

.stat-value {
    font-size: 24px;
    font-weight: 700;
    color: white;
    margin-bottom: 5px;
}

.stat-label {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.7);
}

.table-container {
    overflow-x: auto;
    margin-bottom: 20px;
}

table {
    width: 100%;
    border-collapse: collapse;
    color: white;
}

table th,
table td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

table th {
    background: rgba(255, 255, 255, 0.05);
    font-weight: 600;
    color: rgba(255, 255, 255, 0.9);
}

table tr:hover {
    background: rgba(255, 255, 255, 0.03);
}

table td .btn-sm {
    padding: 5px 10px;
    font-size: 12px;
}

.badge {
    display: inline-block;
    padding: 3px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
}

.badge-danger {
    background: rgba(220, 53, 69, 0.2);
    color: #ff6b6b;
}

.badge-warning {
    background: rgba(255, 193, 7, 0.2);
    color: #ffc107;
}

.badge-info {
    background: rgba(23, 162, 184, 0.2);
    color: #17a2b8;
}

.badge-success {
    background: rgba(40, 167, 69, 0.2);
    color: #28a745;
}

.chart-container {
    height: 300px;
    margin-bottom: 20px;
}
</style>

<div class="api-security-container">
    <div class="page-header">
        <h1><i class="fas fa-shield-alt"></i> API安全配置</h1>
        <p>管理API安全设置和监控安全状态</p>
    </div>
    
    <?php echo $message; ?>
    
    <div class="card">
        <h2><i class="fas fa-chart-line"></i> 安全统计</h2>
        
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-value"><?php echo number_format($apiSecurityConfig['total_requests_24h'] ?? 0); ?></div>
                <div class="stat-label">24小时内总请求数</div>
            </div>
            
            <div class="stat-card">
                <div class="stat-value"><?php echo number_format($apiSecurityConfig['error_count_24h'] ?? 0); ?></div>
                <div class="stat-label">24小时内错误请求数</div>
            </div>
            
            <div class="stat-card">
                <div class="stat-value"><?php echo number_format($apiSecurityConfig['unique_ips_24h'] ?? 0); ?></div>
                <div class="stat-label">24小时内唯一IP数</div>
            </div>
            
            <div class="stat-card">
                <div class="stat-value"><?php echo count($apiSecurityConfig['blacklist_ips'] ?? []); ?></div>
                <div class="stat-label">黑名单IP数量</div>
            </div>
        </div>
        
        <div class="section-title">错误请求TOP5 IP地址</div>
        <div class="table-container">
            <table>
                <thead>
                    <tr>
                        <th>IP地址</th>
                        <th>错误请求数</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (empty($apiSecurityConfig['top_error_ips'])): ?>
                    <tr>
                        <td colspan="3" style="text-align: center;">没有错误请求记录</td>
                    </tr>
                    <?php else: ?>
                        <?php foreach ($apiSecurityConfig['top_error_ips'] as $ip): ?>
                        <tr>
                            <td><?php echo htmlspecialchars($ip['ip_address']); ?></td>
                            <td><?php echo number_format($ip['count']); ?></td>
                            <td>
                                <form method="POST" style="display: inline;">
                                    <input type="hidden" name="ip_address" value="<?php echo htmlspecialchars($ip['ip_address']); ?>">
                                    <input type="hidden" name="reason" value="MULTIPLE_FAILURES">
                                    <input type="hidden" name="expiry_hours" value="24">
                                    <button type="submit" name="add_to_blacklist" class="btn btn-danger btn-sm">
                                        <i class="fas fa-ban"></i> 加入黑名单
                                    </button>
                                </form>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
    
    <div class="card">
        <h2><i class="fas fa-ban"></i> IP黑名单管理</h2>
        
        <div class="section-title">当前黑名单</div>
        <div class="table-container">
            <table>
                <thead>
                    <tr>
                        <th>IP地址</th>
                        <th>原因</th>
                        <th>过期时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (empty($apiSecurityConfig['blacklist_ips'])): ?>
                    <tr>
                        <td colspan="4" style="text-align: center;">黑名单为空</td>
                    </tr>
                    <?php else: ?>
                        <?php foreach ($apiSecurityConfig['blacklist_ips'] as $ip): ?>
                        <tr>
                            <td><?php echo htmlspecialchars($ip['ip_address']); ?></td>
                            <td>
                                <?php 
                                $reasonBadge = '';
                                switch ($ip['reason']) {
                                    case 'BRUTE_FORCE':
                                        $reasonBadge = '<span class="badge badge-danger">暴力破解</span>';
                                        break;
                                    case 'GEO_ANOMALY':
                                        $reasonBadge = '<span class="badge badge-warning">地理异常</span>';
                                        break;
                                    case 'MULTIPLE_FAILURES':
                                        $reasonBadge = '<span class="badge badge-info">多次失败</span>';
                                        break;
                                    case 'ADMIN_BAN':
                                        $reasonBadge = '<span class="badge badge-danger">管理员封禁</span>';
                                        break;
                                    default:
                                        $reasonBadge = '<span class="badge badge-info">未知</span>';
                                }
                                echo $reasonBadge;
                                ?>
                            </td>
                            <td><?php echo $ip['expires_at'] ? date('Y-m-d H:i:s', strtotime($ip['expires_at'])) : '永久'; ?></td>
                            <td>
                                <form method="POST" style="display: inline;">
                                    <input type="hidden" name="ip" value="<?php echo htmlspecialchars($ip['ip_address']); ?>">
                                    <button type="submit" name="remove_from_blacklist" class="btn btn-secondary btn-sm">
                                        <i class="fas fa-trash-alt"></i> 移除
                                    </button>
                                </form>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
        
        <div class="section-title">添加IP到黑名单</div>
        <form method="POST">
            <div class="form-row">
                <div class="form-group">
                    <label>IP地址</label>
                    <input type="text" name="ip_address" placeholder="例如: ***********" required>
                </div>
                
                <div class="form-group">
                    <label>原因</label>
                    <select name="reason">
                        <option value="BRUTE_FORCE">暴力破解</option>
                        <option value="GEO_ANOMALY">地理异常</option>
                        <option value="MULTIPLE_FAILURES">多次失败</option>
                        <option value="ADMIN_BAN" selected>管理员封禁</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label>封禁时长（小时）</label>
                    <input type="number" name="expiry_hours" value="24" min="0" max="8760">
                    <div class="help-text">设置为0表示永久封禁</div>
                </div>
            </div>
            
            <div class="form-actions">
                <button type="submit" name="add_to_blacklist" class="btn btn-primary">
                    <i class="fas fa-plus"></i> 添加到黑名单
                </button>
            </div>
        </form>
    </div>
    
    <div class="card">
        <h2><i class="fas fa-cogs"></i> 安全配置</h2>
        
        <form method="POST">
            <div class="form-row">
                <div class="form-group">
                    <label>最大失败尝试次数</label>
                    <input type="number" name="max_failed_attempts" value="<?php echo htmlspecialchars($apiSecurityConfig['max_failed_attempts'] ?? '5'); ?>" min="1" max="100">
                    <div class="help-text">达到此次数后将自动封禁IP</div>
                </div>
                
                <div class="form-group">
                    <label>封禁时长（秒）</label>
                    <input type="number" name="lockout_time" value="<?php echo htmlspecialchars($apiSecurityConfig['lockout_time'] ?? '1800'); ?>" min="60" max="86400">
                    <div class="help-text">自动封禁的持续时间</div>
                </div>
            </div>
            
            <div class="form-group">
                <label>白名单IP（JSON数组）</label>
                <textarea name="whitelist_ips"><?php echo htmlspecialchars($apiSecurityConfig['whitelist_ips'] ?? '[]'); ?></textarea>
                <div class="help-text">白名单IP不受速率限制和自动封禁影响，格式: ["***********", "********"]</div>
            </div>
            
            <div class="form-group">
                <label>安全HTTP头（JSON对象）</label>
                <textarea name="secure_headers"><?php echo htmlspecialchars($apiSecurityConfig['secure_headers'] ?? '{"X-Frame-Options":"DENY","X-XSS-Protection":"1; mode=block","X-Content-Type-Options":"nosniff"}'); ?></textarea>
                <div class="help-text">API响应中包含的安全HTTP头</div>
            </div>
            
            <div class="form-actions">
                <button type="submit" name="update_security_config" class="btn btn-primary">
                    <i class="fas fa-save"></i> 保存配置
                </button>
            </div>
        </form>
    </div>
</div>

<script>
// 初始化图表（如果需要）
document.addEventListener('DOMContentLoaded', function() {
    // 可以在这里添加图表初始化代码
});
</script> 