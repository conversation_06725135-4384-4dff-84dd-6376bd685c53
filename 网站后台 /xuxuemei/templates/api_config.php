<?php
// 检查用户是否已登录
if (!isset($_SESSION['admin_user_id'])) {
    header('Location: login.php');
    exit;
}

// 加载数据库连接
require_once '../includes/db.php';
require_once '../includes/functions.php';

// 获取当前API配置
function getApiConfig() {
    global $pdo;
    $config = [];
    
    try {
        // 从系统设置表获取API配置
        $stmt = $pdo->query("SELECT setting_key, setting_value FROM system_settings WHERE setting_key LIKE 'api_%'");
        while ($row = $stmt->fetch()) {
            $key = str_replace('api_', '', $row['setting_key']);
            $config[$key] = $row['setting_value'];
        }
        
        // 从API安全配置表获取安全配置
        $stmt = $pdo->query("SELECT config_key, config_value, is_encrypted FROM api_security_config");
        while ($row = $stmt->fetch()) {
            $config[$row['config_key']] = $row['is_encrypted'] ? '******' : $row['config_value'];
        }
        
        // 获取安全统计信息
        $stmt = $pdo->query("SELECT COUNT(*) FROM api_logs WHERE response_status = 'error' AND created_at > DATE_SUB(NOW(), INTERVAL 24 HOUR)");
        $config['error_count_24h'] = $stmt->fetchColumn();
        
        $stmt = $pdo->query("SELECT COUNT(*) FROM api_logs WHERE created_at > DATE_SUB(NOW(), INTERVAL 24 HOUR)");
        $config['total_requests_24h'] = $stmt->fetchColumn();
        
        $stmt = $pdo->query("SELECT COUNT(DISTINCT ip_address) FROM api_logs WHERE created_at > DATE_SUB(NOW(), INTERVAL 24 HOUR)");
        $config['unique_ips_24h'] = $stmt->fetchColumn();
        
    } catch (PDOException $e) {
        error_log("获取API配置失败: " . $e->getMessage());
    }
    
    return $config;
}

// 保存API配置
function saveApiConfig($data) {
    global $pdo;
    $success = true;
    
    try {
        $pdo->beginTransaction();
        
        // 保存系统设置
        foreach ($data as $key => $value) {
            if (strpos($key, 'system_') === 0) {
                $settingKey = 'api_' . substr($key, 7); // 移除前缀 'system_'
                $stmt = $pdo->prepare("
                    INSERT INTO system_settings (setting_key, setting_value) 
                    VALUES (?, ?) 
                    ON DUPLICATE KEY UPDATE setting_value = ?
                ");
                $stmt->execute([$settingKey, $value, $value]);
            }
        }
        
        // 保存安全配置
        foreach ($data as $key => $value) {
            if (strpos($key, 'security_') === 0) {
                $configKey = substr($key, 9); // 移除前缀 'security_'
                
                // 如果是加密字段且值为******，则不更新
                if ($value === '******') {
                    continue;
                }
                
                $stmt = $pdo->prepare("
                    INSERT INTO api_security_config (config_key, config_value, is_encrypted) 
                    VALUES (?, ?, ?) 
                    ON DUPLICATE KEY UPDATE config_value = ?, is_encrypted = ?
                ");
                
                // 判断是否需要加密
                $isEncrypted = in_array($configKey, ['encryption_key', 'signature_salt', 'token_secret']) ? 1 : 0;
                
                $stmt->execute([$configKey, $value, $isEncrypted, $value, $isEncrypted]);
            }
        }
        
        $pdo->commit();
    } catch (PDOException $e) {
        $pdo->rollBack();
        error_log("保存API配置失败: " . $e->getMessage());
        $success = false;
    }
    
    return $success;
}

// 生成新的密钥
function generateNewKey() {
    return bin2hex(random_bytes(16));
}

// 处理表单提交
$message = '';
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['save_api_config'])) {
        if (saveApiConfig($_POST)) {
            $message = '<div class="message success"><i class="fas fa-check-circle"></i> API配置已保存</div>';
        } else {
            $message = '<div class="message error"><i class="fas fa-exclamation-triangle"></i> 保存API配置失败</div>';
        }
    } elseif (isset($_POST['generate_new_keys'])) {
        // 生成新的密钥
        $newApiKey = generateNewKey();
        $newEncryptionKey = generateNewKey();
        $newSignatureSalt = generateNewKey();
        $newTokenSecret = generateNewKey();
        
        // 保存新密钥
        $data = [
            'system_secret_key' => $newApiKey,
            'security_encryption_key' => $newEncryptionKey,
            'security_signature_salt' => $newSignatureSalt,
            'security_token_secret' => $newTokenSecret
        ];
        
        if (saveApiConfig($data)) {
            $message = '<div class="message success"><i class="fas fa-check-circle"></i> 新密钥已生成并保存</div>';
        } else {
            $message = '<div class="message error"><i class="fas fa-exclamation-triangle"></i> 生成新密钥失败</div>';
        }
    } elseif (isset($_POST['add_blacklist_ip'])) {
        // 添加IP到黑名单
        $ip = trim($_POST['blacklist_ip']);
        $reason = $_POST['blacklist_reason'];
        
        if (!empty($ip)) {
            $stmt = $pdo->prepare("INSERT INTO api_blacklist (ip_address, fingerprint, reason) VALUES (?, ?, ?)");
            if ($stmt->execute([$ip, md5($ip), $reason])) {
                $message = '<div class="message success"><i class="fas fa-check-circle"></i> IP已添加到黑名单</div>';
            } else {
                $message = '<div class="message error"><i class="fas fa-exclamation-triangle"></i> 添加IP到黑名单失败</div>';
            }
        }
    }
}

// 获取当前配置
$apiConfig = getApiConfig();

// 获取黑名单IP
try {
    $stmt = $pdo->query("SELECT ip_address, reason, created_at FROM api_blacklist ORDER BY created_at DESC LIMIT 10");
    $blacklistIps = $stmt->fetchAll();
} catch (PDOException $e) {
    $blacklistIps = [];
}

// 获取最近的API日志
try {
    $stmt = $pdo->query("SELECT endpoint, method, ip_address, status_code, created_at FROM api_logs ORDER BY created_at DESC LIMIT 20");
    $recentLogs = $stmt->fetchAll();
} catch (PDOException $e) {
    $recentLogs = [];
}
?>

<style>
/* API配置页面专用样式 - 横向标签页布局 */
.api-config-container {
    max-width: 100%;
    margin: 0;
    padding: 20px;
    box-sizing: border-box;
}

.card {
    background: rgba(255, 255, 255, 0.1) !important;
    backdrop-filter: blur(20px) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    border-radius: 15px !important;
    padding: 25px !important;
    margin-bottom: 25px !important;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1) !important;
}

.card h2 {
    color: white !important;
    margin-bottom: 25px !important;
    font-size: 18px !important;
    font-weight: 600 !important;
    display: flex !important;
    align-items: center !important;
    gap: 12px !important;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2) !important;
    padding-bottom: 15px !important;
}

.page-header {
    text-align: center !important;
    margin-bottom: 30px !important;
}

.page-header h1 {
    color: white !important;
    font-size: 24px !important;
    margin-bottom: 10px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    gap: 15px !important;
}

.page-header p {
    color: rgba(255, 255, 255, 0.7) !important;
    font-size: 14px !important;
    margin: 0 !important;
}

/* 横向标签页布局 */
.tabs-container {
    margin-bottom: 30px !important;
}

.tabs-nav {
    display: flex !important;
    background: rgba(255, 255, 255, 0.05) !important;
    border-radius: 12px !important;
    padding: 5px !important;
    margin-bottom: 25px !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
    overflow-x: auto !important;
}

.tab-button {
    flex: 1 !important;
    padding: 12px 20px !important;
    background: transparent !important;
    border: none !important;
    color: rgba(255, 255, 255, 0.7) !important;
    font-size: 14px !important;
    font-weight: 500 !important;
    border-radius: 8px !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    gap: 8px !important;
    white-space: nowrap !important;
}

.tab-button:hover {
    background: rgba(255, 255, 255, 0.1) !important;
    color: white !important;
}

.tab-button.active {
    background: linear-gradient(135deg, #ff6b9d, #c44569) !important;
    color: white !important;
    box-shadow: 0 4px 12px rgba(255, 107, 157, 0.3) !important;
}

.tab-content {
    display: none !important;
}

.tab-content.active {
    display: block !important;
    animation: fadeIn 0.3s ease !important;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* 配置网格布局 */
.config-grid {
    display: grid !important;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr)) !important;
    gap: 25px !important;
    margin-bottom: 30px !important;
}

.config-section {
    background: rgba(255, 255, 255, 0.05) !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
    border-radius: 12px !important;
    padding: 20px !important;
}

.config-section h3 {
    color: white !important;
    font-size: 16px !important;
    font-weight: 600 !important;
    margin-bottom: 20px !important;
    display: flex !important;
    align-items: center !important;
    gap: 10px !important;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
    padding-bottom: 10px !important;
}

.form-group {
    margin-bottom: 20px !important;
}

.form-group label {
    display: block !important;
    color: white !important;
    margin-bottom: 8px !important;
    font-weight: 500 !important;
    font-size: 14px !important;
}

.form-group input[type="text"],
.form-group input[type="password"],
.form-group input[type="number"],
.form-group select,
.form-group textarea {
    width: 100% !important;
    padding: 12px 15px !important;
    background: rgba(255, 255, 255, 0.1) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    border-radius: 8px !important;
    color: white !important;
    font-size: 14px !important;
    transition: all 0.3s ease !important;
}

.form-group input::placeholder,
.form-group textarea::placeholder {
    color: rgba(255, 255, 255, 0.5) !important;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
    outline: none !important;
    border-color: #ff6b9d !important;
    box-shadow: 0 0 0 2px rgba(255, 107, 157, 0.2) !important;
}

.btn {
    display: inline-flex !important;
    align-items: center !important;
    gap: 8px !important;
    padding: 12px 24px !important;
    border: none !important;
    border-radius: 8px !important;
    font-size: 14px !important;
    font-weight: 500 !important;
    text-decoration: none !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
}

.btn-primary {
    background: linear-gradient(135deg, #ff6b9d, #c44569) !important;
    color: white !important;
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.2) !important;
    color: white !important;
}

.btn-danger {
    background: linear-gradient(135deg, #f87171, #ef4444) !important;
    color: white !important;
}

.btn:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2) !important;
}

.message {
    padding: 15px 20px !important;
    border-radius: 8px !important;
    margin-bottom: 20px !important;
    display: flex !important;
    align-items: center !important;
    gap: 10px !important;
    font-size: 14px !important;
    font-weight: 500 !important;
}

.message.success {
    background: rgba(34, 197, 94, 0.2) !important;
    border: 1px solid rgba(34, 197, 94, 0.3) !important;
    color: #4ade80 !important;
}

.message.error {
    background: rgba(239, 68, 68, 0.2) !important;
    border: 1px solid rgba(239, 68, 68, 0.3) !important;
    color: #f87171 !important;
}

.api-status {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    gap: 15px !important;
    margin-bottom: 30px !important;
    padding: 20px !important;
    border-radius: 12px !important;
    background: rgba(255, 255, 255, 0.05) !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
}

.status-indicator {
    width: 14px !important;
    height: 14px !important;
    border-radius: 50% !important;
    animation: pulse 2s infinite !important;
}

.status-indicator.online {
    background: #4ade80 !important;
    box-shadow: 0 0 15px rgba(74, 222, 128, 0.5) !important;
}

.status-indicator.offline {
    background: #f87171 !important;
    box-shadow: 0 0 15px rgba(248, 113, 113, 0.5) !important;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.status-text {
    color: white !important;
    font-weight: 500 !important;
    font-size: 16px !important;
}

.help-text {
    color: rgba(255, 255, 255, 0.6) !important;
    font-size: 12px !important;
    margin-top: 5px !important;
    line-height: 1.4 !important;
}

.toggle-switch {
    position: relative !important;
    display: inline-block !important;
    width: 54px !important;
    height: 28px !important;
}

.toggle-switch input {
    opacity: 0 !important;
    width: 0 !important;
    height: 0 !important;
}

.toggle-slider {
    position: absolute !important;
    cursor: pointer !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    background-color: rgba(255, 255, 255, 0.2) !important;
    transition: .4s !important;
    border-radius: 28px !important;
}

.toggle-slider:before {
    position: absolute !important;
    content: "" !important;
    height: 20px !important;
    width: 20px !important;
    left: 4px !important;
    bottom: 4px !important;
    background-color: white !important;
    transition: .4s !important;
    border-radius: 50% !important;
}

input:checked + .toggle-slider {
    background-color: #ff6b9d !important;
}

input:checked + .toggle-slider:before {
    transform: translateX(26px) !important;
}

.toggle-label {
    display: flex !important;
    align-items: center !important;
    justify-content: space-between !important;
    gap: 15px !important;
}

.toggle-text {
    color: white !important;
    font-size: 14px !important;
    flex: 1 !important;
}

.stats-grid {
    display: grid !important;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)) !important;
    gap: 20px !important;
    margin-bottom: 30px !important;
}

.stat-card {
    background: rgba(255, 255, 255, 0.05) !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
    border-radius: 12px !important;
    padding: 20px !important;
    text-align: center !important;
}

.stat-value {
    font-size: 32px !important;
    font-weight: 700 !important;
    color: #ff6b9d !important;
    margin-bottom: 8px !important;
}

.stat-label {
    color: rgba(255, 255, 255, 0.8) !important;
    font-size: 14px !important;
    font-weight: 500 !important;
}

.table-container {
    overflow-x: auto !important;
    border-radius: 8px !important;
    background: rgba(255, 255, 255, 0.05) !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
}

table {
    width: 100% !important;
    border-collapse: collapse !important;
    font-size: 14px !important;
}

table th {
    background: rgba(255, 255, 255, 0.1) !important;
    color: white !important;
    padding: 15px 12px !important;
    text-align: left !important;
    font-weight: 600 !important;
    font-size: 14px !important;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2) !important;
    white-space: nowrap !important;
}

table td {
    padding: 15px 12px !important;
    color: rgba(255, 255, 255, 0.9) !important;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
    vertical-align: middle !important;
    font-size: 13px !important;
}

table tr:hover {
    background: rgba(255, 255, 255, 0.05) !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .config-grid {
        grid-template-columns: 1fr !important;
    }
    
    .tabs-nav {
        flex-direction: column !important;
    }
    
    .tab-button {
        flex: none !important;
    }
    
    .stats-grid {
        grid-template-columns: repeat(2, 1fr) !important;
    }
}
</style>

<div class="api-config-container">
    <div class="page-header">
        <h1><i class="fas fa-cloud"></i> API配置中心</h1>
        <p>统一管理API接口配置、安全设置和监控信息</p>
    </div>
    
    <?php echo $message; ?>
    
    <div class="api-status">
        <div class="status-indicator <?php echo isset($apiConfig['version']) ? 'online' : 'offline'; ?>"></div>
        <div class="status-text">
            API服务状态: <?php echo isset($apiConfig['version']) ? '在线运行 (版本 ' . $apiConfig['version'] . ')' : '离线状态'; ?>
        </div>
    </div>
    
    <!-- 横向标签页导航 -->
    <div class="tabs-container">
        <div class="tabs-nav">
            <button class="tab-button active" onclick="switchTab('basic')">
                <i class="fas fa-cogs"></i> 基本配置
            </button>
            <button class="tab-button" onclick="switchTab('security')">
                <i class="fas fa-shield-alt"></i> 安全配置
            </button>
            <button class="tab-button" onclick="switchTab('monitor')">
                <i class="fas fa-chart-line"></i> 监控面板
            </button>
        </div>
        
        <!-- 基本配置标签页 -->
        <div id="basic-tab" class="tab-content active">
            <form method="POST" action="">
                <div class="card">
                    <h2><i class="fas fa-server"></i> 服务器配置</h2>
                    
                    <div class="config-grid">
                        <div class="config-section">
                            <h3><i class="fas fa-globe"></i> 服务器地址</h3>
                            <div class="form-group">
                                <label>主服务器地址</label>
                                <input type="text" name="security_primary_server" value="<?php echo htmlspecialchars($apiConfig['primary_server'] ?? 'https://xiaomeihuakefu.cn'); ?>" placeholder="https://example.com">
                                <div class="help-text">用于处理API请求的主服务器地址</div>
                            </div>
                            
                            <div class="form-group">
                                <label>备用服务器地址</label>
                                <input type="text" name="security_backup_server" value="<?php echo htmlspecialchars($apiConfig['backup_server'] ?? 'https://api.xiaomeihuakefu.cn'); ?>" placeholder="https://api.example.com">
                                <div class="help-text">当主服务器不可用时使用的备用服务器</div>
                            </div>
                            
                            <div class="form-group">
                                <label>安全服务器地址</label>
                                <input type="text" name="security_secure_server" value="<?php echo htmlspecialchars($apiConfig['secure_server'] ?? 'https://secure.xiaomeihuakefu.cn'); ?>" placeholder="https://secure.example.com">
                                <div class="help-text">用于加密通信的安全服务器</div>
                            </div>
                        </div>
                        
                        <div class="config-section">
                            <h3><i class="fas fa-cogs"></i> 基本设置</h3>
                            <div class="form-group">
                                <label>API版本</label>
                                <input type="text" name="system_version" value="<?php echo htmlspecialchars($apiConfig['version'] ?? 'v2'); ?>" placeholder="v2">
                                <div class="help-text">当前API版本号</div>
                            </div>
                            
                            <div class="form-group">
                                <label>调试模式</label>
                                <div class="toggle-label">
                                    <span class="toggle-text">启用详细错误信息和日志记录</span>
                                    <label class="toggle-switch">
                                        <input type="checkbox" name="system_debug_mode" value="true" <?php echo ($apiConfig['debug_mode'] ?? 'false') === 'true' ? 'checked' : ''; ?>>
                                        <span class="toggle-slider"></span>
                                    </label>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label>请求日志</label>
                                <div class="toggle-label">
                                    <span class="toggle-text">记录所有API请求和响应</span>
                                    <label class="toggle-switch">
                                        <input type="checkbox" name="system_log_requests" value="true" <?php echo ($apiConfig['log_requests'] ?? 'true') === 'true' ? 'checked' : ''; ?>>
                                        <span class="toggle-slider"></span>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div style="text-align: center; margin-top: 30px;">
                        <button type="submit" name="save_api_config" class="btn btn-primary">
                            <i class="fas fa-save"></i> 保存基本配置
                        </button>
                    </div>
                </div>
            </form>
        </div>
        
        <!-- 安全配置标签页 -->
        <div id="security-tab" class="tab-content">
            <form method="POST" action="">
                <div class="card">
                    <h2><i class="fas fa-shield-alt"></i> 安全配置</h2>
                    
                    <div class="config-grid">
                        <div class="config-section">
                            <h3><i class="fas fa-key"></i> API密钥管理</h3>
                            <div class="form-group">
                                <label>API密钥</label>
                                <input type="text" name="system_secret_key" value="<?php echo htmlspecialchars($apiConfig['secret_key'] ?? generateNewKey()); ?>" readonly>
                                <div class="help-text">用于API签名验证的主密钥</div>
                            </div>
                            
                            <div class="form-group">
                                <label>数据加密密钥</label>
                                <input type="text" name="security_encryption_key" value="<?php echo htmlspecialchars($apiConfig['encryption_key'] ?? '******'); ?>" readonly>
                                <div class="help-text">用于加密API数据传输的密钥</div>
                            </div>
                            
                            <div class="form-group">
                                <label>签名盐值</label>
                                <input type="text" name="security_signature_salt" value="<?php echo htmlspecialchars($apiConfig['signature_salt'] ?? '******'); ?>" readonly>
                                <div class="help-text">用于增强签名安全性的盐值</div>
                            </div>
                            
                            <div style="text-align: center; margin-top: 20px;">
                                <button type="submit" name="generate_new_keys" class="btn btn-danger">
                                    <i class="fas fa-sync-alt"></i> 重新生成密钥
                                </button>
                                <div class="help-text" style="margin-top: 10px;">
                                    <i class="fas fa-exclamation-triangle"></i> 警告：生成新密钥将使所有现有的API令牌失效
                                </div>
                            </div>
                        </div>
                        
                        <div class="config-section">
                            <h3><i class="fas fa-check-circle"></i> 验证设置</h3>
                            <div class="form-group">
                                <label>签名验证</label>
                                <div class="toggle-label">
                                    <span class="toggle-text">要求请求包含有效的数字签名</span>
                                    <label class="toggle-switch">
                                        <input type="checkbox" name="system_enable_signature_validation" value="true" <?php echo ($apiConfig['enable_signature_validation'] ?? 'true') === 'true' ? 'checked' : ''; ?>>
                                        <span class="toggle-slider"></span>
                                    </label>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label>时间戳验证</label>
                                <div class="toggle-label">
                                    <span class="toggle-text">验证请求时间戳是否在有效期内</span>
                                    <label class="toggle-switch">
                                        <input type="checkbox" name="system_enable_timestamp_validation" value="true" <?php echo ($apiConfig['enable_timestamp_validation'] ?? 'true') === 'true' ? 'checked' : ''; ?>>
                                        <span class="toggle-slider"></span>
                                    </label>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label>速率限制</label>
                                <div class="toggle-label">
                                    <span class="toggle-text">限制每个IP的请求频率</span>
                                    <label class="toggle-switch">
                                        <input type="checkbox" name="system_rate_limit_enabled" value="true" <?php echo ($apiConfig['rate_limit_enabled'] ?? 'true') === 'true' ? 'checked' : ''; ?>>
                                        <span class="toggle-slider"></span>
                                    </label>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label>每分钟最大请求数</label>
                                <input type="number" name="system_rate_limit_requests" value="<?php echo htmlspecialchars($apiConfig['rate_limit_requests'] ?? '60'); ?>" min="10" max="1000">
                                <div class="help-text">每个IP每分钟允许的最大请求数</div>
                            </div>
                        </div>
                    </div>
                    
                    <div style="text-align: center; margin-top: 30px;">
                        <button type="submit" name="save_api_config" class="btn btn-primary">
                            <i class="fas fa-save"></i> 保存安全配置
                        </button>
                    </div>
                </div>
                
                <!-- IP黑名单管理 -->
                <div class="card">
                    <h2><i class="fas fa-ban"></i> IP黑名单管理</h2>
                    
                    <div class="config-grid">
                        <div class="config-section">
                            <h3><i class="fas fa-plus"></i> 添加黑名单IP</h3>
                            <div class="form-group">
                                <label>IP地址</label>
                                <input type="text" name="blacklist_ip" placeholder="***********">
                            </div>
                            <div class="form-group">
                                <label>封禁原因</label>
                                <select name="blacklist_reason">
                                    <option value="BRUTE_FORCE">暴力破解</option>
                                    <option value="MULTIPLE_FAILURES">多次失败</option>
                                    <option value="ADMIN_BAN">管理员封禁</option>
                                </select>
                            </div>
                            <button type="submit" name="add_blacklist_ip" class="btn btn-danger">
                                <i class="fas fa-ban"></i> 添加到黑名单
                            </button>
                        </div>
                        
                        <div class="config-section">
                            <h3><i class="fas fa-list"></i> 当前黑名单</h3>
                            <?php if (!empty($blacklistIps)): ?>
                                <div class="table-container">
                                    <table>
                                        <thead>
                                            <tr>
                                                <th>IP地址</th>
                                                <th>原因</th>
                                                <th>添加时间</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($blacklistIps as $ip): ?>
                                            <tr>
                                                <td><?php echo htmlspecialchars($ip['ip_address']); ?></td>
                                                <td><?php echo htmlspecialchars($ip['reason']); ?></td>
                                                <td><?php echo date('Y-m-d H:i', strtotime($ip['created_at'])); ?></td>
                                            </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php else: ?>
                                <p style="color: rgba(255,255,255,0.7); text-align: center; padding: 20px;">
                                    <i class="fas fa-info-circle"></i> 暂无黑名单IP
                                </p>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </form>
        </div>
        
        <!-- 监控面板标签页 -->
        <div id="monitor-tab" class="tab-content">
            <div class="card">
                <h2><i class="fas fa-chart-line"></i> API监控面板</h2>
                
                <!-- 统计数据 -->
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-value"><?php echo number_format($apiConfig['total_requests_24h'] ?? 0); ?></div>
                        <div class="stat-label">24小时请求总数</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value"><?php echo number_format($apiConfig['error_count_24h'] ?? 0); ?></div>
                        <div class="stat-label">24小时错误数</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value"><?php echo number_format($apiConfig['unique_ips_24h'] ?? 0); ?></div>
                        <div class="stat-label">24小时独立IP</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value"><?php echo count($blacklistIps); ?></div>
                        <div class="stat-label">黑名单IP数量</div>
                    </div>
                </div>
                
                <!-- 最近的API日志 -->
                <div class="config-section">
                    <h3><i class="fas fa-list"></i> 最近API请求日志</h3>
                    <?php if (!empty($recentLogs)): ?>
                        <div class="table-container">
                            <table>
                                <thead>
                                    <tr>
                                        <th>端点</th>
                                        <th>方法</th>
                                        <th>IP地址</th>
                                        <th>状态码</th>
                                        <th>时间</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($recentLogs as $log): ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($log['endpoint']); ?></td>
                                        <td><?php echo htmlspecialchars($log['method']); ?></td>
                                        <td><?php echo htmlspecialchars($log['ip_address']); ?></td>
                                        <td>
                                            <span style="color: <?php echo $log['status_code'] >= 400 ? '#f87171' : '#4ade80'; ?>">
                                                <?php echo $log['status_code']; ?>
                                            </span>
                                        </td>
                                        <td><?php echo date('m-d H:i', strtotime($log['created_at'])); ?></td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <p style="color: rgba(255,255,255,0.7); text-align: center; padding: 40px;">
                            <i class="fas fa-info-circle"></i> 暂无API请求日志
                        </p>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// 标签页切换功能
function switchTab(tabName) {
    // 隐藏所有标签页内容
    const tabContents = document.querySelectorAll('.tab-content');
    tabContents.forEach(content => {
        content.classList.remove('active');
    });
    
    // 移除所有标签按钮的激活状态
    const tabButtons = document.querySelectorAll('.tab-button');
    tabButtons.forEach(button => {
        button.classList.remove('active');
    });
    
    // 显示选中的标签页内容
    const selectedTab = document.getElementById(tabName + '-tab');
    if (selectedTab) {
        selectedTab.classList.add('active');
    }
    
    // 激活选中的标签按钮
    const selectedButton = event.target.closest('.tab-button');
    if (selectedButton) {
        selectedButton.classList.add('active');
    }
}

// 复制文本到剪贴板
function copyToClipboard(text) {
    if (navigator.clipboard && navigator.clipboard.writeText) {
        navigator.clipboard.writeText(text).then(function() {
            showToast('已复制到剪贴板', 'success');
        }).catch(function(err) {
            fallbackCopyTextToClipboard(text);
        });
    } else {
        fallbackCopyTextToClipboard(text);
    }
}

// 备用复制方法
function fallbackCopyTextToClipboard(text) {
    const textarea = document.createElement('textarea');
    textarea.value = text;
    textarea.style.position = 'fixed';
    textarea.style.opacity = '0';
    document.body.appendChild(textarea);
    textarea.select();
    
    try {
        document.execCommand('copy');
        showToast('已复制到剪贴板', 'success');
    } catch (err) {
        showToast('复制失败，请手动复制', 'error');
    }
    
    document.body.removeChild(textarea);
}

// 显示提示消息
function showToast(message, type = 'info') {
    const toast = document.createElement('div');
    
    let backgroundColor, borderColor, icon;
    switch(type) {
        case 'success':
            backgroundColor = 'rgba(34, 197, 94, 0.9)';
            borderColor = '#22c55e';
            icon = 'fas fa-check-circle';
            break;
        case 'error':
            backgroundColor = 'rgba(239, 68, 68, 0.9)';
            borderColor = '#ef4444';
            icon = 'fas fa-exclamation-triangle';
            break;
        case 'warning':
            backgroundColor = 'rgba(245, 158, 11, 0.9)';
            borderColor = '#f59e0b';
            icon = 'fas fa-exclamation-triangle';
            break;
        default:
            backgroundColor = 'rgba(59, 130, 246, 0.9)';
            borderColor = '#3b82f6';
            icon = 'fas fa-info-circle';
    }
    
    toast.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${backgroundColor};
        color: white;
        padding: 15px 20px;
        border-radius: 8px;
        border: 1px solid ${borderColor};
        z-index: 10000;
        font-size: 14px;
        backdrop-filter: blur(10px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        animation: slideInRight 0.3s ease;
        display: flex;
        align-items: center;
        gap: 10px;
    `;
    
    toast.innerHTML = `<i class="${icon}"></i> ${message}`;
    document.body.appendChild(toast);
    
    setTimeout(() => {
        toast.style.animation = 'slideOutRight 0.3s ease';
        setTimeout(() => {
            if (toast.parentNode) {
                toast.remove();
            }
        }, 300);
    }, 3000);
}

// 添加动画CSS
if (!document.getElementById('toast-animations')) {
    const style = document.createElement('style');
    style.id = 'toast-animations';
    style.textContent = `
        @keyframes slideInRight {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }
        @keyframes slideOutRight {
            from {
                transform: translateX(0);
                opacity: 1;
            }
            to {
                transform: translateX(100%);
                opacity: 0;
            }
        }
    `;
    document.head.appendChild(style);
}
</script> 