<?php
// 检查用户是否已登录
if (!isset($_SESSION['admin_user_id'])) {
    header('Location: login.php');
    exit;
}

// 加载数据库连接
require_once '../includes/db.php';
require_once '../includes/functions.php';

// 获取API统计数据
function getApiStats($period = '24h') {
    global $pdo;
    $stats = [];
    
    try {
        // 根据时间段设置查询条件
        switch ($period) {
            case '7d':
                $timeCondition = "created_at > DATE_SUB(NOW(), INTERVAL 7 DAY)";
                $groupFormat = "%Y-%m-%d";
                break;
            case '30d':
                $timeCondition = "created_at > DATE_SUB(NOW(), INTERVAL 30 DAY)";
                $groupFormat = "%Y-%m-%d";
                break;
            case '24h':
            default:
                $timeCondition = "created_at > DATE_SUB(NOW(), INTERVAL 24 HOUR)";
                $groupFormat = "%Y-%m-%d %H:00:00";
                break;
        }
        
        // 获取总请求数
        $stmt = $pdo->query("SELECT COUNT(*) FROM api_logs WHERE $timeCondition");
        $stats['total_requests'] = $stmt->fetchColumn();
        
        // 获取成功请求数
        $stmt = $pdo->query("SELECT COUNT(*) FROM api_logs WHERE response_status = 'success' AND $timeCondition");
        $stats['success_requests'] = $stmt->fetchColumn();
        
        // 获取错误请求数
        $stmt = $pdo->query("SELECT COUNT(*) FROM api_logs WHERE response_status = 'error' AND $timeCondition");
        $stats['error_requests'] = $stmt->fetchColumn();
        
        // 获取唯一IP数
        $stmt = $pdo->query("SELECT COUNT(DISTINCT ip_address) FROM api_logs WHERE $timeCondition");
        $stats['unique_ips'] = $stmt->fetchColumn();
        
        // 获取请求趋势数据
        $stmt = $pdo->query("
            SELECT 
                DATE_FORMAT(created_at, '$groupFormat') as time_period,
                COUNT(*) as total,
                SUM(CASE WHEN response_status = 'success' THEN 1 ELSE 0 END) as success,
                SUM(CASE WHEN response_status = 'error' THEN 1 ELSE 0 END) as error
            FROM api_logs 
            WHERE $timeCondition
            GROUP BY time_period
            ORDER BY time_period ASC
        ");
        $stats['trends'] = $stmt->fetchAll();
        
        // 获取端点使用统计
        $stmt = $pdo->query("
            SELECT 
                endpoint,
                COUNT(*) as count,
                SUM(CASE WHEN response_status = 'success' THEN 1 ELSE 0 END) as success,
                SUM(CASE WHEN response_status = 'error' THEN 1 ELSE 0 END) as error
            FROM api_logs 
            WHERE $timeCondition
            GROUP BY endpoint
            ORDER BY count DESC
            LIMIT 10
        ");
        $stats['endpoints'] = $stmt->fetchAll();
        
        // 获取错误消息统计
        $stmt = $pdo->query("
            SELECT 
                message,
                COUNT(*) as count
            FROM api_logs 
            WHERE response_status = 'error' AND $timeCondition
            GROUP BY message
            ORDER BY count DESC
            LIMIT 10
        ");
        $stats['errors'] = $stmt->fetchAll();
        
        // 获取IP地址统计
        $stmt = $pdo->query("
            SELECT 
                ip_address,
                COUNT(*) as count,
                SUM(CASE WHEN response_status = 'success' THEN 1 ELSE 0 END) as success,
                SUM(CASE WHEN response_status = 'error' THEN 1 ELSE 0 END) as error
            FROM api_logs 
            WHERE $timeCondition
            GROUP BY ip_address
            ORDER BY count DESC
            LIMIT 10
        ");
        $stats['ips'] = $stmt->fetchAll();
        
        // 获取最近的API日志
        $stmt = $pdo->query("
            SELECT 
                id,
                endpoint,
                ip_address,
                response_status,
                message,
                created_at
            FROM api_logs 
            WHERE $timeCondition
            ORDER BY created_at DESC
            LIMIT 20
        ");
        $stats['recent_logs'] = $stmt->fetchAll();
        
    } catch (PDOException $e) {
        error_log("获取API统计数据失败: " . $e->getMessage());
    }
    
    return $stats;
}

// 获取卡密使用统计
function getKeyUsageStats($period = '24h') {
    global $pdo;
    $stats = [];
    
    try {
        // 根据时间段设置查询条件
        switch ($period) {
            case '7d':
                $timeCondition = "created_at > DATE_SUB(NOW(), INTERVAL 7 DAY)";
                break;
            case '30d':
                $timeCondition = "created_at > DATE_SUB(NOW(), INTERVAL 30 DAY)";
                break;
            case '24h':
            default:
                $timeCondition = "created_at > DATE_SUB(NOW(), INTERVAL 24 HOUR)";
                break;
        }
        
        // 获取卡密验证次数
        $stmt = $pdo->query("SELECT COUNT(*) FROM key_usage_logs WHERE $timeCondition");
        $stats['total_verifications'] = $stmt->fetchColumn();
        
        // 获取卡密状态检查次数
        $stmt = $pdo->query("SELECT COUNT(*) FROM key_status_checks WHERE $timeCondition");
        $stats['total_status_checks'] = $stmt->fetchColumn();
        
        // 获取卡密心跳次数
        $stmt = $pdo->query("SELECT COUNT(*) FROM heartbeat_logs WHERE $timeCondition");
        $stats['total_heartbeats'] = $stmt->fetchColumn();
        
        // 获取活跃卡密数量
        $stmt = $pdo->query("
            SELECT COUNT(DISTINCT key_id) 
            FROM (
                SELECT key_id FROM key_usage_logs WHERE $timeCondition
                UNION
                SELECT key_id FROM key_status_checks WHERE $timeCondition
                UNION
                SELECT key_id FROM heartbeat_logs WHERE $timeCondition
            ) as active_keys
        ");
        $stats['active_keys'] = $stmt->fetchColumn();
        
        // 获取最活跃的卡密
        $stmt = $pdo->query("
            SELECT 
                lk.id,
                lk.key_value,
                lk.type,
                lk.status,
                lk.expiry_date,
                COUNT(kul.id) as usage_count
            FROM license_keys lk
            JOIN key_usage_logs kul ON lk.id = kul.key_id
            WHERE kul.$timeCondition
            GROUP BY lk.id
            ORDER BY usage_count DESC
            LIMIT 10
        ");
        $stats['top_keys'] = $stmt->fetchAll();
        
    } catch (PDOException $e) {
        error_log("获取卡密使用统计失败: " . $e->getMessage());
    }
    
    return $stats;
}

// 获取选定的时间段
$period = isset($_GET['period']) ? $_GET['period'] : '24h';
$validPeriods = ['24h', '7d', '30d'];
if (!in_array($period, $validPeriods)) {
    $period = '24h';
}

// 获取统计数据
$apiStats = getApiStats($period);
$keyUsageStats = getKeyUsageStats($period);

// 格式化时间段标签
$periodLabel = '';
switch ($period) {
    case '7d':
        $periodLabel = '过去7天';
        break;
    case '30d':
        $periodLabel = '过去30天';
        break;
    case '24h':
    default:
        $periodLabel = '过去24小时';
        break;
}
?>

<style>
.api-monitor-container {
    max-width: 100%;
    margin: 0;
    padding: 20px;
    box-sizing: border-box;
}

.card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.card h2 {
    color: white;
    margin-bottom: 20px;
    font-size: 18px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.stat-card {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    padding: 15px;
    text-align: center;
}

.stat-value {
    font-size: 24px;
    font-weight: 700;
    color: white;
    margin-bottom: 5px;
}

.stat-label {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.7);
}

.section-title {
    color: white;
    margin: 30px 0 15px;
    font-size: 16px;
    font-weight: 600;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding-bottom: 10px;
}

.chart-container {
    height: 300px;
    margin-bottom: 20px;
}

.table-container {
    overflow-x: auto;
    margin-bottom: 20px;
}

table {
    width: 100%;
    border-collapse: collapse;
    color: white;
}

table th,
table td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

table th {
    background: rgba(255, 255, 255, 0.05);
    font-weight: 600;
    color: rgba(255, 255, 255, 0.9);
}

table tr:hover {
    background: rgba(255, 255, 255, 0.03);
}

.badge {
    display: inline-block;
    padding: 3px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
}

.badge-success {
    background: rgba(40, 167, 69, 0.2);
    color: #28a745;
}

.badge-danger {
    background: rgba(220, 53, 69, 0.2);
    color: #ff6b6b;
}

.period-selector {
    display: flex;
    justify-content: flex-end;
    margin-bottom: 20px;
}

.period-btn {
    padding: 8px 15px;
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.2);
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-left: 10px;
}

.period-btn:hover {
    background: rgba(255, 255, 255, 0.2);
}

.period-btn.active {
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-color: transparent;
}

.progress-container {
    width: 100%;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 5px;
    height: 8px;
    margin-top: 5px;
}

.progress-bar {
    height: 100%;
    border-radius: 5px;
    background: linear-gradient(90deg, #667eea, #764ba2);
}

.progress-bar.success {
    background: linear-gradient(90deg, #28a745, #20c997);
}

.progress-bar.error {
    background: linear-gradient(90deg, #dc3545, #ff6b6b);
}

.progress-label {
    display: flex;
    justify-content: space-between;
    color: rgba(255, 255, 255, 0.7);
    font-size: 12px;
    margin-top: 5px;
}

.two-column {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 20px;
}

@media (max-width: 768px) {
    .two-column {
        grid-template-columns: 1fr;
    }
}
</style>

<div class="api-monitor-container">
    <div class="page-header">
        <h1><i class="fas fa-chart-line"></i> API监控</h1>
        <p>监控API接口使用情况和性能指标</p>
    </div>
    
    <div class="period-selector">
        <a href="?page=api_monitor&period=24h" class="period-btn <?php echo $period === '24h' ? 'active' : ''; ?>">24小时</a>
        <a href="?page=api_monitor&period=7d" class="period-btn <?php echo $period === '7d' ? 'active' : ''; ?>">7天</a>
        <a href="?page=api_monitor&period=30d" class="period-btn <?php echo $period === '30d' ? 'active' : ''; ?>">30天</a>
    </div>
    
    <div class="card">
        <h2><i class="fas fa-tachometer-alt"></i> API概览 <span style="font-size: 14px; opacity: 0.7;"><?php echo $periodLabel; ?></span></h2>
        
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-value"><?php echo number_format($apiStats['total_requests'] ?? 0); ?></div>
                <div class="stat-label">总请求数</div>
            </div>
            
            <div class="stat-card">
                <div class="stat-value"><?php echo number_format($apiStats['success_requests'] ?? 0); ?></div>
                <div class="stat-label">成功请求数</div>
            </div>
            
            <div class="stat-card">
                <div class="stat-value"><?php echo number_format($apiStats['error_requests'] ?? 0); ?></div>
                <div class="stat-label">错误请求数</div>
            </div>
            
            <div class="stat-card">
                <div class="stat-value"><?php echo number_format($apiStats['unique_ips'] ?? 0); ?></div>
                <div class="stat-label">唯一IP数</div>
            </div>
        </div>
        
        <div class="section-title">请求趋势</div>
        <div class="chart-container">
            <canvas id="requestTrendsChart"></canvas>
        </div>
    </div>
    
    <div class="two-column">
        <div class="card">
            <h2><i class="fas fa-key"></i> 卡密使用统计 <span style="font-size: 14px; opacity: 0.7;"><?php echo $periodLabel; ?></span></h2>
            
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-value"><?php echo number_format($keyUsageStats['active_keys'] ?? 0); ?></div>
                    <div class="stat-label">活跃卡密数</div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-value"><?php echo number_format($keyUsageStats['total_verifications'] ?? 0); ?></div>
                    <div class="stat-label">卡密验证次数</div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-value"><?php echo number_format($keyUsageStats['total_status_checks'] ?? 0); ?></div>
                    <div class="stat-label">状态检查次数</div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-value"><?php echo number_format($keyUsageStats['total_heartbeats'] ?? 0); ?></div>
                    <div class="stat-label">心跳次数</div>
                </div>
            </div>
            
            <div class="section-title">最活跃的卡密</div>
            <div class="table-container">
                <table>
                    <thead>
                        <tr>
                            <th>卡密</th>
                            <th>类型</th>
                            <th>状态</th>
                            <th>使用次数</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (empty($keyUsageStats['top_keys'])): ?>
                        <tr>
                            <td colspan="4" style="text-align: center;">没有卡密使用记录</td>
                        </tr>
                        <?php else: ?>
                            <?php foreach ($keyUsageStats['top_keys'] as $key): ?>
                            <tr>
                                <td><?php echo substr(htmlspecialchars($key['key_value']), 0, 8) . '...'; ?></td>
                                <td><?php echo htmlspecialchars($key['type']); ?></td>
                                <td>
                                    <?php 
                                    if ($key['status'] === 'active') {
                                        echo '<span class="badge badge-success">活跃</span>';
                                    } else {
                                        echo '<span class="badge badge-danger">禁用</span>';
                                    }
                                    ?>
                                </td>
                                <td><?php echo number_format($key['usage_count']); ?></td>
                            </tr>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
        
        <div class="card">
            <h2><i class="fas fa-exchange-alt"></i> 端点使用统计 <span style="font-size: 14px; opacity: 0.7;"><?php echo $periodLabel; ?></span></h2>
            
            <div class="table-container">
                <table>
                    <thead>
                        <tr>
                            <th>端点</th>
                            <th>总请求数</th>
                            <th>成功率</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (empty($apiStats['endpoints'])): ?>
                        <tr>
                            <td colspan="3" style="text-align: center;">没有端点使用记录</td>
                        </tr>
                        <?php else: ?>
                            <?php foreach ($apiStats['endpoints'] as $endpoint): ?>
                            <tr>
                                <td><?php echo htmlspecialchars($endpoint['endpoint']); ?></td>
                                <td><?php echo number_format($endpoint['count']); ?></td>
                                <td>
                                    <?php 
                                    $successRate = $endpoint['count'] > 0 ? ($endpoint['success'] / $endpoint['count'] * 100) : 0;
                                    ?>
                                    <div class="progress-container">
                                        <div class="progress-bar <?php echo $successRate >= 90 ? 'success' : ($successRate < 70 ? 'error' : ''); ?>" style="width: <?php echo $successRate; ?>%;"></div>
                                    </div>
                                    <div class="progress-label">
                                        <span><?php echo number_format($endpoint['success']); ?> 成功</span>
                                        <span><?php echo number_format($successRate, 1); ?>%</span>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
            
            <div class="section-title">常见错误</div>
            <div class="table-container">
                <table>
                    <thead>
                        <tr>
                            <th>错误消息</th>
                            <th>出现次数</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (empty($apiStats['errors'])): ?>
                        <tr>
                            <td colspan="2" style="text-align: center;">没有错误记录</td>
                        </tr>
                        <?php else: ?>
                            <?php foreach ($apiStats['errors'] as $error): ?>
                            <tr>
                                <td><?php echo htmlspecialchars($error['message']); ?></td>
                                <td><?php echo number_format($error['count']); ?></td>
                            </tr>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    
    <div class="card">
        <h2><i class="fas fa-history"></i> 最近的API请求 <span style="font-size: 14px; opacity: 0.7;"><?php echo $periodLabel; ?></span></h2>
        
        <div class="table-container">
            <table>
                <thead>
                    <tr>
                        <th>时间</th>
                        <th>端点</th>
                        <th>IP地址</th>
                        <th>状态</th>
                        <th>消息</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (empty($apiStats['recent_logs'])): ?>
                    <tr>
                        <td colspan="5" style="text-align: center;">没有API请求记录</td>
                    </tr>
                    <?php else: ?>
                        <?php foreach ($apiStats['recent_logs'] as $log): ?>
                        <tr>
                            <td><?php echo date('Y-m-d H:i:s', strtotime($log['created_at'])); ?></td>
                            <td><?php echo htmlspecialchars($log['endpoint']); ?></td>
                            <td><?php echo htmlspecialchars($log['ip_address']); ?></td>
                            <td>
                                <?php 
                                if ($log['response_status'] === 'success') {
                                    echo '<span class="badge badge-success">成功</span>';
                                } else {
                                    echo '<span class="badge badge-danger">错误</span>';
                                }
                                ?>
                            </td>
                            <td><?php echo htmlspecialchars($log['message']); ?></td>
                        </tr>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 请求趋势图表
    const trendsCtx = document.getElementById('requestTrendsChart').getContext('2d');
    
    // 准备图表数据
    const trendsData = {
        labels: <?php 
            $labels = [];
            foreach ($apiStats['trends'] as $trend) {
                $labels[] = $trend['time_period'];
            }
            echo json_encode($labels);
        ?>,
        datasets: [
            {
                label: '总请求数',
                data: <?php 
                    $data = [];
                    foreach ($apiStats['trends'] as $trend) {
                        $data[] = $trend['total'];
                    }
                    echo json_encode($data);
                ?>,
                borderColor: 'rgba(255, 255, 255, 0.8)',
                backgroundColor: 'rgba(255, 255, 255, 0.1)',
                borderWidth: 2,
                tension: 0.4,
                fill: true
            },
            {
                label: '成功请求',
                data: <?php 
                    $data = [];
                    foreach ($apiStats['trends'] as $trend) {
                        $data[] = $trend['success'];
                    }
                    echo json_encode($data);
                ?>,
                borderColor: 'rgba(40, 167, 69, 0.8)',
                backgroundColor: 'rgba(40, 167, 69, 0.1)',
                borderWidth: 2,
                tension: 0.4,
                fill: true
            },
            {
                label: '错误请求',
                data: <?php 
                    $data = [];
                    foreach ($apiStats['trends'] as $trend) {
                        $data[] = $trend['error'];
                    }
                    echo json_encode($data);
                ?>,
                borderColor: 'rgba(220, 53, 69, 0.8)',
                backgroundColor: 'rgba(220, 53, 69, 0.1)',
                borderWidth: 2,
                tension: 0.4,
                fill: true
            }
        ]
    };
    
    // 创建图表
    new Chart(trendsCtx, {
        type: 'line',
        data: trendsData,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'top',
                    labels: {
                        color: 'rgba(255, 255, 255, 0.7)'
                    }
                }
            },
            scales: {
                x: {
                    grid: {
                        color: 'rgba(255, 255, 255, 0.1)'
                    },
                    ticks: {
                        color: 'rgba(255, 255, 255, 0.7)'
                    }
                },
                y: {
                    beginAtZero: true,
                    grid: {
                        color: 'rgba(255, 255, 255, 0.1)'
                    },
                    ticks: {
                        color: 'rgba(255, 255, 255, 0.7)'
                    }
                }
            }
        }
    });
});
</script> 