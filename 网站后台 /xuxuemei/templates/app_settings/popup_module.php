<?php
// APP弹窗模块
?>

<div class="popup-module">
    <!-- 创建弹窗表单 -->
    <div class="module-card">
        <div class="card-header">
            <h3><i class="fas fa-plus-circle"></i> 创建APP弹窗</h3>
            <p>设置APP内的弹窗提醒内容</p>
        </div>
        
        <form id="popup-form" class="popup-form">
            <div class="form-group">
                <label for="popup-title">弹窗标题 <span class="required">*</span></label>
                <input type="text" id="popup-title" name="title" placeholder="请输入弹窗标题" required>
            </div>

            <div class="form-group">
                <label>提醒方式 <span class="required">*</span></label>
                <div class="reminder-options">
                    <label class="reminder-option">
                        <input type="checkbox" name="type[]" value="once">
                        <span class="option-text">1次</span>
                    </label>
                    <label class="reminder-option">
                        <input type="checkbox" name="type[]" value="daily_7">
                        <span class="option-text">7天</span>
                    </label>
                    <label class="reminder-option">
                        <input type="checkbox" name="type[]" value="always">
                        <span class="option-text">每次</span>
                    </label>
                    <label class="reminder-option custom-option">
                        <input type="checkbox" name="type[]" value="custom">
                        <span class="option-text">自定义天数</span>
                        <input type="number" id="custom-days" name="custom_days" placeholder="天数" min="1" max="365" class="custom-days-input">
                        <span class="days-unit">天</span>
                    </label>
                </div>
            </div>
            
            <div class="form-group">
                <label for="popup-content">弹窗内容 <span class="required">*</span></label>
                <div id="popup-word-editor"></div>
                <input type="hidden" id="popup-content" name="content">
            </div>
            
            <div class="form-actions">
                <button type="button" class="btn btn-secondary" onclick="resetPopupForm()">
                    <i class="fas fa-undo"></i> 重置
                </button>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i> 创建弹窗
                </button>
            </div>
        </form>
    </div>
    
    <!-- 弹窗列表 -->
    <div class="module-card">
        <div class="card-header">
            <div class="header-content">
                <div class="header-text">
                    <h3><i class="fas fa-list"></i> 弹窗管理</h3>
                    <p>管理已创建的APP弹窗</p>
                </div>
                <div class="header-actions">
                    <button class="btn btn-danger btn-sm" onclick="deleteAllPopups()" id="delete-all-btn" style="display: none;">
                        <i class="fas fa-trash-alt"></i> 一键删除
                    </button>
                </div>
            </div>
        </div>
        
        <div class="popup-list" id="popup-list">
            <!-- 弹窗列表将通过JavaScript动态加载 -->
            <div class="loading-placeholder">
                <i class="fas fa-spinner fa-spin"></i>
                <span>加载中...</span>
            </div>
        </div>
    </div>
</div>

<!-- 弹窗模块专用样式 -->
<style>
.popup-module {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.module-card {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 25px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.card-header {
    margin-bottom: 25px;
    text-align: center;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
}

.header-text {
    flex: 1;
}

.header-actions {
    flex-shrink: 0;
}

.card-header h3 {
    color: white;
    font-size: 20px;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.card-header p {
    color: rgba(255, 255, 255, 0.7);
    margin: 0;
    font-size: 14px;
}

.btn-sm {
    padding: 6px 12px;
    font-size: 12px;
    border-radius: 6px;
}

.btn-danger {
    background: linear-gradient(135deg, #ff4757, #ff3838);
    border: none;
    color: white;
    transition: all 0.3s ease;
}

.btn-danger:hover {
    background: linear-gradient(135deg, #ff3838, #ff2f2f);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(255, 71, 87, 0.3);
}

/* 表单样式 */
.popup-form {
    max-width: 800px;
    margin: 0 auto;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    color: white;
    font-weight: 500;
    margin-bottom: 8px;
    font-size: 14px;
}

.required {
    color: #ff6b9d;
}

.form-group input,
.form-group select {
    width: 100%;
    padding: 12px 15px;
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    background: rgba(255, 255, 255, 0.1);
    color: white;
    font-size: 14px;
    box-sizing: border-box;
}

.form-group input::placeholder {
    color: rgba(255, 255, 255, 0.5);
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: #ff6b9d;
    box-shadow: 0 0 0 2px rgba(255, 107, 157, 0.2);
}

/* 提醒方式复选框样式 */
.reminder-options {
    display: flex;
    flex-wrap: nowrap;
    gap: 12px;
    margin-top: 8px;
    align-items: center;
}

.reminder-option {
    display: flex;
    align-items: center;
    gap: 6px;
    cursor: pointer;
    padding: 8px 12px;
    border-radius: 6px;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
    white-space: nowrap;
    flex-shrink: 0;
}

.reminder-option:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 107, 157, 0.3);
}

.reminder-option input[type="checkbox"] {
    width: 16px;
    height: 16px;
    margin: 0;
    cursor: pointer;
    accent-color: #ff6b9d;
}

.reminder-option .option-text {
    color: white;
    font-size: 14px;
    font-weight: 500;
    user-select: none;
}

.reminder-option.custom-option {
    display: flex;
    align-items: center;
    gap: 6px;
    min-width: 180px;
}

.custom-days-input {
    width: 60px !important;
    padding: 4px 6px !important;
    margin: 0 !important;
    background: rgba(255, 255, 255, 0.1) !important;
    border: 1px solid rgba(255, 255, 255, 0.3) !important;
    border-radius: 4px !important;
    color: white !important;
    font-size: 12px !important;
    text-align: center;
}

.custom-days-input:focus {
    border-color: #ff6b9d !important;
    outline: none !important;
}

.days-unit {
    color: rgba(255, 255, 255, 0.8);
    font-size: 12px;
    margin: 0;
}

/* 响应式处理 */
@media (max-width: 768px) {
    .reminder-options {
        flex-wrap: wrap;
        gap: 10px;
    }

    .reminder-option.custom-option {
        min-width: 160px;
    }
}

/* Word风格编辑器样式已在外部CSS文件中定义 */

.ql-toolbar .ql-picker.ql-expanded .ql-picker-label {
    color: #ff6b9d !important;
}

/* 表单操作按钮 */
.form-actions {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-top: 30px;
}

/* 弹窗列表样式 */
.popup-list {
    min-height: 200px;
}

.loading-placeholder {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    color: rgba(255, 255, 255, 0.7);
    padding: 40px;
    font-size: 14px;
}

.popup-item {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 15px;
    transition: all 0.3s ease;
}

.popup-item:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.2);
}

.popup-item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.popup-item-title {
    color: white;
    font-size: 16px;
    font-weight: 600;
    margin: 0;
}

.popup-item-type {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
}

.popup-item-type.once {
    background: rgba(255, 193, 7, 0.2);
    color: #ffc107;
    border: 1px solid rgba(255, 193, 7, 0.3);
}

.popup-item-type.weekly {
    background: rgba(0, 123, 255, 0.2);
    color: #007bff;
    border: 1px solid rgba(0, 123, 255, 0.3);
}

.popup-item-type.permanent {
    background: rgba(40, 167, 69, 0.2);
    color: #28a745;
    border: 1px solid rgba(40, 167, 69, 0.3);
}

.popup-item-content {
    color: rgba(255, 255, 255, 0.8);
    font-size: 14px;
    line-height: 1.5;
    margin-bottom: 15px;
    max-height: 100px;
    overflow: hidden;
    position: relative;
}

.popup-item-actions {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
}

.popup-item-actions .btn {
    padding: 6px 12px;
    font-size: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .form-row {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .editor-toolbar {
        flex-direction: column;
        align-items: stretch;
    }
    
    .toolbar-group {
        justify-content: center;
    }
    
    .form-actions {
        flex-direction: column;
    }
    
    .popup-item-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .popup-item-actions {
        justify-content: center;
    }
}
</style>

<!-- 引入Quill.js库 -->
<link href="https://cdn.quilljs.com/1.3.7/quill.snow.css" rel="stylesheet">
<!-- Font Awesome 图标支持（Quill工具栏图标） -->
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
<!-- Quill深色主题样式 -->
<link href="../assets/quill-dark-theme.css" rel="stylesheet">
<script src="https://cdn.quilljs.com/1.3.7/quill.min.js"></script>

<!-- 修复Quill工具栏图标显示问题 -->
<style>
/* Quill工具栏图标修复 */
.ql-toolbar .ql-bold:before {
    content: "B";
    font-weight: bold;
    font-size: 16px;
}

.ql-toolbar .ql-italic:before {
    content: "I";
    font-style: italic;
    font-size: 16px;
}

.ql-toolbar .ql-underline:before {
    content: "U";
    text-decoration: underline;
    font-size: 16px;
}

.ql-toolbar .ql-strike:before {
    content: "S";
    text-decoration: line-through;
    font-size: 16px;
}

.ql-toolbar .ql-list[value="ordered"]:before {
    content: "1.";
    font-weight: bold;
}

.ql-toolbar .ql-list[value="bullet"]:before {
    content: "•";
    font-weight: bold;
    font-size: 18px;
}

.ql-toolbar .ql-link:before {
    content: "🔗";
    font-size: 14px;
}

.ql-toolbar .ql-blockquote:before {
    content: """;
    font-size: 18px;
    font-weight: bold;
}

.ql-toolbar .ql-clean:before {
    content: "🧹";
    font-size: 14px;
}

.ql-toolbar .ql-indent[value="-1"]:before {
    content: "⬅";
    font-size: 14px;
}

.ql-toolbar .ql-indent[value="+1"]:before {
    content: "➡";
    font-size: 14px;
}

.ql-toolbar .ql-align:before {
    content: "≡";
    font-size: 16px;
}

.ql-toolbar .ql-align[value="center"]:before {
    content: "≡";
    text-align: center;
}

.ql-toolbar .ql-align[value="right"]:before {
    content: "≡";
    text-align: right;
}

.ql-toolbar .ql-align[value="justify"]:before {
    content: "≡";
}

/* 确保工具栏按钮有足够的尺寸和对比度 */
.ql-toolbar button {
    width: 28px !important;
    height: 28px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    border-radius: 4px !important;
    margin: 2px !important;
    background: rgba(255, 255, 255, 0.1) !important;
    color: white !important;
    font-family: Arial, sans-serif !important;
}

.ql-toolbar button:hover {
    background: rgba(255, 255, 255, 0.2) !important;
    border-color: rgba(255, 255, 255, 0.4) !important;
}

.ql-toolbar button.ql-active {
    background: #ff6b9d !important;
    border-color: #ff6b9d !important;
    color: white !important;
}

/* 修复下拉选择器的显示 */
.ql-toolbar .ql-picker {
    color: white !important;
}

.ql-toolbar .ql-picker-label {
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    border-radius: 4px !important;
    background: rgba(255, 255, 255, 0.1) !important;
    color: white !important;
    padding: 4px 8px !important;
    min-width: 60px !important;
}

.ql-toolbar .ql-picker-label:hover {
    background: rgba(255, 255, 255, 0.2) !important;
    border-color: rgba(255, 255, 255, 0.4) !important;
}

.ql-toolbar .ql-picker-options {
    background: rgba(0, 0, 0, 0.95) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    border-radius: 4px !important;
    position: absolute !important;
    z-index: 10000 !important;
    top: 100% !important;
    left: 0 !important;
    min-width: 120px !important;
    margin-top: 2px !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3) !important;
}

.ql-toolbar .ql-picker-item {
    color: white !important;
    padding: 6px 12px !important;
}

.ql-toolbar .ql-picker-item:hover {
    background: rgba(255, 107, 157, 0.2) !important;
}

/* 颜色选择器修复 */
.ql-color .ql-picker-label,
.ql-background .ql-picker-label {
    width: 28px !important;
    height: 28px !important;
}

.ql-color .ql-picker-label:before {
    content: "A";
    font-weight: bold;
    font-size: 16px;
}

.ql-background .ql-picker-label:before {
    content: "A";
    font-weight: bold;
    font-size: 16px;
    background: yellow;
    color: black;
    padding: 2px;
    border-radius: 2px;
}

/* 字体大小选择器 */
.ql-size .ql-picker-label:before {
    content: "大";
    font-size: 14px;
}

.ql-size .ql-picker-label[data-value="small"]:before {
    content: "小";
    font-size: 12px;
}

.ql-size .ql-picker-label[data-value="large"]:before {
    content: "大";
    font-size: 16px;
}

.ql-size .ql-picker-label[data-value="huge"]:before {
    content: "超";
    font-size: 18px;
}
</style>

<!-- Word风格编辑器CSS和JS -->
<link rel="stylesheet" href="../../assets/word-style-editor.css?v=<?php echo time(); ?>">
<script src="../../assets/word-style-editor.js?v=<?php echo time(); ?>"></script>

<!-- 弹窗模块专用JavaScript -->
<script>
// 全局变量保存Word编辑器实例
let popupWordEditor = null;

// 初始化弹窗编辑器
document.addEventListener('DOMContentLoaded', function() {
    initPopupWordEditor();
    loadPopupList();
    initReminderOptions();
});

// 初始化提醒方式选项
function initReminderOptions() {
    const customCheckbox = document.querySelector('input[name="type[]"][value="custom"]');
    const customDaysInput = document.getElementById('custom-days');

    // 监听自定义复选框变化
    if (customCheckbox && customDaysInput) {
        customCheckbox.addEventListener('change', function() {
            if (this.checked) {
                customDaysInput.disabled = false;
                customDaysInput.focus();
            } else {
                customDaysInput.disabled = true;
                customDaysInput.value = '';
            }
        });

        // 初始状态设置
        customDaysInput.disabled = !customCheckbox.checked;
    }
}

function initPopupWordEditor() {
    try {
        console.log('开始初始化弹窗编辑器...');

        // 检查容器是否存在
        const container = document.getElementById('popup-word-editor');
        if (!container) {
            console.error('弹窗编辑器容器不存在');
            return;
        }

        // 检查WordStyleEditor类是否可用
        if (typeof WordStyleEditor === 'undefined') {
            console.error('WordStyleEditor类未定义，请检查是否正确加载了word-style-editor.js');
            return;
        }

        // 初始化Word风格编辑器
        popupWordEditor = new WordStyleEditor('popup-word-editor', {
            placeholder: '请输入弹窗内容...',
            minHeight: '200px'
        });

        console.log('弹窗编辑器初始化成功');

        // 监听内容变化，同步到隐藏字段
        setTimeout(() => {
            const editorContent = document.querySelector('#popup-word-editor .word-editor-content');
            if (editorContent) {
                editorContent.addEventListener('input', function() {
                    const htmlContent = this.innerHTML;
                    document.getElementById('popup-content').value = htmlContent;
                    console.log('编辑器内容已同步到隐藏字段');
                });
                console.log('编辑器事件监听器已绑定');
            } else {
                console.warn('编辑器内容区域未找到');
            }
        }, 100);

    } catch (error) {
        console.error('初始化弹窗编辑器失败:', error);
    }
}

// Quill修复函数已移除，使用Word风格编辑器





// 表单提交处理
document.getElementById('popup-form').addEventListener('submit', function(e) {
    e.preventDefault();

    const formData = new FormData(this);

    // 从Word编辑器获取内容
    if (popupWordEditor) {
        const htmlContent = popupWordEditor.getContent();
        formData.set('content', htmlContent);
        // 同时更新隐藏字段
        document.getElementById('popup-content').value = htmlContent;
    } else {
        // 如果编辑器未初始化，尝试从隐藏字段获取
        const hiddenContent = document.getElementById('popup-content').value;
        if (hiddenContent) {
            formData.set('content', hiddenContent);
        }
    }

    // 调试信息
    console.log('表单数据:', {
        title: formData.get('title'),
        content: formData.get('content'),
        types: formData.getAll('type[]')
    });

    // 验证表单
    if (!validatePopupForm(formData)) {
        return;
    }

    // 检查是否为编辑模式
    const editId = this.dataset.editId;
    const isEditMode = editId && editId !== '';

    let url, method, successMessage;

    if (isEditMode) {
        // 编辑模式：更新现有弹窗
        url = `../../api/app_settings.php/popup/${editId}`;
        method = 'PUT';
        successMessage = '弹窗更新成功！';
    } else {
        // 创建模式：创建新弹窗
        url = '../../api/app_settings.php/popup';
        method = 'POST';
        successMessage = '弹窗创建成功！';
    }

    // 发送到后端保存
    fetch(url, {
        method: method,
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast(successMessage, 'success');
            resetPopupForm();
            loadPopupList();
        } else {
            showToast(data.message || (isEditMode ? '更新失败，请重试' : '创建失败，请重试'), 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('网络错误，请重试', 'error');
    });
});

// 验证表单
function validatePopupForm(formData) {
    const title = formData.get('title');
    const types = formData.getAll('type[]');
    const content = formData.get('content');

    console.log('验证表单数据:', {
        title: title,
        types: types,
        content: content,
        contentLength: content ? content.length : 0
    });

    if (!title || title.trim() === '') {
        showToast('请输入弹窗标题', 'warning');
        document.getElementById('popup-title').focus();
        return false;
    }

    if (!types || types.length === 0) {
        showToast('请至少选择一种提醒方式', 'warning');
        return false;
    }

    // 如果选择了自定义天数，验证天数输入
    if (types.includes('custom')) {
        const customDays = formData.get('custom_days');
        if (!customDays || customDays < 1 || customDays > 365) {
            showToast('请输入有效的自定义天数（1-365天）', 'warning');
            document.getElementById('custom-days').focus();
            return false;
        }
    }

    if (!content || content.trim() === '') {
        console.log('内容验证失败:', {
            content: content,
            editorExists: !!popupWordEditor,
            editorContent: popupWordEditor ? popupWordEditor.getContent() : 'N/A'
        });
        showToast('请输入弹窗内容', 'warning');
        return false;
    }

    console.log('表单验证通过');
    return true;
}

// 重置表单
function resetPopupForm() {
    const form = document.getElementById('popup-form');
    form.reset();
    document.getElementById('popup-content').value = '';

    // 清空Word编辑器内容
    if (popupWordEditor) {
        popupWordEditor.clear();
    }

    // 清除编辑模式
    delete form.dataset.editId;

    // 恢复按钮文本为创建模式
    document.querySelector('#popup-form button[type="submit"]').innerHTML =
        '<i class="fas fa-save"></i> 创建弹窗';
}

// 加载弹窗列表
function loadPopupList() {
    const listContainer = document.getElementById('popup-list');
    
    fetch('../../api/app_settings.php/popup/list')
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            displayPopupList(data.popups);
            // 显示/隐藏一键删除按钮
            const deleteAllBtn = document.getElementById('delete-all-btn');
            if (data.popups && data.popups.length > 0) {
                deleteAllBtn.style.display = 'inline-block';
            } else {
                deleteAllBtn.style.display = 'none';
            }
        } else {
            listContainer.innerHTML = `
                <div class="error-placeholder">
                    <i class="fas fa-exclamation-triangle"></i>
                    <span>加载失败：${data.message}</span>
                </div>
            `;
            // 隐藏一键删除按钮
            document.getElementById('delete-all-btn').style.display = 'none';
        }
    })
    .catch(error => {
        console.error('Error:', error);
        listContainer.innerHTML = `
            <div class="error-placeholder">
                <i class="fas fa-exclamation-triangle"></i>
                <span>网络错误，请刷新重试</span>
            </div>
        `;
    });
}

// 显示弹窗列表
function displayPopupList(popups) {
    const listContainer = document.getElementById('popup-list');
    
    if (!popups || popups.length === 0) {
        listContainer.innerHTML = `
            <div class="empty-placeholder">
                <i class="fas fa-window-maximize"></i>
                <span>暂无弹窗，点击上方创建第一个弹窗</span>
            </div>
        `;
        return;
    }
    
    const popupItems = popups.map(popup => `
        <div class="popup-item" data-id="${popup.id}">
            <div class="popup-item-header">
                <h4 class="popup-item-title">${popup.title}</h4>
                <span class="popup-item-type ${popup.type}">${getTypeText(popup.type)}</span>
            </div>
            <div class="popup-item-content">
                ${stripHtml(popup.content).substring(0, 100)}...
            </div>
            <div class="popup-item-actions">
                <button class="btn btn-info btn-sm" onclick="previewPopup(${popup.id})">
                    <i class="fas fa-eye"></i> 预览
                </button>
                <button class="btn btn-primary btn-sm" onclick="editPopup(${popup.id})">
                    <i class="fas fa-edit"></i> 编辑
                </button>
                <button class="btn btn-danger btn-sm" onclick="deletePopup(${popup.id})">
                    <i class="fas fa-trash"></i> 删除
                </button>
            </div>
        </div>
    `).join('');
    
    listContainer.innerHTML = popupItems;
}

// 获取类型文本
function getTypeText(type) {
    const typeMap = {
        'once': '1次',
        'weekly': '7天',
        'always': '每次',
        'custom': '自定义'
    };

    // 如果是数组，处理多个类型
    if (Array.isArray(type)) {
        return type.map(t => typeMap[t] || t).join(', ');
    }

    return typeMap[type] || type;
}

// 移除HTML标签
function stripHtml(html) {
    const tmp = document.createElement("DIV");
    tmp.innerHTML = html;
    return tmp.textContent || tmp.innerText || "";
}

// 预览弹窗
function previewPopup(id) {
    fetch(`../../api/app_settings.php/popup/${id}`)
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const popup = data.popup;
            showPopupPreview(popup.title, popup.content, popup.type);
        } else {
            showToast('加载弹窗失败', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('网络错误', 'error');
    });
}

// 显示弹窗预览
function showPopupPreview(title, content, type) {
    const modal = document.createElement('div');
    modal.className = 'popup-preview-modal';
    modal.innerHTML = `
        <div class="popup-preview-content">
            <div class="popup-preview-header">
                <h3>${title}</h3>
                <button class="close-preview" onclick="closePopupPreview()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="popup-preview-body">
                ${content}
            </div>
            <div class="popup-preview-footer">
                <button class="btn btn-primary popup-confirm-btn" onclick="closePopupPreview()">我知道了</button>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
    window.popupPreviewModal = modal;

    // 确保按钮事件正确绑定
    const closeBtn = modal.querySelector('.close-preview');
    const confirmBtn = modal.querySelector('.popup-confirm-btn');

    if (closeBtn) {
        closeBtn.addEventListener('click', closePopupPreview);
    }

    if (confirmBtn) {
        confirmBtn.addEventListener('click', closePopupPreview);
    }

    setTimeout(() => modal.classList.add('show'), 100);
}

// 关闭弹窗预览
function closePopupPreview() {
    if (window.popupPreviewModal) {
        window.popupPreviewModal.classList.remove('show');
        setTimeout(() => {
            if (window.popupPreviewModal && window.popupPreviewModal.parentNode) {
                document.body.removeChild(window.popupPreviewModal);
            }
            window.popupPreviewModal = null;
        }, 300);
    }
}

// 编辑弹窗
function editPopup(id) {
    fetch(`../../api/app_settings.php/popup/${id}`)
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const popup = data.popup;
            document.getElementById('popup-title').value = popup.title;

            // 处理提醒方式复选框
            // 先清空所有复选框
            document.querySelectorAll('input[name="type[]"]').forEach(checkbox => {
                checkbox.checked = false;
            });

            // 设置对应的复选框
            const types = Array.isArray(popup.type) ? popup.type : [popup.type];
            types.forEach(type => {
                const checkbox = document.querySelector(`input[name="type[]"][value="${type}"]`);
                if (checkbox) {
                    checkbox.checked = true;
                }
            });

            // 如果有自定义天数，设置天数值
            if (popup.custom_days) {
                document.getElementById('custom-days').value = popup.custom_days;
            }

            // 加载内容到Word编辑器
            if (popupWordEditor) {
                popupWordEditor.setContent(popup.content);
            }
            document.getElementById('popup-content').value = popup.content;
            
            // 设置编辑模式
            document.getElementById('popup-form').dataset.editId = id;
            document.querySelector('#popup-form button[type="submit"]').innerHTML = 
                '<i class="fas fa-save"></i> 更新弹窗';
                
            // 滚动到表单顶部
            document.querySelector('.popup-form').scrollIntoView({ behavior: 'smooth' });
            showToast('弹窗已加载到编辑区', 'info');
        } else {
            showToast('加载弹窗失败', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('网络错误', 'error');
    });
}

// 删除弹窗
function deletePopup(id) {
    showConfirm('确定要删除这个弹窗吗？此操作不可恢复！', function() {
        fetch(`../../api/app_settings.php/popup/${id}`, {
            method: 'DELETE'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showToast('弹窗删除成功！', 'success');
                loadPopupList();
            } else {
                showToast(data.message || '删除失败', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showToast('网络错误', 'error');
        });
    });
}

// 一键删除所有弹窗
function deleteAllPopups() {
    showConfirm('确定要删除所有弹窗吗？此操作不可恢复！', function() {
        fetch('../../api/app_settings.php/popup/all', {
            method: 'DELETE'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showToast(`成功删除 ${data.deleted_count || 0} 个弹窗！`, 'success');
                loadPopupList(); // 重新加载列表
                // 重置表单
                resetPopupForm();
            } else {
                showToast(data.message || '删除失败', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showToast('网络错误，请重试', 'error');
        });
    });
}

// 当标签切换到弹窗时触发
window.addEventListener('tabChanged', function(e) {
    if (e.detail.tab === 'popup') {
        console.log('切换到弹窗页面');
        loadPopupList();
    }
});
</script>

<!-- 弹窗预览模态框样式 -->
<style>
.popup-preview-modal {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 2000;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.popup-preview-modal.show {
    opacity: 1;
}

.popup-preview-content {
    background: white;
    border-radius: 15px;
    max-width: 400px;
    width: 350px;
    max-height: 500px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    transform: scale(0.8);
    transition: transform 0.3s ease;
}

.popup-preview-modal.show .popup-preview-content {
    transform: scale(1);
}

.popup-preview-header {
    padding: 20px 20px 15px 20px;
    border-bottom: 1px solid #eee;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: linear-gradient(135deg, #ff6b9d, #ff8fab);
    border-radius: 15px 15px 0 0;
}

.popup-preview-header h3 {
    color: white;
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    max-width: 80%;
}



.close-preview {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    font-size: 16px;
    color: white;
    cursor: pointer;
    padding: 8px;
    border-radius: 50%;
    transition: all 0.2s ease;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.close-preview:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

.popup-preview-body {
    padding: 20px;
    color: #333;
    font-size: 14px;
    line-height: 1.6;
    max-height: 300px;
    overflow-y: auto;
}

.popup-preview-footer {
    padding: 20px;
    text-align: center;
}

.popup-confirm-btn {
    background: linear-gradient(135deg, #667eea, #764ba2);
    border: none;
    color: white;
    padding: 12px 40px;
    border-radius: 25px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.popup-confirm-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.empty-placeholder,
.error-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px;
    color: rgba(255, 255, 255, 0.6);
    text-align: center;
}

.empty-placeholder i,
.error-placeholder i {
    font-size: 48px;
    margin-bottom: 15px;
    opacity: 0.5;
}

.empty-placeholder span,
.error-placeholder span {
    font-size: 16px;
}
</style>
