<?php
// APP协议模块
?>

<div class="agreement-module">
    <!-- 创建协议表单 -->
    <div class="module-card">
        <div class="card-header">
            <h3><i class="fas fa-file-contract"></i> 创建APP协议</h3>
            <p>编辑并管理APP用户协议内容</p>
        </div>
        
        <form id="agreement-form" class="agreement-form">
            <div class="form-row">
                <div class="form-group">
                    <label for="agreement-title">协议标题 <span class="required">*</span></label>
                    <input type="text" id="agreement-title" name="title" placeholder="请输入协议标题" required>
                </div>

                <div class="form-group">
                    <label for="agreement-sort-order">显示顺序</label>
                    <input type="number" id="agreement-sort-order" name="sort_order" placeholder="数字越小越靠前，如：1, 2, 3..." min="0" step="1">
                    <small class="form-help">留空则自动排在最后</small>
                </div>
            </div>

            <div class="form-group">
                <label for="agreement-content">协议内容 <span class="required">*</span></label>
                <div id="agreement-word-editor"></div>
                <input type="hidden" id="agreement-content" name="content">
            </div>
            
            <div class="form-actions">
                <button type="button" class="btn btn-secondary" onclick="resetAgreementForm()">
                    <i class="fas fa-undo"></i> 重置
                </button>
                <button type="button" class="btn btn-warning" onclick="testAutoFormat()">
                    <i class="fas fa-magic"></i> 测试格式化
                </button>
                <button type="button" class="btn btn-info" onclick="previewAgreement()">
                    <i class="fas fa-eye"></i> 预览
                </button>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i> 保存并发布协议
                </button>
            </div>
        </form>
    </div>
    
    <!-- 协议列表 -->
    <div class="module-card">
        <div class="card-header">
            <h3><i class="fas fa-list"></i> 协议管理</h3>
            <p>管理已保存的APP协议</p>
        </div>
        
        <div class="agreement-list" id="agreement-list">
            <!-- 协议列表将通过JavaScript动态加载 -->
            <div class="loading-placeholder">
                <i class="fas fa-spinner fa-spin"></i>
                <span>加载中...</span>
            </div>
        </div>
    </div>
</div>

<!-- 预览模态框 -->
<div id="agreement-preview-modal" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <h3><i class="fas fa-eye"></i> 协议预览</h3>
            <button type="button" class="close-btn" onclick="closePreviewModal()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="modal-body">
            <div id="agreement-preview-content" class="preview-content"></div>
        </div>
        <div class="modal-footer">
            <button type="button" class="btn btn-secondary" onclick="closePreviewModal()">
                <i class="fas fa-times"></i> 关闭
            </button>
        </div>
    </div>
</div>

<!-- 协议模块专用样式 -->
<style>
.agreement-module {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

/* 继承通用模块样式 */
.agreement-form {
    max-width: 1000px;
    margin: 0 auto;
}

.agreement-form .form-group {
    margin-bottom: 25px;
}

.agreement-form .form-row {
    display: flex;
    gap: 20px;
    margin-bottom: 25px;
}

.agreement-form .form-row .form-group {
    flex: 1;
    margin-bottom: 0;
}

.agreement-form .rich-editor {
    min-height: 400px;
    max-height: 600px;
}

.agreement-form select {
    width: 100%;
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    background-color: white;
    cursor: pointer;
}

.agreement-form select:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

/* 协议列表样式 */
.agreement-item-badges {
    display: flex;
    gap: 8px;
    align-items: center;
}



/* Word风格编辑器样式已在外部CSS文件中定义 */

/* 协议列表样式 */
.agreement-item {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 15px;
    transition: all 0.3s ease;
}

.agreement-item:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.2);
}

.agreement-item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.agreement-item-title {
    color: white;
    font-size: 18px;
    font-weight: 600;
    margin: 0;
}

/* 状态标签样式 */
.badge {
    display: inline-block;
    padding: 4px 8px;
    font-size: 12px;
    font-weight: 500;
    border-radius: 4px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.badge-success {
    background-color: #28a745;
    color: white;
}

.badge-secondary {
    background-color: #6c757d;
    color: white;
}



.agreement-item-meta {
    color: rgba(255, 255, 255, 0.6);
    font-size: 13px;
    margin-bottom: 10px;
    display: flex;
    gap: 20px;
}

.agreement-item-content {
    color: rgba(255, 255, 255, 0.8);
    font-size: 14px;
    line-height: 1.5;
    margin-bottom: 15px;
    max-height: 80px;
    overflow: hidden;
    position: relative;
}

.agreement-item-actions {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
}

.agreement-item-actions .btn {
    padding: 6px 12px;
    font-size: 12px;
}

/* 预览模态框样式 */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
}

.modal.show {
    display: flex;
    align-items: center;
    justify-content: center;
    animation: fadeIn 0.3s ease;
}

.modal-content {
    background: linear-gradient(135deg, #1e3c72, #2a5298);
    border-radius: 15px;
    width: 90%;
    max-width: 800px;
    max-height: 90vh;
    overflow: hidden;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    animation: slideUp 0.3s ease;
}

.modal-header {
    padding: 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    color: white;
    margin: 0;
    font-size: 18px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.close-btn {
    background: transparent;
    border: none;
    color: rgba(255, 255, 255, 0.7);
    font-size: 18px;
    cursor: pointer;
    padding: 5px;
    border-radius: 5px;
    transition: all 0.2s ease;
}

.close-btn:hover {
    color: white;
    background: rgba(255, 255, 255, 0.1);
}

.modal-body {
    padding: 20px;
    max-height: 60vh;
    overflow-y: auto;
}

.preview-content {
    background: white;
    border-radius: 8px;
    padding: 20px;
    color: #333;
    font-size: 14px;
    line-height: 1.6;
    min-height: 200px;
}

.modal-footer {
    padding: 15px 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .agreement-item-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .agreement-item-status {
        align-self: flex-end;
    }
    
    .agreement-item-meta {
        flex-direction: column;
        gap: 5px;
    }
    
    .agreement-item-actions {
        justify-content: center;
    }
    
    .modal-content {
        width: 95%;
        margin: 10px;
    }
}
</style>

<!-- 引入Quill.js库 -->
<link href="https://cdn.quilljs.com/1.3.7/quill.snow.css" rel="stylesheet">
<!-- Font Awesome 图标支持（Quill工具栏图标） -->
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
<!-- Quill深色主题样式 -->
<link href="../assets/quill-dark-theme.css" rel="stylesheet">
<script src="https://cdn.quilljs.com/1.3.7/quill.min.js"></script>

<!-- 修复Quill工具栏图标显示问题 -->
<style>
/* Quill工具栏图标修复 */
.ql-toolbar .ql-bold:before {
    content: "B";
    font-weight: bold;
    font-size: 16px;
}

.ql-toolbar .ql-italic:before {
    content: "I";
    font-style: italic;
    font-size: 16px;
}

.ql-toolbar .ql-underline:before {
    content: "U";
    text-decoration: underline;
    font-size: 16px;
}

.ql-toolbar .ql-strike:before {
    content: "S";
    text-decoration: line-through;
    font-size: 16px;
}

.ql-toolbar .ql-list[value="ordered"]:before {
    content: "1.";
    font-weight: bold;
}

.ql-toolbar .ql-list[value="bullet"]:before {
    content: "•";
    font-weight: bold;
    font-size: 18px;
}

.ql-toolbar .ql-link:before {
    content: "🔗";
    font-size: 14px;
}

.ql-toolbar .ql-blockquote:before {
    content: """;
    font-size: 18px;
    font-weight: bold;
}

.ql-toolbar .ql-clean:before {
    content: "🧹";
    font-size: 14px;
}

.ql-toolbar .ql-indent[value="-1"]:before {
    content: "⬅";
    font-size: 14px;
}

.ql-toolbar .ql-indent[value="+1"]:before {
    content: "➡";
    font-size: 14px;
}

.ql-toolbar .ql-align:before {
    content: "≡";
    font-size: 16px;
}

.ql-toolbar .ql-align[value="center"]:before {
    content: "≡";
    text-align: center;
}

.ql-toolbar .ql-align[value="right"]:before {
    content: "≡";
    text-align: right;
}

.ql-toolbar .ql-align[value="justify"]:before {
    content: "≡";
}

/* 添加字体格式选择器 */
.ql-toolbar .ql-header[value="1"]:before {
    content: "H1";
    font-size: 18px;
    font-weight: bold;
}

.ql-toolbar .ql-header[value="2"]:before {
    content: "H2";
    font-size: 16px;
    font-weight: bold;
}

.ql-toolbar .ql-header[value="3"]:before {
    content: "H3";
    font-size: 14px;
    font-weight: bold;
}

/* 确保工具栏按钮有足够的尺寸和对比度 */
.ql-toolbar button {
    width: 28px !important;
    height: 28px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    border-radius: 4px !important;
    margin: 2px !important;
    background: rgba(255, 255, 255, 0.1) !important;
    color: white !important;
    font-family: Arial, sans-serif !important;
}

.ql-toolbar button:hover {
    background: rgba(255, 255, 255, 0.2) !important;
    border-color: rgba(255, 255, 255, 0.4) !important;
}

.ql-toolbar button.ql-active {
    background: #ff6b9d !important;
    border-color: #ff6b9d !important;
    color: white !important;
}

/* 修复下拉选择器的显示 */
.ql-toolbar .ql-picker {
    color: white !important;
}

.ql-toolbar .ql-picker-label {
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    border-radius: 4px !important;
    background: rgba(255, 255, 255, 0.1) !important;
    color: white !important;
    padding: 4px 8px !important;
    min-width: 60px !important;
}

.ql-toolbar .ql-picker-label:hover {
    background: rgba(255, 255, 255, 0.2) !important;
    border-color: rgba(255, 255, 255, 0.4) !important;
}

.ql-toolbar .ql-picker-options {
    background: rgba(30, 30, 30, 0.95) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    border-radius: 4px !important;
}

.ql-toolbar .ql-picker-item {
    color: white !important;
    padding: 6px 12px !important;
}

.ql-toolbar .ql-picker-item:hover {
    background: rgba(255, 107, 157, 0.2) !important;
}

/* 颜色选择器修复 */
.ql-color .ql-picker-label,
.ql-background .ql-picker-label {
    width: 28px !important;
    height: 28px !important;
}

.ql-color .ql-picker-label:before {
    content: "A";
    font-weight: bold;
    font-size: 16px;
}

.ql-background .ql-picker-label:before {
    content: "A";
    font-weight: bold;
    font-size: 16px;
    background: yellow;
    color: black;
    padding: 2px;
    border-radius: 2px;
}

/* 字体大小和字体选择器 */
.ql-size .ql-picker-label:before {
    content: "大";
    font-size: 14px;
}

.ql-size .ql-picker-label[data-value="small"]:before {
    content: "小";
    font-size: 12px;
}

.ql-size .ql-picker-label[data-value="large"]:before {
    content: "大";
    font-size: 16px;
}

.ql-size .ql-picker-label[data-value="huge"]:before {
    content: "超";
    font-size: 18px;
}

.ql-font .ql-picker-label:before {
    content: "字体";
    font-size: 14px;
}
</style>

<!-- Word风格编辑器CSS和JS -->
<link rel="stylesheet" href="../../assets/word-style-editor.css?v=<?php echo time(); ?>">
<script src="../../assets/word-style-editor.js?v=<?php echo time(); ?>"></script>

<!-- 协议模块专用JavaScript -->
<script>
// 全局变量保存Word编辑器实例
let agreementWordEditor = null;

// 初始化协议编辑器
document.addEventListener('DOMContentLoaded', function() {
    initAgreementWordEditor();
    loadAgreementList();
});

function initAgreementWordEditor() {
    try {
        console.log('开始初始化协议编辑器...');

        // 检查容器是否存在
        const container = document.getElementById('agreement-word-editor');
        if (!container) {
            console.error('协议编辑器容器不存在');
            return;
        }

        // 检查WordStyleEditor类是否可用
        if (typeof WordStyleEditor === 'undefined') {
            console.error('WordStyleEditor类未定义，请检查是否正确加载了word-style-editor.js');
            return;
        }

        // 初始化Word风格编辑器
        agreementWordEditor = new WordStyleEditor('agreement-word-editor', {
            placeholder: '请输入协议内容...',
            minHeight: '300px'
        });

        console.log('协议编辑器初始化成功');

        // 监听内容变化，同步到隐藏字段
        setTimeout(() => {
            const editorContent = document.querySelector('#agreement-word-editor .word-editor-content');
            if (editorContent) {
                editorContent.addEventListener('input', function() {
                    const htmlContent = this.innerHTML;
                    document.getElementById('agreement-content').value = htmlContent;
                    console.log('协议编辑器内容已同步到隐藏字段');
                });
                console.log('协议编辑器事件监听器已绑定');
            } else {
                console.warn('协议编辑器内容区域未找到');
            }
        }, 100);

    } catch (error) {
        console.error('初始化协议编辑器失败:', error);
    }
}

// 测试自动格式化功能
function testAutoFormat() {
    if (!agreementWordEditor) {
        alert('编辑器未初始化');
        return;
    }

    // 插入测试内容
    const testContent = `
一、服务定义与范围
1.1 本软件（以下简称"本服务"）是由技术开发者团队运营的人工智能在线咨询应答、常见问题处理等技术支持服务。
1.2 用户需通过购买有效卡密激活服务权限，下单后可包但不限于：单次使用权限（具体以官方购买页面说明为准）

二、用户使用规范
2.1 合法使用承诺
用户在使用本服务时须严格遵守以下规定：
(a) 不得利用本服务从事任何违反所在国家/地区法律的行为；
(b) 禁止用于欺诈、钓鱼、传播恶意信息、侵犯他人权利等非法活动；

三、数据与隐私保护
3.1 数据安全保障
`;

    // 设置编辑器内容
    agreementWordEditor.setContent(testContent);

    // 手动触发格式化
    setTimeout(() => {
        agreementWordEditor.autoFormatTitles();
        alert('测试内容已插入，请检查标题是否自动加粗');
    }, 500);
}

// Quill修复函数已移除，使用Word风格编辑器



// 表单提交处理
document.getElementById('agreement-form').addEventListener('submit', function(e) {
    e.preventDefault();

    // 获取表单数据
    const title = document.getElementById('agreement-title').value.trim();
    let content = '';

    // 从Word编辑器获取内容
    if (agreementWordEditor) {
        content = agreementWordEditor.getContent();
    }

    // 验证必填字段
    if (!title) {
        showToast('请输入协议标题', 'warning');
        return;
    }

    if (!content || content.trim() === '') {
        showToast('请输入协议内容', 'warning');
        return;
    }

    // 获取排序字段值
    const sortOrderInput = document.getElementById('agreement-sort-order');
    const sortOrder = sortOrderInput ? parseInt(sortOrderInput.value) || 0 : 0;

    // 准备JSON数据 - 协议类型根据标题自动确定，状态默认为草稿
    const requestData = {
        title: title,
        content: content,
        type: 'privacy', // 固定为隐私协议类型
        version: '1.0', // 固定版本号
        status: 'draft', // 保存时默认为草稿状态
        sort_order: sortOrder // 添加排序字段
    };

    // 检查是否为编辑模式
    const editId = this.dataset.editId;
    const isEdit = editId && editId !== '';

    // 显示提交状态
    const submitBtn = this.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> ' + (isEdit ? '更新中...' : '保存中...');
    submitBtn.disabled = true;

    // 确定API URL和方法
    const apiUrl = isEdit ? `../../api/app_settings.php/agreement/${editId}` : '../../api/app_settings.php/agreement';
    const method = isEdit ? 'PUT' : 'POST';

    // 发送到后端保存
    fetch(apiUrl, {
        method: method,
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData)
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        if (data.success) {
            showToast(isEdit ? '协议更新成功！' : '协议保存成功！', 'success');

            // 保存成功后自动发布协议
            if (data.agreement && data.agreement.id) {
                publishAgreementAfterSave(data.agreement.id);
            }

            resetAgreementForm();
            loadAgreementList();
        } else {
            showToast(data.message || (isEdit ? '更新失败，请重试' : '保存失败，请重试'), 'error');
            console.error('API Error:', data);
        }
    })
    .catch(error => {
        console.error('Network Error:', error);
        showToast('网络错误，请重试: ' + error.message, 'error');
    })
    .finally(() => {
        // 恢复按钮状态
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    });
});

// 重置表单
function resetAgreementForm() {
    const form = document.getElementById('agreement-form');
    form.reset();
    document.getElementById('agreement-content').value = '';

    // 重置排序字段
    const sortOrderInput = document.getElementById('agreement-sort-order');
    if (sortOrderInput) {
        sortOrderInput.value = '';
    }

    // 清除编辑模式
    delete form.dataset.editId;
    form.querySelector('button[type="submit"]').innerHTML = '<i class="fas fa-save"></i> 保存并发布协议';

    // 清空Word编辑器内容
    if (agreementWordEditor) {
        agreementWordEditor.clear();
    }
}

// 预览协议
function previewAgreement() {
    const title = document.getElementById('agreement-title').value;
    let content = '';
    
    // 从Word编辑器获取内容
    if (agreementWordEditor) {
        content = agreementWordEditor.getContent();
    }
    
    if (!title || !content) {
        showToast('请先填写协议标题和内容', 'warning');
        return;
    }
    
    const previewContent = document.getElementById('agreement-preview-content');
    previewContent.innerHTML = `
        <h2 style="color: #333; margin-bottom: 20px; text-align: center;">${title}</h2>
        ${content}
    `;
    
    document.getElementById('agreement-preview-modal').classList.add('show');
}

// 关闭预览模态框
function closePreviewModal() {
    document.getElementById('agreement-preview-modal').classList.remove('show');
}

// 加载协议列表
function loadAgreementList() {
    const listContainer = document.getElementById('agreement-list');
    
    fetch('../../api/app_settings.php/agreement/list')
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            displayAgreementList(data.agreements);
        } else {
            listContainer.innerHTML = `
                <div class="error-placeholder">
                    <i class="fas fa-exclamation-triangle"></i>
                    <span>加载失败：${data.message}</span>
                </div>
            `;
        }
    })
    .catch(error => {
        console.error('Error:', error);
        listContainer.innerHTML = `
            <div class="error-placeholder">
                <i class="fas fa-exclamation-triangle"></i>
                <span>网络错误，请刷新重试</span>
            </div>
        `;
    });
}

// 显示协议列表
function displayAgreementList(agreements) {
    const listContainer = document.getElementById('agreement-list');
    
    if (!agreements || agreements.length === 0) {
        listContainer.innerHTML = `
            <div class="empty-placeholder">
                <i class="fas fa-file-contract"></i>
                <span>暂无协议，点击上方创建第一个协议</span>
            </div>
        `;
        return;
    }
    
    const agreementItems = agreements.map(agreement => {
        // 根据协议状态决定是否显示发布按钮
        const isPublished = agreement.status === 'published';
        const publishButton = isPublished ?
            `<button class="btn btn-warning btn-sm" onclick="unpublishAgreement(${agreement.id})">
                <i class="fas fa-undo"></i> 撤回
            </button>` :
            `<button class="btn btn-success btn-sm" onclick="publishAgreement(${agreement.id})">
                <i class="fas fa-paper-plane"></i> 发布
            </button>`;

        const statusBadge = isPublished ?
            '<span class="badge badge-success">已发布</span>' :
            '<span class="badge badge-secondary">草稿</span>';

        return `
        <div class="agreement-item" data-id="${agreement.id}">
            <div class="agreement-item-header">
                <h4 class="agreement-item-title">${agreement.title}</h4>
                ${statusBadge}
            </div>
            <div class="agreement-item-meta">
                <span><i class="fas fa-sort-numeric-up"></i> 排序：${agreement.sort_order || 0}</span>
                <span><i class="fas fa-calendar"></i> 创建：${formatDate(agreement.created_at)}</span>
                <span><i class="fas fa-edit"></i> 更新：${formatDate(agreement.updated_at)}</span>
            </div>
            <div class="agreement-item-content">
                ${stripHtml(agreement.content).substring(0, 100)}...
            </div>
            <div class="agreement-item-actions">
                <button class="btn btn-info btn-sm" onclick="previewAgreementItem(${agreement.id})">
                    <i class="fas fa-eye"></i> 预览
                </button>
                <button class="btn btn-primary btn-sm" onclick="editAgreement(${agreement.id})">
                    <i class="fas fa-edit"></i> 编辑
                </button>
                ${publishButton}
                <button class="btn btn-danger btn-sm" onclick="deleteAgreement(${agreement.id})">
                    <i class="fas fa-trash"></i> 删除
                </button>
            </div>
        </div>
        `;
    }).join('');
    
    listContainer.innerHTML = agreementItems;
}



// 格式化日期
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN');
}

// 移除HTML标签
function stripHtml(html) {
    const tmp = document.createElement("DIV");
    tmp.innerHTML = html;
    return tmp.textContent || tmp.innerText || "";
}

// 预览协议项目
function previewAgreementItem(id) {
    fetch(`../../api/app_settings.php/agreement/${id}`)
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const previewContent = document.getElementById('agreement-preview-content');
            previewContent.innerHTML = `
                <h2 style="color: #333; margin-bottom: 20px; text-align: center;">${data.agreement.title}</h2>
                ${data.agreement.content}
            `;
            document.getElementById('agreement-preview-modal').classList.add('show');
        } else {
            showToast('加载协议失败', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('网络错误', 'error');
    });
}

// 编辑协议
function editAgreement(id) {
    fetch(`../../api/app_settings.php/agreement/${id}`)
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const agreement = data.agreement;

            // 加载所有字段
            document.getElementById('agreement-title').value = agreement.title;

            // 填充排序字段
            const sortOrderInput = document.getElementById('agreement-sort-order');
            if (sortOrderInput) {
                sortOrderInput.value = agreement.sort_order || 0;
            }

            // 加载内容到Word编辑器
            if (agreementWordEditor) {
                agreementWordEditor.setContent(agreement.content);
            }
            document.getElementById('agreement-content').value = agreement.content;

            // 设置编辑模式
            document.getElementById('agreement-form').dataset.editId = id;
            document.querySelector('#agreement-form button[type="submit"]').innerHTML =
                '<i class="fas fa-save"></i> 更新并发布协议';

            // 滚动到表单顶部
            document.querySelector('.agreement-form').scrollIntoView({ behavior: 'smooth' });
            showToast('协议已加载到编辑区', 'info');
        } else {
            showToast('加载协议失败', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('网络错误', 'error');
    });
}

// 保存后自动发布协议
function publishAgreementAfterSave(id) {
    fetch('../../api/app_settings.php/agreement/publish', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ id: id })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast('协议已自动发布到app！', 'success');
        } else {
            showToast('协议保存成功，但发布失败：' + (data.message || '未知错误'), 'warning');
        }
    })
    .catch(error => {
        console.error('Auto publish error:', error);
        showToast('协议保存成功，但自动发布失败', 'warning');
    });
}

// 手动发布协议
function publishAgreement(id) {
    showConfirm('确定要发布这个协议吗？发布后用户将能看到最新内容。', function() {
        fetch('../../api/app_settings.php/agreement/publish', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ id: id })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showToast('协议发布成功！', 'success');
                loadAgreementList();
            } else {
                showToast(data.message || '发布失败', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showToast('网络错误', 'error');
        });
    });
}

// 撤回协议（设置为草稿状态）
function unpublishAgreement(id) {
    showConfirm('确定要撤回这个协议吗？撤回后将变为草稿状态。', function() {
        // 通过更新协议状态来撤回
        fetch(`../../api/app_settings.php/agreement/${id}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                status: 'draft',
                // 需要获取当前协议信息来保持其他字段不变
                _action: 'unpublish'
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showToast('协议撤回成功！', 'info');
                loadAgreementList();
            } else {
                showToast(data.message || '撤回失败', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showToast('网络错误', 'error');
        });
    });
}

// 删除协议
function deleteAgreement(id) {
    showConfirm('确定要删除这个协议吗？此操作不可恢复！', function() {
        fetch(`../../api/app_settings.php/agreement/${id}`, {
            method: 'DELETE'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showToast('协议删除成功！', 'success');
                loadAgreementList();
            } else {
                showToast(data.message || '删除失败', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showToast('网络错误', 'error');
        });
    });
}

// 点击模态框外部关闭
document.getElementById('agreement-preview-modal').addEventListener('click', function(e) {
    if (e.target === this) {
        closePreviewModal();
    }
});
</script>