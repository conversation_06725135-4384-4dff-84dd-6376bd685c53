<?php
// APP更新模块
?>

<div class="update-module">
    <!-- 创建新版本表单 -->
    <div class="module-card">
        <div class="card-header">
            <h3><i class="fas fa-download"></i> 创建新版本</h3>
            <p>发布APP的新版本更新</p>
        </div>
        
        <form id="update-form" class="update-form" enctype="multipart/form-data">
            <div class="form-row">
                <div class="form-group">
                    <label for="version-number">版本号</label>
                    <input type="text" id="version-number" name="version" placeholder="例如：1.0.1（可选）"
                           pattern="^\d+\.\d+\.\d+$" title="版本号格式：主版本号.次版本号.修订号">
                </div>

                <div class="form-group">
                    <label for="version-title">版本标题</label>
                    <input type="text" id="version-title" name="title" placeholder="请输入版本标题（可选）">
                </div>
            </div>

            <div class="form-group">
                <label for="update-description">更新说明</label>
                <div id="update-word-editor"></div>
                <input type="hidden" id="update-description" name="description">
            </div>



            <!-- Windows下载链接区域 -->
            <div class="windows-download-section">
                <div class="section-header">
                    <h4><i class="fab fa-windows"></i> Windows安装包下载链接</h4>
                    <span class="optional">(可选)</span>
                </div>
                
                <div class="form-group">
                    <label for="exe-download-url">
                        <i class="fas fa-bolt"></i> 直链地址 (推荐)
                        <span class="help-text">用于APP内自动升级的高速直链地址，如developer-oss.lanrar.com链接</span>
                    </label>
                    <input type="url" id="exe-download-url" name="exe_download_url"
                           placeholder="请输入Windows安装包直链地址（推荐使用蓝奏云解析的直链）"
                           class="url-input">
                </div>
                
                <div class="form-group">
                    <label for="exe-backup-url">
                        <i class="fas fa-life-ring"></i> 备用下载地址
                        <span class="help-text">当直链失效时的备用下载地址，通常是蓝奏云分享链接</span>
                    </label>
                    <input type="url" id="exe-backup-url" name="exe_backup_url"
                           placeholder="请输入Windows安装包备用下载地址（如蓝奏云分享链接）"
                           class="url-input">
                    <div class="url-helper">
                        <i class="fas fa-lightbulb"></i>
                        建议使用蓝奏云链接作为备用地址，确保下载稳定性
                    </div>
                </div>
            </div>

            <!-- macOS下载链接区域 - 只支持分架构版本 -->
            <div class="macos-download-section">
                <div class="section-header">
                    <h4><i class="fab fa-apple"></i> macOS安装包下载链接</h4>
                    <span class="architecture-note">请分别上传M芯片和Intel版本的安装包</span>
                </div>

                <!-- 分架构版本输入框 -->
                <div id="separate-mode" class="macos-mode-content">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="dmg-m1-download-url">
                                <i class="fas fa-microchip"></i> M芯片版本直链地址
                                <span class="help-text">适用于M1/M2/M3等Apple Silicon芯片的Mac</span>
                            </label>
                            <input type="url" id="dmg-m1-download-url" name="dmg_m1_download_url"
                                   placeholder="请输入M芯片版本直链地址"
                                   class="url-input">
                        </div>

                        <div class="form-group">
                            <label for="dmg-intel-download-url">
                                <i class="fas fa-desktop"></i> Intel版本直链地址
                                <span class="help-text">适用于Intel处理器的Mac</span>
                            </label>
                            <input type="url" id="dmg-intel-download-url" name="dmg_intel_download_url"
                                   placeholder="请输入Intel版本直链地址"
                                   class="url-input">
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="dmg-m1-backup-url">
                                <i class="fas fa-life-ring"></i> M芯片版本备用地址
                                <span class="help-text">M芯片版本的备用下载地址</span>
                            </label>
                            <input type="url" id="dmg-m1-backup-url" name="dmg_m1_backup_url"
                                   placeholder="请输入M芯片版本备用下载地址"
                                   class="url-input">
                        </div>

                        <div class="form-group">
                            <label for="dmg-intel-backup-url">
                                <i class="fas fa-life-ring"></i> Intel版本备用地址
                                <span class="help-text">Intel版本的备用下载地址</span>
                            </label>
                            <input type="url" id="dmg-intel-backup-url" name="dmg_intel_backup_url"
                                   placeholder="请输入Intel版本备用下载地址"
                                   class="url-input">
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 强制更新已默认启用，不再显示选项 -->
            <input type="hidden" id="force-update" name="force_update" value="1">
            
            <div class="form-actions">
                <button type="button" class="btn btn-secondary" onclick="resetUpdateForm()">
                    <i class="fas fa-undo"></i> 重置
                </button>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-plus"></i> 创建版本
                </button>
            </div>
        </form>
    </div>
    
    <!-- 版本列表 -->
    <div class="module-card">
        <div class="card-header">
            <div class="header-content">
                <div class="header-text">
                    <h3><i class="fas fa-history"></i> 版本管理</h3>
                    <p>管理已创建的APP版本</p>
                </div>
                <div class="header-actions">
                    <button class="btn btn-danger btn-sm" onclick="deleteAllVersions()" id="delete-all-versions-btn" style="display: none;">
                        <i class="fas fa-trash-alt"></i> 一键删除
                    </button>
                </div>
            </div>
        </div>
        
        <div class="version-list" id="version-list">
            <!-- 版本列表将通过JavaScript动态加载 -->
            <div class="loading-placeholder">
                <i class="fas fa-spinner fa-spin"></i>
                <span>加载中...</span>
            </div>
        </div>
    </div>
    
    <!-- 蓝奏云API解析直链 -->
    <div class="module-card">
        <div class="card-header">
            <h3><i class="fas fa-cloud-download-alt"></i> 蓝奏云直链解析</h3>
            <p>输入蓝奏云分享链接，自动解析获取直链地址</p>
        </div>
        
        <div class="lanzou-parser">
            <div class="form-group">
                <label for="lanzou-url">
                    <i class="fas fa-link"></i> 蓝奏云分享链接
                </label>
                <input type="url" id="lanzou-url" name="lanzou-url" 
                       placeholder="请输入蓝奏云分享链接（如：https://wwke.lanzoue.com/iR2qs329vihg）"
                       class="lanzou-input">
            </div>
            
            <div class="form-actions">
                <button type="button" class="btn btn-primary" onclick="parseLanzouUrl()">
                    <i class="fas fa-magic"></i> 获取直链
                </button>
                <button type="button" class="btn btn-secondary" onclick="clearLanzouResult()">
                    <i class="fas fa-trash"></i> 清空
                </button>
            </div>
            
            <div id="lanzou-result" class="lanzou-result" style="display: none;">
                <!-- 解析结果将显示在这里 -->
            </div>
        </div>
    </div>
</div>

<!-- 下载链接区域样式 -->
<style>
/* Windows下载链接区域样式 */
.windows-download-section {
    border: 2px solid #0078d4;
    border-radius: 8px;
    padding: 20px;
    margin: 15px 0;
    background: linear-gradient(135deg, #f0f8ff 0%, #e3f2fd 100%);
}

.windows-download-section .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid rgba(0, 120, 212, 0.3);
}

.windows-download-section .section-header h4 {
    margin: 0;
    color: #0078d4;
    font-size: 16px;
}

.macos-download-section {
    border: 2px solid #e3f2fd;
    border-radius: 8px;
    padding: 20px;
    margin: 15px 0;
    background: linear-gradient(135deg, #f8f9ff 0%, #e3f2fd 100%);
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #ddd;
}

.section-header h4 {
    margin: 0;
    color: #1976d2;
    font-size: 16px;
}

.macos-mode-selector {
    display: flex;
    gap: 15px;
}

.radio-option {
    display: flex;
    align-items: center;
    gap: 5px;
    cursor: pointer;
    padding: 5px 10px;
    border-radius: 5px;
    transition: background-color 0.3s;
}

.radio-option:hover {
    background-color: rgba(25, 118, 210, 0.1);
}

.radio-option input[type="radio"] {
    margin: 0;
}

.architecture-note {
    font-size: 12px;
    color: #ffc107;
    font-style: italic;
    margin-left: 10px;
}

.macos-mode-content {
    transition: all 0.3s ease;
}

.help-text {
    display: block;
    font-size: 12px;
    color: #666;
    margin-top: 2px;
    font-style: italic;
}



/* 表单行样式优化 */
.form-row {
    display: flex;
    gap: 20px;
    margin-bottom: 15px;
}

.form-row .form-group {
    flex: 1;
}

/* URL输入框样式 */
.url-input {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    transition: border-color 0.3s;
}

.url-input:focus {
    outline: none;
    border-color: #1976d2;
    box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.2);
}

.url-input:valid {
    border-color: #4caf50;
}

.url-input:invalid:not(:placeholder-shown) {
    border-color: #f44336;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .section-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .form-row {
        flex-direction: column;
        gap: 10px;
    }

    .macos-mode-selector {
        width: 100%;
        justify-content: flex-start;
    }
}
</style>

<!-- Word风格编辑器CSS和JS -->
<link rel="stylesheet" href="../../assets/word-style-editor.css?v=<?php echo time(); ?>">
<script src="../../assets/word-style-editor.js?v=<?php echo time(); ?>"></script>

<style>
/* 头部布局样式 */
.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
}

.header-text {
    flex: 1;
    text-align: center;
}

.header-actions {
    flex-shrink: 0;
}

.btn-sm {
    padding: 6px 12px;
    font-size: 12px;
    border-radius: 6px;
}

.btn-danger {
    background: linear-gradient(135deg, #ff4757, #ff3838);
    border: none;
    color: white;
    transition: all 0.3s ease;
}

.btn-danger:hover {
    background: linear-gradient(135deg, #ff3838, #ff2f2f);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(255, 71, 87, 0.3);
}

/* Word风格编辑器样式已在外部CSS文件中定义 */
.quill-editor-container {
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 8px;
    background: rgba(0, 0, 0, 0.6);
    overflow: visible;
    position: relative;
}

.quill-editor {
    min-height: 200px;
    background: rgba(0, 0, 0, 0.6);
    color: white;
}

/* Quill编辑器内容区域样式 */
.ql-editor {
    background: rgba(0, 0, 0, 0.6) !important;
    color: white !important;
    border: none !important;
}

.ql-editor p {
    color: white !important;
}

.ql-editor::before {
    color: rgba(255, 255, 255, 0.6) !important;
}

/* Quill工具栏样式 */
.ql-toolbar {
    background: rgba(0, 0, 0, 0.7) !important;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2) !important;
    position: relative !important;
    overflow: visible !important;
}

.ql-toolbar .ql-stroke {
    stroke: rgba(255, 255, 255, 0.8) !important;
}

.ql-toolbar .ql-fill {
    fill: rgba(255, 255, 255, 0.8) !important;
}

.ql-toolbar .ql-picker-label {
    color: rgba(255, 255, 255, 0.8) !important;
}

.ql-toolbar button:hover {
    background: rgba(255, 255, 255, 0.1) !important;
}

.ql-toolbar button.ql-active {
    background: rgba(255, 107, 157, 0.3) !important;
}

/* Quill下拉菜单样式 */
.ql-picker-options {
    background: rgba(0, 0, 0, 0.95) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    position: absolute !important;
    z-index: 10000 !important;
    top: 100% !important;
    left: 0 !important;
    min-width: 120px !important;
    margin-top: 2px !important;
    border-radius: 4px !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3) !important;
}

.ql-picker-item {
    color: white !important;
}

.ql-picker-item:hover {
    background: rgba(255, 255, 255, 0.1) !important;
}

/* Quill工具栏图标修复 */
.ql-toolbar .ql-bold:before {
    content: "B";
    font-weight: bold;
    font-size: 16px;
}

.ql-toolbar .ql-italic:before {
    content: "I";
    font-style: italic;
    font-size: 16px;
}

.ql-toolbar .ql-underline:before {
    content: "U";
    text-decoration: underline;
    font-size: 16px;
}

.ql-toolbar .ql-strike:before {
    content: "S";
    text-decoration: line-through;
    font-size: 16px;
}

.ql-toolbar .ql-list[value="ordered"]:before {
    content: "1.";
    font-weight: bold;
}

.ql-toolbar .ql-list[value="bullet"]:before {
    content: "•";
    font-weight: bold;
    font-size: 18px;
}

.ql-toolbar .ql-link:before {
    content: "🔗";
    font-size: 14px;
}

.ql-toolbar .ql-blockquote:before {
    content: """;
    font-size: 18px;
    font-weight: bold;
}

.ql-toolbar .ql-clean:before {
    content: "🧹";
    font-size: 14px;
}

.ql-toolbar .ql-indent[value="-1"]:before {
    content: "⬅";
    font-size: 14px;
}

.ql-toolbar .ql-indent[value="+1"]:before {
    content: "➡";
    font-size: 14px;
}

.ql-toolbar .ql-align:before {
    content: "≡";
    font-size: 16px;
}

.ql-toolbar .ql-align[value="center"]:before {
    content: "≡";
    text-align: center;
}

.ql-toolbar .ql-align[value="right"]:before {
    content: "≡";
    text-align: right;
}

.ql-toolbar .ql-align[value="justify"]:before {
    content: "≡";
}

/* 确保工具栏按钮有足够的尺寸和对比度 */
.ql-toolbar button {
    width: 28px !important;
    height: 28px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    border-radius: 4px !important;
    margin: 2px !important;
    background: rgba(255, 255, 255, 0.1) !important;
    color: white !important;
    font-family: Arial, sans-serif !important;
}

.ql-toolbar button:hover {
    background: rgba(255, 255, 255, 0.2) !important;
    border-color: rgba(255, 255, 255, 0.4) !important;
}

.ql-toolbar button.ql-active {
    background: #ff6b9d !important;
    border-color: #ff6b9d !important;
    color: white !important;
}

/* 修复下拉选择器的显示 */
.ql-toolbar .ql-picker {
    color: white !important;
}

.ql-toolbar .ql-picker-label {
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    border-radius: 4px !important;
    background: rgba(255, 255, 255, 0.1) !important;
    color: white !important;
    padding: 4px 8px !important;
    min-width: 60px !important;
}

.ql-toolbar .ql-picker-label:hover {
    background: rgba(255, 255, 255, 0.2) !important;
    border-color: rgba(255, 255, 255, 0.4) !important;
}

.ql-toolbar .ql-picker-options {
    background: rgba(30, 30, 30, 0.95) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    border-radius: 4px !important;
}

.ql-toolbar .ql-picker-item {
    color: white !important;
    padding: 6px 12px !important;
}

.ql-toolbar .ql-picker-item:hover {
    background: rgba(255, 107, 157, 0.2) !important;
}

/* 颜色选择器修复 */
.ql-color .ql-picker-label,
.ql-background .ql-picker-label {
    width: 28px !important;
    height: 28px !important;
}

.ql-color .ql-picker-label:before {
    content: "A";
    font-weight: bold;
    font-size: 16px;
}

.ql-background .ql-picker-label:before {
    content: "A";
    font-weight: bold;
    font-size: 16px;
    background: yellow;
    color: black;
    padding: 2px;
    border-radius: 2px;
}

/* 字体大小选择器 */
.ql-size .ql-picker-label:before {
    content: "大";
    font-size: 14px;
}

.ql-size .ql-picker-label[data-value="small"]:before {
    content: "小";
    font-size: 12px;
}

.ql-size .ql-picker-label[data-value="large"]:before {
    content: "大";
    font-size: 16px;
}

.ql-size .ql-picker-label[data-value="huge"]:before {
    content: "超";
    font-size: 18px;
}
</style>

<!-- 更新模块专用样式 -->
<style>
.update-module {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

/* 表单样式 */
.update-form {
    max-width: 900px;
    margin: 0 auto;
}

.update-form .form-group {
    margin-bottom: 25px;
}

.update-form .rich-editor {
    min-height: 150px;
}

/* 下载链接说明样式 */
.download-links-info {
    background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
    border: 1px solid #e1bee7;
    border-radius: 12px;
    padding: 20px;
    margin: 20px 0;
    position: relative;
}

.download-links-info::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #2196f3, #9c27b0);
    border-radius: 12px 12px 0 0;
}

.info-header {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
    font-weight: 600;
    color: #4a148c;
}

.info-header i {
    font-size: 18px;
    margin-right: 8px;
    color: #2196f3;
}

.info-content p {
    margin: 0 0 10px 0;
    color: #333;
    font-size: 14px;
}

.info-content ul {
    margin: 0;
    padding-left: 20px;
}

.info-content li {
    margin: 5px 0;
    color: #555;
    font-size: 13px;
}

/* URL输入框样式 */
.url-input-container {
    position: relative;
    margin-top: 8px;
}

.url-input {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 8px;
    font-size: 14px;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.1);
    color: white;
}

.url-input:focus {
    border-color: #2196f3;
    box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.1);
    outline: none;
    background: rgba(255, 255, 255, 0.15);
}

.url-input::placeholder {
    color: rgba(255, 255, 255, 0.6);
}

.url-helper {
    display: flex;
    align-items: center;
    margin-top: 6px;
    font-size: 12px;
    color: rgba(255, 255, 255, 0.7);
}

.url-helper i {
    margin-right: 5px;
    color: #ff9800;
}

.form-group-inline {
    margin-top: 10px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.form-group-inline label {
    margin: 0;
    font-size: 13px;
    color: rgba(255, 255, 255, 0.8);
    white-space: nowrap;
}

.size-input {
    flex: 1;
    padding: 8px 12px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 6px;
    font-size: 13px;
    background: rgba(255, 255, 255, 0.1);
    color: white;
}

.size-input::placeholder {
    color: rgba(255, 255, 255, 0.6);
}

.required {
    color: #f44336;
    font-weight: normal;
    margin-left: 4px;
}

.optional {
    color: rgba(255, 255, 255, 0.6);
    font-weight: normal;
    margin-left: 4px;
    font-size: 12px;
}

/* 表单标签图标样式 */
.form-group label i {
    margin-right: 6px;
    width: 16px;
    text-align: center;
}

.fab.fa-windows {
    color: #0078d4;
}

.fab.fa-apple {
    color: #333;
}

/* 版本列表中的下载链接样式 */
.version-links {
    margin-top: 10px;
    padding-top: 10px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.download-link {
    display: flex;
    align-items: center;
    margin: 5px 0;
    font-size: 13px;
}

.download-link i {
    margin-right: 8px;
    width: 16px;
    text-align: center;
}

.download-link a {
    color: #64b5f6;
    text-decoration: none;
    transition: color 0.3s ease;
}

.download-link a:hover {
    color: #42a5f5;
    text-decoration: underline;
}





.btn-info {
    background: #17a2b8;
    color: white;
}

.btn-info:hover {
    background: #138496;
}

/* 编辑模态框样式 */
.edit-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.edit-modal-overlay.show {
    opacity: 1;
    visibility: visible;
}

.edit-modal-container {
    background: #f8f9fa;
    border-radius: 12px;
    max-width: 800px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    transform: scale(0.8) translateY(50px);
    transition: all 0.3s ease;
    border: 1px solid #dee2e6;
}

.edit-modal-overlay.show .edit-modal-container {
    transform: scale(1) translateY(0);
}

.edit-modal-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px 25px;
    border-radius: 12px 12px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.edit-modal-header h3 {
    margin: 0;
    font-size: 20px;
    font-weight: 600;
}

.edit-modal-close {
    background: none;
    border: none;
    color: white;
    font-size: 24px;
    cursor: pointer;
    padding: 5px;
    line-height: 1;
    transition: opacity 0.3s ease;
}

.edit-modal-close:hover {
    opacity: 0.8;
}

.edit-modal-body {
    padding: 30px;
    background: #f8f9fa;
}

.edit-form .form-group {
    margin-bottom: 20px;
}

.edit-form .form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #333;
}

.edit-form .form-group input[type="text"],
.edit-form .form-group input[type="url"] {
    width: 100%;
    padding: 12px;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    font-size: 14px;
    transition: border-color 0.3s ease;
    background: white;
    color: #333;
}

.edit-form .form-group input[type="text"]:focus,
.edit-form .form-group input[type="url"]:focus {
    outline: none;
    border-color: #667eea;
}

.edit-form .form-group input[type="checkbox"] {
    margin-right: 8px;
}

.edit-form .form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.edit-form .form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #e1e5e9;
}

@media (max-width: 768px) {
    .edit-form .form-row {
        grid-template-columns: 1fr;
    }

    .edit-modal-container {
        width: 95%;
        margin: 10px;
    }

    .edit-modal-body {
        padding: 20px;
    }
}
}

.file-upload-area small {
    color: rgba(255, 255, 255, 0.5);
    font-size: 12px;
}

.file-preview {
    margin-top: 10px;
    padding: 10px;
    background: rgba(40, 167, 69, 0.1);
    border: 1px solid rgba(40, 167, 69, 0.3);
    border-radius: 5px;
    color: #28a745;
    font-size: 13px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.file-preview i {
    color: #28a745;
}

/* 复选框样式 */
.checkbox-group {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    background: rgba(255, 107, 157, 0.1);
    border: 1px solid rgba(255, 107, 157, 0.2);
    border-radius: 8px;
}

.checkbox-label {
    display: flex;
    align-items: center;
    cursor: pointer;
    color: white;
    font-size: 14px;
    margin: 0;
}

.checkbox-label input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 20px;
    height: 20px;
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 3px;
    margin-right: 12px;
    position: relative;
    transition: all 0.3s ease;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
    background: #ff6b9d;
    border-color: #ff6b9d;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
    content: '';
    position: absolute;
    left: 6px;
    top: 2px;
    width: 5px;
    height: 10px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
}

.checkbox-text small {
    color: rgba(255, 255, 255, 0.6);
    font-size: 12px;
    margin-left: 5px;
}

/* 版本列表样式 */
.version-item {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 15px;
    transition: all 0.3s ease;
}

.version-item:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.2);
}

.version-item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.version-info {
    display: flex;
    align-items: center;
    gap: 15px;
}

.version-number {
    background: linear-gradient(135deg, #ff6b9d, #c44569);
    color: white;
    padding: 5px 12px;
    border-radius: 20px;
    font-weight: 600;
    font-size: 14px;
}

.version-title {
    color: white;
    font-size: 18px;
    font-weight: 600;
    margin: 0;
}

.version-status {
    display: flex;
    gap: 10px;
    align-items: center;
}

.status-badge {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
}

.status-badge.draft {
    background: rgba(108, 117, 125, 0.2);
    color: #6c757d;
    border: 1px solid rgba(108, 117, 125, 0.3);
}

.status-badge.published {
    background: rgba(40, 167, 69, 0.2);
    color: #28a745;
    border: 1px solid rgba(40, 167, 69, 0.3);
}

/* 强制更新标识样式已移除，因为所有版本都默认为强制更新 */

.version-meta {
    color: rgba(255, 255, 255, 0.6);
    font-size: 13px;
    margin-bottom: 15px;
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
}

.version-description {
    color: rgba(255, 255, 255, 0.8);
    font-size: 14px;
    line-height: 1.5;
    margin-bottom: 15px;
    max-height: 80px;
    overflow: hidden;
    position: relative;
}

.version-files {
    display: flex;
    gap: 15px;
    margin-bottom: 15px;
    flex-wrap: wrap;
}

.file-info {
    background: rgba(0, 123, 255, 0.1);
    border: 1px solid rgba(0, 123, 255, 0.3);
    color: #007bff;
    padding: 5px 10px;
    border-radius: 15px;
    font-size: 12px;
    display: flex;
    align-items: center;
    gap: 5px;
}

.version-actions {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
    flex-wrap: wrap;
}

.version-actions .btn {
    padding: 6px 12px;
    font-size: 12px;
}

/* 蓝奏云解析器样式 */
.lanzou-parser {
    padding: 20px;
}

.lanzou-parser .form-group {
    margin-bottom: 20px;
}

.lanzou-parser .form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: white;
    font-size: 14px;
}

.lanzou-input {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 8px;
    font-size: 14px;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.1);
    color: white;
    box-sizing: border-box;
}

.lanzou-input:focus {
    border-color: #ff6b9d;
    box-shadow: 0 0 0 3px rgba(255, 107, 157, 0.1);
    outline: none;
    background: rgba(255, 255, 255, 0.15);
}

.lanzou-input::placeholder {
    color: rgba(255, 255, 255, 0.6);
}

.lanzou-parser .form-actions {
    display: flex;
    gap: 12px;
    margin: 20px 0;
}

.lanzou-result {
    margin-top: 20px;
    padding: 20px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    border-left: 4px solid #28a745;
}

.lanzou-result.error {
    border-left-color: #dc3545;
    background: rgba(220, 53, 69, 0.1);
}

.lanzou-result.loading {
    border-left-color: #ffc107;
    background: rgba(255, 193, 7, 0.1);
}

.lanzou-result h4 {
    margin: 0 0 10px 0;
    color: white;
    font-size: 16px;
}

.lanzou-result p {
    margin: 8px 0;
    color: rgba(255, 255, 255, 0.8);
    font-size: 14px;
}

.lanzou-file-info {
    display: grid;
    grid-template-columns: auto 1fr;
    gap: 12px 20px;
    margin: 15px 0;
    font-size: 14px;
}

.lanzou-file-info strong {
    color: white;
}

.lanzou-file-info span {
    color: rgba(255, 255, 255, 0.9);
}

.lanzou-direct-link-container {
    display: flex;
    align-items: flex-start;
    gap: 8px;
    margin-top: 8px;
}

.lanzou-direct-link {
    background: rgba(0, 0, 0, 0.3);
    color: #e2e8f0;
    padding: 12px;
    border-radius: 6px;
    font-family: 'Courier New', monospace;
    word-break: break-all;
    word-wrap: break-word;
    white-space: pre-wrap;
    max-width: 100%;
    overflow-wrap: break-word;
    flex: 1;
    font-size: 13px;
    line-height: 1.4;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.copy-link-btn {
    background: rgba(40, 167, 69, 0.8);
    color: white;
    border: none;
    padding: 12px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 44px;
    height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.copy-link-btn:hover {
    background: rgba(40, 167, 69, 1);
    transform: translateY(-1px);
}

.copy-link-btn:active {
    transform: translateY(0);
}

.copy-link-btn.copied {
    background: rgba(23, 162, 184, 0.8);
}

.copy-link-btn.copied i::before {
    content: "\f00c";
}
    font-size: 12px;
    word-break: break-all;
    margin: 10px 0;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.lanzou-download-btn {
    display: inline-block;
    padding: 10px 20px;
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
    text-decoration: none;
    border-radius: 6px;
    margin: 8px 8px 8px 0;
    font-weight: 500;
    font-size: 14px;
    transition: all 0.3s;
}

.lanzou-download-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
    text-decoration: none;
    color: white;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .version-item-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .version-info {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }
    
    .version-status {
        align-self: flex-end;
    }
    
    .version-meta {
        flex-direction: column;
        gap: 5px;
    }
    
    .version-actions {
        justify-content: center;
    }
    
    .file-upload-area {
        padding: 20px 15px;
    }
    
    .lanzou-parser .form-actions {
        flex-direction: column;
    }
    
    .lanzou-file-info {
        grid-template-columns: 1fr;
        gap: 8px;
    }
}
</style>

<!-- 更新模块专用JavaScript -->
<script>
// 全局变量保存Word编辑器实例
let updateWordEditor = null;

// macOS架构支持功能（只支持分架构模式）
function initMacOSArchitectureSupport() {
    console.log('初始化macOS架构支持功能（仅分架构模式）');

    // 绑定URL输入框验证
    bindUrlValidation();
}

// 绑定URL输入框验证和蓝奏云自动转换
function bindUrlValidation() {
    const urlInputs = document.querySelectorAll('.url-input');
    urlInputs.forEach(input => {
        input.addEventListener('blur', function() {
            // 先进行蓝奏云URL转换
            handleLanzouUrlConversion(this);
            // 然后验证URL
            validateUrlInput(this);
        });

        input.addEventListener('input', function() {
            // 实时验证
            if (this.value.length > 10) {
                validateUrlInput(this);
            }
        });

        // 添加paste事件监听，处理粘贴的蓝奏云链接
        input.addEventListener('paste', function() {
            // 使用setTimeout确保粘贴的内容已经填入
            setTimeout(() => {
                handleLanzouUrlConversion(this);
                validateUrlInput(this);
            }, 10);
        });
    });
}

// 验证URL输入
function validateUrlInput(input) {
    const url = input.value.trim();
    if (!url) {
        input.style.borderColor = '';
        input.title = '';
        return;
    }

    try {
        new URL(url);
        input.style.borderColor = '#4caf50';
        input.title = '链接格式正确';
    } catch (e) {
        input.style.borderColor = '#f44336';
        input.title = '请输入有效的URL链接';
    }
}

// 获取macOS下载配置（仅支持分架构版本）
function getMacOSDownloadData() {
    const data = {
        mode: 'separate',
        urls: {},
        backupUrls: {},
        hasAnyUrl: false
    };

    const m1Input = document.getElementById('dmg-m1-download-url');
    const intelInput = document.getElementById('dmg-intel-download-url');
    const m1BackupInput = document.getElementById('dmg-m1-backup-url');
    const intelBackupInput = document.getElementById('dmg-intel-backup-url');

    const m1Url = m1Input ? m1Input.value.trim() : '';
    const intelUrl = intelInput ? intelInput.value.trim() : '';
    const m1BackupUrl = m1BackupInput ? m1BackupInput.value.trim() : '';
    const intelBackupUrl = intelBackupInput ? intelBackupInput.value.trim() : '';

    if (m1Url) {
        data.urls.m1 = m1Url;
        data.hasAnyUrl = true;
    }
    if (intelUrl) {
        data.urls.intel = intelUrl;
        data.hasAnyUrl = true;
    }
    if (m1BackupUrl) {
        data.backupUrls.m1 = m1BackupUrl;
    }
    if (intelBackupUrl) {
        data.backupUrls.intel = intelBackupUrl;
    }

    return data;
}

// 获取macOS下载URL检查（用于验证）
function getMacOSDownloadUrls() {
    const data = getMacOSDownloadData();
    
    // 检查是否有任何有效的URL（包括备用地址）
    let hasAnyUrl = false;
    
    // 检查直链地址
    if (data.urls.m1 || data.urls.intel) {
        hasAnyUrl = true;
    }
    
    // 检查备用地址
    if (data.backupUrls.m1 || data.backupUrls.intel) {
        hasAnyUrl = true;
    }
    
    return {
        hasAnyUrl: hasAnyUrl
    };
}

// 检查是否有任何下载链接（Windows或macOS，包括备用地址）
function hasAnyDownloadUrl() {
    // Windows链接检查
    const exeUrl = document.getElementById('exe-download-url').value.trim();
    const exeBackupUrl = document.getElementById('exe-backup-url').value.trim();
    
    if (exeUrl || exeBackupUrl) {
        return true;
    }
    
    // macOS链接检查
    const macosUrls = getMacOSDownloadUrls();
    return macosUrls.hasAnyUrl;
}

// 设置macOS下载配置（仅支持分架构版本）
function setMacOSDownloadConfig(version) {
    console.log('设置macOS下载配置（分架构模式）:', version);

    // 只处理分架构模式
    const m1Input = document.getElementById('dmg-m1-download-url');
    const intelInput = document.getElementById('dmg-intel-download-url');
    const m1BackupInput = document.getElementById('dmg-m1-backup-url');
    const intelBackupInput = document.getElementById('dmg-intel-backup-url');

    if (version.dmg_m1_download_url && m1Input) {
        m1Input.value = version.dmg_m1_download_url;
    }
    if (version.dmg_intel_download_url && intelInput) {
        intelInput.value = version.dmg_intel_download_url;
    }
    if (version.dmg_m1_backup_url && m1BackupInput) {
        m1BackupInput.value = version.dmg_m1_backup_url;
    }
    if (version.dmg_intel_backup_url && intelBackupInput) {
        intelBackupInput.value = version.dmg_intel_backup_url;
    }
}

// 初始化更新模块
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM加载完成，开始初始化更新模块...');

    try {
        console.log('初始化Word编辑器...');
        initUpdateWordEditor();
        console.log('Word编辑器初始化完成');
    } catch (error) {
        console.error('Word编辑器初始化失败:', error);
    }

    try {
        console.log('初始化文件上传...');
        initFileUploads();
        console.log('文件上传初始化完成');
    } catch (error) {
        console.error('文件上传初始化失败:', error);
    }

    try {
        console.log('初始化macOS架构支持...');
        initMacOSArchitectureSupport();
        console.log('macOS架构支持初始化完成');
    } catch (error) {
        console.error('macOS架构支持初始化失败:', error);
    }

    // 检查当前是否在更新标签页
    const currentTab = new URLSearchParams(window.location.search).get('tab') || 'popup';
    console.log('当前标签页:', currentTab);

    if (currentTab === 'update') {
        console.log('当前在更新标签页，立即加载版本列表...');
        try {
            loadVersionList();
            console.log('版本列表加载请求已发送');
        } catch (error) {
            console.error('版本列表加载失败:', error);
            // 如果版本列表加载失败，显示错误信息
            const listContainer = document.getElementById('version-list');
            if (listContainer) {
                listContainer.innerHTML = `
                    <div class="error-placeholder">
                        <i class="fas fa-exclamation-triangle"></i>
                        <span>初始化错误: ${error.message}</span>
                    </div>
                `;
            }
        }
    } else {
        console.log('当前不在更新标签页，等待标签切换...');
    }
});

// 监听标签切换事件
window.addEventListener('tabChanged', function(event) {
    console.log('标签切换事件:', event.detail);
    if (event.detail.tab === 'update') {
        console.log('切换到更新标签页，加载版本列表...');
        try {
            loadVersionList();
        } catch (error) {
            console.error('标签切换时版本列表加载失败:', error);
        }
    }
});

function initUpdateWordEditor() {
    // 初始化Word风格编辑器
    updateWordEditor = new WordStyleEditor('update-word-editor', {
        placeholder: '请输入本次更新的详细说明...',
        minHeight: '200px'
    });

    // 监听内容变化，同步到隐藏字段
    const editorContent = document.querySelector('#update-word-editor .word-editor-content');
    if (editorContent) {
        editorContent.addEventListener('input', function() {
            const htmlContent = this.innerHTML;
            document.getElementById('update-description').value = htmlContent;
        });
    }
}

// 修复Quill编辑器下拉菜单位置的强力函数
function fixQuillDropdownPosition(editorSelector) {
    const editor = document.querySelector(editorSelector);
    if (!editor) return;

    const toolbar = editor.querySelector('.ql-toolbar');
    if (!toolbar) return;

    // 强制设置容器样式
    const container = editor.closest('.quill-editor-container');
    if (container) {
        container.style.overflow = 'visible';
        container.style.position = 'relative';
    }
    toolbar.style.overflow = 'visible';
    toolbar.style.position = 'relative';

    // 使用MutationObserver监听DOM变化
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.type === 'childList' || mutation.type === 'attributes') {
                // 查找所有显示的下拉菜单
                const visibleOptions = toolbar.querySelectorAll('.ql-picker-options[style*="block"]');
                visibleOptions.forEach(options => {
                    forceDropdownPosition(options, toolbar);
                });
            }
        });
    });

    // 开始观察
    observer.observe(toolbar, {
        childList: true,
        subtree: true,
        attributes: true,
        attributeFilter: ['style', 'class']
    });

    // 监听所有picker按钮的点击
    const pickers = toolbar.querySelectorAll('.ql-picker');
    pickers.forEach(picker => {
        const label = picker.querySelector('.ql-picker-label');
        if (label) {
            label.addEventListener('click', function() {
                setTimeout(() => {
                    const options = picker.querySelector('.ql-picker-options');
                    if (options) {
                        forceDropdownPosition(options, toolbar);
                    }
                }, 1);
            });
        }
    });

    // 定期检查并修复位置
    setInterval(() => {
        const visibleOptions = toolbar.querySelectorAll('.ql-picker-options');
        visibleOptions.forEach(options => {
            if (options.style.display !== 'none' && options.offsetParent !== null) {
                forceDropdownPosition(options, toolbar);
            }
        });
    }, 100);
}

// 强制设置下拉菜单位置
function forceDropdownPosition(options, toolbar) {
    if (!options || !toolbar) return;

    // 强制重置所有可能影响位置的样式
    options.style.position = 'absolute';
    options.style.zIndex = '99999';
    options.style.top = 'calc(100% + 2px)';
    options.style.left = '0';
    options.style.right = 'auto';
    options.style.bottom = 'auto';
    options.style.transform = 'none';
    options.style.margin = '0';
    options.style.marginTop = '2px';

    // 确保在容器内显示
    setTimeout(() => {
        const toolbarRect = toolbar.getBoundingClientRect();
        const optionsRect = options.getBoundingClientRect();

        // 如果右侧超出工具栏，调整到右对齐
        if (optionsRect.right > toolbarRect.right) {
            options.style.left = 'auto';
            options.style.right = '0';
        }

        // 如果下方超出屏幕，调整到上方显示
        if (optionsRect.bottom > window.innerHeight - 20) {
            options.style.top = 'auto';
            options.style.bottom = 'calc(100% + 2px)';
        }
    }, 1);
}

// 强制将下拉菜单移动到工具栏容器内
function forceDropdownsInContainer(editorSelector) {
    const editor = document.querySelector(editorSelector);
    if (!editor) return;

    const toolbar = editor.querySelector('.ql-toolbar');
    const container = editor.closest('.quill-editor-container');
    if (!toolbar || !container) return;

    // 设置容器样式
    container.style.position = 'relative';
    container.style.overflow = 'visible';
    toolbar.style.position = 'relative';
    toolbar.style.overflow = 'visible';

    // 监听所有picker的点击事件
    const pickers = toolbar.querySelectorAll('.ql-picker');
    pickers.forEach((picker, index) => {
        const label = picker.querySelector('.ql-picker-label');
        if (!label) return;

        label.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            // 关闭其他所有下拉菜单
            pickers.forEach(otherPicker => {
                if (otherPicker !== picker) {
                    const otherOptions = otherPicker.querySelector('.ql-picker-options');
                    if (otherOptions) {
                        otherOptions.style.display = 'none';
                        otherPicker.classList.remove('ql-expanded');
                    }
                }
            });

            // 切换当前下拉菜单
            const options = picker.querySelector('.ql-picker-options');
            if (!options) return;

            const isExpanded = picker.classList.contains('ql-expanded');

            if (isExpanded) {
                // 关闭下拉菜单
                options.style.display = 'none';
                picker.classList.remove('ql-expanded');
            } else {
                // 打开下拉菜单
                picker.classList.add('ql-expanded');
                options.style.display = 'block';

                // 强制设置位置 - 使用fixed定位确保不被父容器限制
                const labelRect = label.getBoundingClientRect();
                options.style.position = 'fixed';
                options.style.top = (labelRect.bottom + 2) + 'px';
                options.style.left = labelRect.left + 'px';
                options.style.right = 'auto';
                options.style.bottom = 'auto';
                options.style.zIndex = '999999';
                options.style.transform = 'none';
                options.style.margin = '0';
                options.classList.add('force-positioned');

                // 检查是否需要调整位置
                setTimeout(() => {
                    const optionsRect = options.getBoundingClientRect();
                    const viewportWidth = window.innerWidth;
                    const viewportHeight = window.innerHeight;

                    // 如果右侧超出屏幕，调整到左对齐
                    if (optionsRect.right > viewportWidth - 10) {
                        options.style.left = (labelRect.right - optionsRect.width) + 'px';
                    }

                    // 如果下方超出屏幕，调整到上方
                    if (optionsRect.bottom > viewportHeight - 10) {
                        options.style.top = (labelRect.top - optionsRect.height - 2) + 'px';
                    }
                }, 10);
            }
        });

        // 监听选项点击
        const options = picker.querySelector('.ql-picker-options');
        if (options) {
            options.addEventListener('click', function(e) {
                // 选择选项后关闭下拉菜单
                setTimeout(() => {
                    options.style.display = 'none';
                    picker.classList.remove('ql-expanded');
                }, 100);
            });
        }
    });

    // 点击其他地方关闭下拉菜单
    document.addEventListener('click', function(e) {
        if (!toolbar.contains(e.target)) {
            pickers.forEach(picker => {
                const options = picker.querySelector('.ql-picker-options');
                if (options) {
                    options.style.display = 'none';
                    picker.classList.remove('ql-expanded');
                }
            });
        }
    });
}

function initUpdateEditor() {
    const editor = document.getElementById('update-description-editor');
    const hiddenInput = document.getElementById('update-description');
    
    // 监听编辑器内容变化
    editor.addEventListener('input', function() {
        hiddenInput.value = editor.innerHTML;
    });
    
    // 初始化工具栏按钮
    document.querySelectorAll('.update-module .editor-toolbar .toolbar-btn').forEach(btn => {
        btn.addEventListener('click', function(e) {
            e.preventDefault();
            const command = this.dataset.command;
            
            if (command) {
                document.execCommand(command, false, null);
                editor.focus();
            }
        });
    });
    
    // 初始化选择框和颜色选择器
    document.querySelectorAll('.update-module .editor-toolbar .toolbar-select, .update-module .editor-toolbar .toolbar-color').forEach(input => {
        input.addEventListener('change', function() {
            const command = this.dataset.command;
            const value = this.value;
            
            if (command && value) {
                document.execCommand(command, false, value);
                editor.focus();
                if (this.tagName === 'SELECT') {
                    this.value = '';
                }
            }
        });
    });
}

function initFileUploads() {
    // EXE文件上传
    const exeInput = document.getElementById('exe-file');
    const exeArea = exeInput.nextElementSibling;
    const exePreview = document.getElementById('exe-preview');
    
    // DMG文件上传
    const dmgInput = document.getElementById('dmg-file');
    const dmgArea = dmgInput.nextElementSibling;
    const dmgPreview = document.getElementById('dmg-preview');
    
    // 设置文件上传处理
    setupFileUpload(exeInput, exeArea, exePreview, '.exe');
    setupFileUpload(dmgInput, dmgArea, dmgPreview, '.dmg');
}

function setupFileUpload(input, area, preview, extension) {
    // 文件选择事件
    input.addEventListener('change', function() {
        handleFileSelect(this, preview, extension);
    });
    
    // 拖拽事件
    area.addEventListener('dragover', function(e) {
        e.preventDefault();
        this.classList.add('dragover');
    });
    
    area.addEventListener('dragleave', function(e) {
        e.preventDefault();
        this.classList.remove('dragover');
    });
    
    area.addEventListener('drop', function(e) {
        e.preventDefault();
        this.classList.remove('dragover');
        
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            const file = files[0];
            if (file.name.toLowerCase().endsWith(extension)) {
                input.files = files;
                handleFileSelect(input, preview, extension);
            } else {
                showToast(`请选择${extension}文件`, 'warning');
            }
        }
    });
}

function handleFileSelect(input, preview, extension) {
    const file = input.files[0];
    if (file) {
        const fileName = file.name;
        const fileSize = formatFileSize(file.size);
        
        preview.innerHTML = `
            <i class="fas fa-file-archive"></i>
            <span>${fileName} (${fileSize})</span>
        `;
        preview.style.display = 'flex';
    } else {
        preview.style.display = 'none';
    }
}

function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 表单提交处理
document.getElementById('update-form').addEventListener('submit', function(e) {
    e.preventDefault();

    // 获取表单数据
    const version = document.getElementById('version-number').value.trim();
    const title = document.getElementById('version-title').value.trim();
    const exeUrl = document.getElementById('exe-download-url').value.trim();
    const exeBackupUrl = document.getElementById('exe-backup-url').value.trim();
    const forceUpdate = document.getElementById('force-update').value;

    // 从Word编辑器获取内容
    let description = '';
    if (updateWordEditor) {
        description = updateWordEditor.getContent();
    }

    // 验证必填字段
    if (!title) {
        showToast('请输入版本标题', 'error');
        return;
    }

    // 检查是否为编辑模式
    const isEditing = window.editingVersionId;
    const actionText = isEditing ? '更新' : '创建';
    const submitBtn = this.querySelector('button[type="submit"]');

    // 设置按钮加载状态
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = `<i class="fas fa-spinner fa-spin"></i> ${actionText}中...`;
    submitBtn.disabled = true;

    if (!description || description.trim() === '<p><br></p>' || description.trim() === '') {
        showToast('请输入更新说明', 'error');
        return;
    }

    if (!hasAnyDownloadUrl()) {
        showToast('请至少提供一个下载链接（Windows或macOS）', 'error');
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
        return;
    }

    // 验证URL格式
    if (exeUrl && !isValidUrl(exeUrl)) {
        showToast('Windows直链地址格式不正确', 'error');
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
        return;
    }
    
    if (exeBackupUrl && !isValidUrl(exeBackupUrl)) {
        showToast('Windows备用地址格式不正确', 'error');
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
        return;
    }

    // 构建FormData
    const formData = new FormData();

    if (isEditing) {
        // 编辑模式
        formData.append('action', 'update');
        formData.append('id', window.editingVersionId);
    } else {
        // 创建模式
        formData.append('action', 'create');
        formData.append('status', 'published'); // 直接发布
        formData.append('platform', 'all');
    }

    if (version) formData.append('version', version);
    formData.append('title', title);
    formData.append('description', description);
    if (exeUrl) formData.append('exe_download_url', exeUrl);
    if (exeBackupUrl) formData.append('exe_backup_url', exeBackupUrl);

    // 处理macOS下载链接（仅支持分架构版本）
    const macosData = getMacOSDownloadData();
    if (macosData.urls.m1) {
        formData.append('dmg_m1_download_url', macosData.urls.m1);
    }
    if (macosData.urls.intel) {
        formData.append('dmg_intel_download_url', macosData.urls.intel);
    }
    if (macosData.backupUrls.m1) {
        formData.append('dmg_m1_backup_url', macosData.backupUrls.m1);
    }
    if (macosData.backupUrls.intel) {
        formData.append('dmg_intel_backup_url', macosData.backupUrls.intel);
    }
    formData.append('macos_architecture_support', 'separate');

    formData.append('force_update', forceUpdate);

    // 发送到独立API
    fetch('../../../app_update_standalone.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            if (isEditing) {
                showToast('版本更新成功！', 'success');
                cancelEdit(); // 退出编辑模式
            } else {
                showToast('版本创建成功！', 'success');
                resetUpdateForm();
            }
            loadVersionList();
        } else {
            showToast(data.message || `${actionText}失败，请重试`, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('网络错误，请重试', 'error');
    })
    .finally(() => {
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    });
});

// URL验证函数
function isValidUrl(string) {
    try {
        new URL(string);
        return true;
    } catch (_) {
        return false;
    }
}

// 重置表单（仅保留分架构字段）
function resetUpdateForm() {
    document.getElementById('update-form').reset();
    document.getElementById('update-description').value = '';

    // 清空Word编辑器内容
    if (updateWordEditor) {
        updateWordEditor.clear();
    }

    // 清空所有URL输入框（仅分架构版本）
    document.getElementById('exe-download-url').value = '';
    document.getElementById('exe-backup-url').value = '';
    document.getElementById('dmg-m1-download-url').value = '';
    document.getElementById('dmg-intel-download-url').value = '';
    document.getElementById('dmg-m1-backup-url').value = '';
    document.getElementById('dmg-intel-backup-url').value = '';
}

// 加载版本列表
function loadVersionList() {
    console.log('loadVersionList函数被调用');

    const listContainer = document.getElementById('version-list');
    if (!listContainer) {
        console.error('找不到version-list容器元素');
        return;
    }

    console.log('开始发送API请求: ../../../app_update_standalone.php?action=list');

    fetch('../../../app_update_standalone.php?action=list')
    .then(response => {
        console.log('收到API响应，状态码:', response.status);
        console.log('响应头:', response.headers);

        if (!response.ok) {
            throw new Error(`HTTP错误: ${response.status}`);
        }

        return response.text();
    })
    .then(responseText => {
        console.log('原始响应内容:', responseText.substring(0, 500) + '...');

        let data;
        try {
            data = JSON.parse(responseText);
        } catch (parseError) {
            console.error('JSON解析错误:', parseError);
            throw new Error('响应不是有效的JSON格式');
        }

        console.log('解析后的数据:', data);

        if (data.success) {
            console.log('API调用成功，版本数量:', data.data.versions.length);
            displayVersionList(data.data.versions);

            // 显示/隐藏一键删除按钮
            const deleteAllBtn = document.getElementById('delete-all-versions-btn');
            if (deleteAllBtn) {
                if (data.data.versions && data.data.versions.length > 0) {
                    deleteAllBtn.style.display = 'inline-block';
                } else {
                    deleteAllBtn.style.display = 'none';
                }
            }
        } else {
            console.error('API返回错误:', data.message);
            listContainer.innerHTML = `
                <div class="error-placeholder">
                    <i class="fas fa-exclamation-triangle"></i>
                    <span>加载失败：${data.message}</span>
                </div>
            `;
            // 隐藏一键删除按钮
            const deleteAllBtn = document.getElementById('delete-all-versions-btn');
            if (deleteAllBtn) {
                deleteAllBtn.style.display = 'none';
            }
        }
    })
    .catch(error => {
        console.error('版本列表加载错误:', error);
        listContainer.innerHTML = `
            <div class="error-placeholder">
                <i class="fas fa-exclamation-triangle"></i>
                <span>网络错误：${error.message}</span>
                <br><small>请检查浏览器控制台获取详细信息</small>
            </div>
        `;

        // 隐藏一键删除按钮
        const deleteAllBtn = document.getElementById('delete-all-versions-btn');
        if (deleteAllBtn) {
            deleteAllBtn.style.display = 'none';
        }
    });
}

// 显示版本列表
function displayVersionList(versions) {
    const listContainer = document.getElementById('version-list');
    
    if (!versions || versions.length === 0) {
        listContainer.innerHTML = `
            <div class="empty-placeholder">
                <i class="fas fa-download"></i>
                <span>暂无版本，点击上方创建第一个版本</span>
            </div>
        `;
        return;
    }
    
    const versionItems = versions.map(version => `
        <div class="version-item" data-id="${version.id}">
            <div class="version-item-header">
                <div class="version-info">
                    <span class="version-number">v${version.version}</span>
                    <h4 class="version-title">${version.title}</h4>
                </div>
                <div class="version-status">
                    <span class="status-badge ${version.status}">${version.status === 'published' ? '已发布' : '草稿'}</span>
                    <!-- 所有版本默认为强制更新，不再显示标识 -->
                </div>
            </div>
            <div class="version-meta">
                <span><i class="fas fa-calendar"></i> 创建时间：${version.created_at}</span>
            </div>
            <div class="version-description">
                ${version.description.replace(/<[^>]*>/g, '').substring(0, 100)}...
            </div>
            <div class="version-files">
                ${version.exe_download_url ? `<span class="file-info"><i class="fab fa-windows"></i> Windows版本 ${version.exe_backup_url ? '(含备用)' : ''}</span>` : ''}
                ${version.dmg_download_url ? `<span class="file-info"><i class="fab fa-apple"></i> macOS版本 ${version.dmg_backup_url ? '(含备用)' : ''}</span>` : ''}
                ${version.dmg_m1_download_url ? `<span class="file-info"><i class="fas fa-microchip"></i> M芯片版本 ${version.dmg_m1_backup_url ? '(含备用)' : ''}</span>` : ''}
                ${version.dmg_intel_download_url ? `<span class="file-info"><i class="fas fa-desktop"></i> Intel版本 ${version.dmg_intel_backup_url ? '(含备用)' : ''}</span>` : ''}
            </div>
            <div class="version-links">
                ${version.exe_download_url ? `
                    <div class="download-link">
                        <i class="fab fa-windows"></i> 
                        <a href="${version.exe_download_url}" target="_blank" rel="noopener">Windows直链</a>
                        ${version.exe_backup_url ? ` | <a href="${version.exe_backup_url}" target="_blank" rel="noopener">备用地址</a>` : ''}
                    </div>
                ` : ''}
                ${version.dmg_download_url ? `
                    <div class="download-link">
                        <i class="fab fa-apple"></i> 
                        <a href="${version.dmg_download_url}" target="_blank" rel="noopener">macOS直链</a>
                        ${version.dmg_backup_url ? ` | <a href="${version.dmg_backup_url}" target="_blank" rel="noopener">备用地址</a>` : ''}
                    </div>
                ` : ''}
                ${version.dmg_m1_download_url ? `
                    <div class="download-link">
                        <i class="fas fa-microchip"></i> 
                        <a href="${version.dmg_m1_download_url}" target="_blank" rel="noopener">M芯片直链</a>
                        ${version.dmg_m1_backup_url ? ` | <a href="${version.dmg_m1_backup_url}" target="_blank" rel="noopener">备用地址</a>` : ''}
                    </div>
                ` : ''}
                ${version.dmg_intel_download_url ? `
                    <div class="download-link">
                        <i class="fas fa-desktop"></i> 
                        <a href="${version.dmg_intel_download_url}" target="_blank" rel="noopener">Intel直链</a>
                        ${version.dmg_intel_backup_url ? ` | <a href="${version.dmg_intel_backup_url}" target="_blank" rel="noopener">备用地址</a>` : ''}
                    </div>
                ` : ''}
            </div>
            <div class="version-actions">
                ${version.status === 'draft' ?
                    `<button class="btn btn-success btn-sm" onclick="publishVersion(${version.id})">
                        <i class="fas fa-paper-plane"></i> 发布
                    </button>` :
                    `<button class="btn btn-warning btn-sm" onclick="unpublishVersion(${version.id})">
                        <i class="fas fa-undo"></i> 撤回
                    </button>`
                }
                <button class="btn btn-primary btn-sm" onclick="editVersion(${version.id})">
                    <i class="fas fa-edit"></i> 编辑
                </button>
                <button class="btn btn-danger btn-sm" onclick="deleteVersion(${version.id})">
                    <i class="fas fa-trash"></i> 删除
                </button>
            </div>
        </div>
    `).join('');
    
    listContainer.innerHTML = versionItems;
}

// 发布版本
function publishVersion(id) {
    showConfirm('确定要发布这个版本吗？发布后用户将可以下载更新。', function() {
        const formData = new FormData();
        formData.append('action', 'publish');
        formData.append('id', id);

        fetch('../../../app_update_standalone.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showToast('版本发布成功！', 'success');
                loadVersionList();
            } else {
                showToast(data.message || '发布失败', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showToast('网络错误', 'error');
        });
    });
}

// 撤回版本
function unpublishVersion(id) {
    showConfirm('确定要撤回这个版本吗？撤回后将变为草稿状态。', function() {
        const formData = new FormData();
        formData.append('action', 'unpublish');
        formData.append('id', id);

        fetch('../../../app_update_standalone.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showToast('版本撤回成功！', 'info');
                loadVersionList();
            } else {
                showToast(data.message || '撤回失败', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showToast('网络错误', 'error');
        });
    });
}



// 编辑版本 - 直接在创建版本框内编辑
function editVersion(id) {
    console.log('编辑版本，ID:', id);

    // 获取版本信息
    fetch('../../../app_update_standalone.php?action=list')
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const version = data.data.versions.find(v => v.id == id);
            if (version) {
                loadVersionToForm(version);
                // 滚动到表单顶部
                document.querySelector('.update-form').scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            } else {
                showToast('找不到版本信息', 'error');
            }
        } else {
            showToast('获取版本信息失败', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('网络错误', 'error');
    });
}

// 将版本数据加载到表单中（支持备用地址）
function loadVersionToForm(version) {
    // 设置编辑模式
    window.editingVersionId = version.id;

    // 填充表单字段
    document.getElementById('version-number').value = version.version || '';
    document.getElementById('version-title').value = version.title || '';
    document.getElementById('exe-download-url').value = version.exe_download_url || '';
    document.getElementById('exe-backup-url').value = version.exe_backup_url || '';
    document.getElementById('force-update').value = version.force_update ? '1' : '0';

    // 设置macOS下载配置
    setMacOSDownloadConfig(version);

    // 设置Word编辑器内容
    if (updateWordEditor && version.description) {
        updateWordEditor.setContent(version.description);
    }

    // 更新按钮文本
    const submitBtn = document.querySelector('#update-form button[type="submit"]');
    if (submitBtn) {
        submitBtn.innerHTML = '<i class="fas fa-save"></i> 更新版本';
        submitBtn.classList.remove('btn-primary');
        submitBtn.classList.add('btn-warning');
    }

    // 添加取消编辑按钮
    if (!document.getElementById('cancel-edit-btn')) {
        const cancelBtn = document.createElement('button');
        cancelBtn.type = 'button';
        cancelBtn.id = 'cancel-edit-btn';
        cancelBtn.className = 'btn btn-secondary';
        cancelBtn.innerHTML = '<i class="fas fa-times"></i> 取消编辑';
        cancelBtn.onclick = cancelEdit;
        submitBtn.parentNode.insertBefore(cancelBtn, submitBtn);
    }

    showToast('版本信息已加载到编辑表单', 'success');
}

// 取消编辑（仅清空分架构字段）
function cancelEdit() {
    // 清除编辑模式
    window.editingVersionId = null;

    // 清空表单
    document.getElementById('update-form').reset();
    if (updateWordEditor) {
        updateWordEditor.setContent('');
    }

    // 清空所有URL输入框（仅分架构版本）
    document.getElementById('exe-download-url').value = '';
    document.getElementById('exe-backup-url').value = '';
    document.getElementById('dmg-m1-download-url').value = '';
    document.getElementById('dmg-intel-download-url').value = '';
    document.getElementById('dmg-m1-backup-url').value = '';
    document.getElementById('dmg-intel-backup-url').value = '';

    // 恢复按钮状态
    const submitBtn = document.querySelector('#update-form button[type="submit"]');
    if (submitBtn) {
        submitBtn.innerHTML = '<i class="fas fa-plus"></i> 创建版本';
        submitBtn.classList.remove('btn-warning');
        submitBtn.classList.add('btn-primary');
    }

    // 移除取消按钮
    const cancelBtn = document.getElementById('cancel-edit-btn');
    if (cancelBtn) {
        cancelBtn.remove();
    }

    showToast('已取消编辑', 'info');
}

// 注意：编辑功能已改为直接在创建表单中进行，不再使用模态框

// 删除版本
function deleteVersion(id) {
    showConfirm('确定要删除这个版本吗？此操作不可恢复！', function() {
        const formData = new FormData();
        formData.append('action', 'delete');
        formData.append('id', id);

        fetch('../../../app_update_standalone.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showToast('版本删除成功！', 'success');
                loadVersionList();
            } else {
                showToast(data.message || '删除失败', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showToast('网络错误', 'error');
        });
    });
}

// 一键删除所有版本
function deleteAllVersions() {
    showConfirm('确定要删除所有版本吗？此操作将删除所有版本记录和相关文件，不可恢复！', function() {
        const formData = new FormData();
        formData.append('action', 'delete_all');

        fetch('../../../app_update_standalone.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showToast(`成功删除 ${data.deleted_count || 0} 个版本和 ${data.deleted_files || 0} 个文件！`, 'success');
                loadVersionList(); // 重新加载列表
                // 重置表单
                resetUpdateForm();
            } else {
                showToast(data.message || '删除失败', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showToast('网络错误，请重试', 'error');
        });
    });
}

// 蓝奏云解析功能
function parseLanzouUrl() {
    const urlInput = document.getElementById('lanzou-url');
    const resultDiv = document.getElementById('lanzou-result');
    const url = urlInput.value.trim();
    
    if (!url) {
        showToast('请输入蓝奏云分享链接', 'warning');
        return;
    }
    
    // 验证URL格式
    if (!isValidLanzouUrl(url)) {
        showToast('请输入有效的蓝奏云链接', 'error');
        return;
    }
    
    // 显示加载状态
    resultDiv.className = 'lanzou-result loading';
    resultDiv.style.display = 'block';
    resultDiv.innerHTML = `
        <h4><i class="fas fa-spinner fa-spin"></i> 正在解析蓝奏云链接...</h4>
        <p>请稍候，正在获取文件信息和直链地址...</p>
    `;
    
    // 构建API URL - 使用正确的API接口
    const apiUrl = '../../../lanzou_api.php?url=' + encodeURIComponent(url) + '&type=parse';
    
    // 发送请求
    fetch(apiUrl)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            return response.json();
        })
        .then(data => {
            console.log('蓝奏云API响应:', data);
            
            if (data.success) {
                const hasDirectLink = data.fileUrl && (
                    data.fileUrl.includes('developer-oss.lanrar.com') ||
                    data.fileUrl.includes('vip.d0.baidupan.com') ||
                    data.fileUrl.includes('pc.woozooo.com') ||
                    data.fileUrl.includes('webgetstore.com')
                );
                
                resultDiv.className = hasDirectLink ? 'lanzou-result' : 'lanzou-result error';
                resultDiv.innerHTML = `
                    <h4><i class="fas fa-${hasDirectLink ? 'check-circle' : 'exclamation-triangle'}"></i> 解析${hasDirectLink ? '成功' : '完成'}</h4>
                    
                    <div class="lanzou-file-info">
                        <strong><i class="fas fa-file"></i> 文件名:</strong> 
                        <span>${data.info.file_name || '未知文件名'}</span>
                        
                        <strong><i class="fas fa-hdd"></i> 文件大小:</strong> 
                        <span>${data.info.file_size || '未知大小'}</span>
                        
                        <strong><i class="fas fa-link"></i> 直链状态:</strong> 
                        <span style="color: ${hasDirectLink ? '#28a745' : '#dc3545'}; font-weight: bold;">
                            ${hasDirectLink ? '✅ 成功获取真实直链' : '❌ 未获取到有效直链'}
                        </span>
                    </div>
                    
                    ${hasDirectLink ? `
                        <div style="margin: 15px 0;">
                            <strong><i class="fas fa-download"></i> 直链地址:</strong>
                            <div class="lanzou-direct-link-container">
                                <div class="lanzou-direct-link" title="${data.fileUrl}">${data.fileUrl}</div>
                                <button class="copy-link-btn" onclick="copyToClipboard('${data.fileUrl.replace(/'/g, "\\'")}', this)" title="复制链接">
                                    <i class="fas fa-copy"></i>
                                </button>
                            </div>
                        </div>
                        
                        <div style="margin: 15px 0;">
                            <a href="${data.fileUrl}" target="_blank" class="lanzou-download-btn">
                                <i class="fas fa-download"></i> 直接下载
                            </a>
                            <a href="../../../lanzou_api.php?url=${encodeURIComponent(url)}&type=down" target="_blank" class="lanzou-download-btn">
                                <i class="fas fa-external-link-alt"></i> 通过API下载
                            </a>
                        </div>
                    ` : `
                        <p style="color: #dc3545; margin: 10px 0;">
                            <i class="fas fa-info-circle"></i> 可能的原因：链接已过期、文件被删除或服务器无法访问
                        </p>
                    `}
                    
                    <div style="margin-top: 15px; padding-top: 15px; border-top: 1px solid rgba(255,255,255,0.2);">
                        <small style="color: rgba(255,255,255,0.6);">
                            <i class="fas fa-clock"></i> 解析时间: ${new Date().toLocaleString()}
                        </small>
                    </div>
                `;
                
                if (hasDirectLink) {
                    showToast('蓝奏云直链解析成功！', 'success');
                } else {
                    showToast('解析完成，但未获取到有效直链', 'warning');
                }
            } else {
                resultDiv.className = 'lanzou-result error';
                resultDiv.innerHTML = `
                    <h4><i class="fas fa-exclamation-triangle"></i> 解析失败</h4>
                    <p><strong>错误信息:</strong> ${data.error}</p>
                    <p style="color: rgba(255,255,255,0.7); font-size: 13px;">
                        请检查链接是否正确，或稍后重试
                    </p>
                `;
                showToast('蓝奏云链接解析失败', 'error');
            }
        })
        .catch(error => {
            console.error('蓝奏云API请求错误:', error);
            resultDiv.className = 'lanzou-result error';
            resultDiv.innerHTML = `
                <h4><i class="fas fa-exclamation-triangle"></i> 请求失败</h4>
                <p><strong>错误信息:</strong> ${error.message}</p>
                <p style="color: rgba(255,255,255,0.7); font-size: 13px;">
                    请检查网络连接或API服务是否正常
                </p>
            `;
            showToast('网络请求失败，请重试', 'error');
        });
}

// 清空蓝奏云解析结果
function clearLanzouResult() {
    const urlInput = document.getElementById('lanzou-url');
    const resultDiv = document.getElementById('lanzou-result');

    urlInput.value = '';
    resultDiv.style.display = 'none';
    resultDiv.innerHTML = '';

    showToast('已清空解析结果', 'info');
}

// 复制到剪贴板
function copyToClipboard(text, button) {
    navigator.clipboard.writeText(text).then(function() {
        // 成功复制
        const icon = button.querySelector('i');
        const originalClass = icon.className;

        // 更改按钮样式
        button.classList.add('copied');
        icon.className = 'fas fa-check';

        showToast('直链已复制到剪贴板', 'success');

        // 2秒后恢复原样
        setTimeout(() => {
            button.classList.remove('copied');
            icon.className = originalClass;
        }, 2000);
    }).catch(function(err) {
        console.error('复制失败:', err);
        showToast('复制失败，请手动复制', 'error');
    });
}

// 验证蓝奏云URL
function isValidLanzouUrl(url) {
    if (!url || typeof url !== 'string') {
        return false;
    }
    
    try {
        const urlObj = new URL(url);
        const validDomains = [
            'lanzoue.com', 'lanzou.com', 'lanzoul.com', 
            'lanzoux.com', 'lanzoui.com', 'woozooo.com'
        ];
        
        return validDomains.some(domain => urlObj.hostname.includes(domain));
    } catch (e) {
        return false;
    }
}

// 蓝奏云URL自动转换为API格式
function convertLanzouUrlToApi(url) {
    if (!url || !isValidLanzouUrl(url)) {
        return url;
    }
    
    // 检查是否已经是API格式
    if (url.includes('xiaomeihuakefu.cn/lanzou_api.php')) {
        return url;
    }

    // 转换为API格式 - 使用正确的API接口
    const apiUrl = `https://xiaomeihuakefu.cn/lanzou_api.php?url=${encodeURIComponent(url)}&type=down`;
    return apiUrl;
}

// 处理URL输入框的蓝奏云链接自动转换
function handleLanzouUrlConversion(inputElement) {
    const originalUrl = inputElement.value.trim();
    
    if (!originalUrl) {
        return;
    }
    
    if (isValidLanzouUrl(originalUrl)) {
        const apiUrl = convertLanzouUrlToApi(originalUrl);
        
        if (apiUrl !== originalUrl) {
            inputElement.value = apiUrl;
            
            // 显示转换提示
            showToast(`蓝奏云链接已自动转换为API格式`, 'success');
            
            // 触发验证
            validateUrlInput(inputElement);
        }
    }
}
</script>