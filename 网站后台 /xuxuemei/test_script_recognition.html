<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>脚本识别功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        textarea {
            height: 200px;
            font-family: monospace;
        }
        .test-script {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .success {
            background-color: #e8f5e8;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>脚本识别功能测试</h1>
        <p>这个页面用于测试脚本名称和版本号的自动识别功能。</p>
        
        <div class="form-group">
            <label>脚本名称</label>
            <input type="text" id="scriptName" placeholder="脚本名称将自动填充">
        </div>
        
        <div class="form-group">
            <label>版本号</label>
            <input type="text" id="scriptVersion" placeholder="版本号将自动填充">
        </div>
        
        <div class="form-group">
            <label>脚本代码</label>
            <textarea id="scriptCode" placeholder="在此粘贴您的脚本代码..."></textarea>
        </div>
        
        <div>
            <button onclick="loadTestScript1()">加载测试脚本1</button>
            <button onclick="loadTestScript2()">加载测试脚本2</button>
            <button onclick="clearAll()">清空所有</button>
        </div>
        
        <h3>测试脚本示例：</h3>
        <div class="test-script">// ==UserScript==
// @name         小梅花AI智能客服
// @version      1.2.3
// @description  智能客服助手
// <AUTHOR>
// ==/UserScript==

(function() {
    'use strict';
    console.log('小梅花AI智能客服 (v1.2.3) 已加载');
})();</div>
    </div>

    <script>
        // 复制脚本识别逻辑
        function processScript() {
            const codeTextarea = document.getElementById('scriptCode');
            const nameInput = document.getElementById('scriptName');
            const versionInput = document.getElementById('scriptVersion');

            let code = codeTextarea.value;

            if (!code.trim()) {
                return;
            }
            
            console.log('开始处理脚本代码，长度:', code.length);

            // 提取脚本名称
            const nameMatch = code.match(/@name\s+(.+?)(?:\r?\n|$)/i);
            console.log('名称匹配结果:', nameMatch);
            if (nameMatch) {
                const extractedName = nameMatch[1].trim();
                console.log('提取到的脚本名称:', extractedName);
                if (extractedName) {
                    nameInput.value = extractedName;
                    nameInput.style.backgroundColor = '#e8f5e8';
                    setTimeout(() => {
                        nameInput.style.backgroundColor = '';
                    }, 1000);
                    console.log('脚本名称已自动填充:', extractedName);
                }
            } else {
                console.log('未找到脚本名称');
            }

            // 提取版本号
            const versionMatch = code.match(/@version\s+(.+?)(?:\r?\n|$)/i);
            console.log('版本号匹配结果:', versionMatch);
            if (versionMatch) {
                const extractedVersion = versionMatch[1].trim();
                console.log('提取到的版本号:', extractedVersion);
                if (extractedVersion) {
                    versionInput.value = extractedVersion;
                    versionInput.style.backgroundColor = '#e8f5e8';
                    setTimeout(() => {
                        versionInput.style.backgroundColor = '';
                    }, 1000);
                    console.log('版本号已自动填充:', extractedVersion);
                }
            } else {
                console.log('未找到版本号');
            }
        }

        // 事件监听器
        document.getElementById('scriptCode').addEventListener('paste', function(e) {
            setTimeout(() => {
                const code = this.value;
                if (code.includes('// ==UserScript==') || code.includes('@name') || code.includes('@version')) {
                    setTimeout(processScript, 100);
                }
            }, 50);
        });

        document.getElementById('scriptCode').addEventListener('input', function(e) {
            const code = this.value;
            if (code.includes('// ==UserScript==') || code.includes('@name') || code.includes('@version')) {
                clearTimeout(window.scriptInputTimeout);
                window.scriptInputTimeout = setTimeout(() => {
                    processScript();
                }, 300);
            }
        });

        document.getElementById('scriptCode').addEventListener('keyup', function(e) {
            const code = this.value;
            if (code.includes('// ==UserScript==') || code.includes('@name') || code.includes('@version')) {
                clearTimeout(window.scriptKeyupTimeout);
                window.scriptKeyupTimeout = setTimeout(() => {
                    processScript();
                }, 200);
            }
        });

        // 测试函数
        function loadTestScript1() {
            const testScript = `// ==UserScript==
// @name         小梅花AI智能客服
// @version      1.2.3
// @description  智能客服助手
// <AUTHOR>
// ==/UserScript==

(function() {
    'use strict';
    console.log('小梅花AI智能客服 (v1.2.3) 已加载');
})();`;
            document.getElementById('scriptCode').value = testScript;
            setTimeout(processScript, 100);
        }

        function loadTestScript2() {
            const testScript = `// ==UserScript==
// @name         测试脚本-升级版
// @version      2.0.1
// @description  这是一个测试脚本
// ==/UserScript==

(function() {
    console.log('测试脚本运行中...');
})();`;
            document.getElementById('scriptCode').value = testScript;
            setTimeout(processScript, 100);
        }

        function clearAll() {
            document.getElementById('scriptName').value = '';
            document.getElementById('scriptVersion').value = '';
            document.getElementById('scriptCode').value = '';
        }

        // 页面加载完成后的处理
        document.addEventListener('DOMContentLoaded', function() {
            console.log('测试页面已加载，自动识别功能已启用');
        });
    </script>
</body>
</html>
