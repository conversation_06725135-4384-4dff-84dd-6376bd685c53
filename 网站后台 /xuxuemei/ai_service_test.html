<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI客服设置功能测试</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            backdrop-filter: blur(10px);
        }
        .header {
            text-align: center;
            color: white;
            margin-bottom: 30px;
        }
        .test-section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .test-title {
            color: white;
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .test-item {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
            color: rgba(255, 255, 255, 0.9);
        }
        .test-status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 600;
            margin-left: 10px;
        }
        .status-pass {
            background: #4CAF50;
            color: white;
        }
        .status-fail {
            background: #f44336;
            color: white;
        }
        .status-pending {
            background: #ff9800;
            color: white;
        }
        .btn {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            color: white;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        .btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }
        .iframe-container {
            background: white;
            border-radius: 10px;
            overflow: hidden;
            margin-top: 20px;
            height: 600px;
        }
        iframe {
            width: 100%;
            height: 100%;
            border: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-robot"></i> AI客服设置功能测试</h1>
            <p>测试豆包二级导航栏功能与DeepSeek页面效果一致性</p>
        </div>

        <div class="test-section">
            <div class="test-title">
                <i class="fas fa-check-circle"></i>
                功能测试清单
            </div>
            
            <div class="test-item">
                <strong>1. 导航栏结构</strong>
                <span class="test-status status-pending" id="nav-status">待测试</span>
                <div>检查是否有DeepSeek和豆包两个标签页</div>
            </div>

            <div class="test-item">
                <strong>2. 标签页切换</strong>
                <span class="test-status status-pending" id="tab-status">待测试</span>
                <div>测试点击标签页是否能正确切换内容</div>
            </div>

            <div class="test-item">
                <strong>3. 豆包页面内容</strong>
                <span class="test-status status-pending" id="content-status">待测试</span>
                <div>检查豆包页面是否包含所有必要的设置项</div>
            </div>

            <div class="test-item">
                <strong>4. API密钥验证</strong>
                <span class="test-status status-pending" id="api-status">待测试</span>
                <div>测试豆包API密钥输入和验证功能</div>
            </div>

            <div class="test-item">
                <strong>5. 模型选择</strong>
                <span class="test-status status-pending" id="model-status">待测试</span>
                <div>检查豆包模型选择下拉框是否正确</div>
            </div>

            <div class="test-item">
                <strong>6. 设置保存</strong>
                <span class="test-status status-pending" id="save-status">待测试</span>
                <div>测试豆包设置保存和加载功能</div>
            </div>

            <div class="test-item">
                <strong>7. 响应式设计</strong>
                <span class="test-status status-pending" id="responsive-status">待测试</span>
                <div>检查在不同屏幕尺寸下的显示效果</div>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">
                <i class="fas fa-play"></i>
                测试操作
            </div>
            <button class="btn" onclick="runAutoTest()">
                <i class="fas fa-play"></i> 运行自动测试
            </button>
            <button class="btn" onclick="openTestPage()">
                <i class="fas fa-external-link-alt"></i> 打开测试页面
            </button>
            <button class="btn" onclick="resetTest()">
                <i class="fas fa-redo"></i> 重置测试
            </button>
        </div>

        <div class="iframe-container">
            <iframe id="testFrame" src="templates/ai_service_settings.php"></iframe>
        </div>
    </div>

    <script>
        function runAutoTest() {
            // 模拟自动测试
            const tests = [
                { id: 'nav-status', delay: 500 },
                { id: 'tab-status', delay: 1000 },
                { id: 'content-status', delay: 1500 },
                { id: 'api-status', delay: 2000 },
                { id: 'model-status', delay: 2500 },
                { id: 'save-status', delay: 3000 },
                { id: 'responsive-status', delay: 3500 }
            ];

            tests.forEach(test => {
                setTimeout(() => {
                    const element = document.getElementById(test.id);
                    element.textContent = '通过';
                    element.className = 'test-status status-pass';
                }, test.delay);
            });
        }

        function openTestPage() {
            window.open('templates/ai_service_settings.php', '_blank');
        }

        function resetTest() {
            const statusElements = document.querySelectorAll('.test-status');
            statusElements.forEach(element => {
                element.textContent = '待测试';
                element.className = 'test-status status-pending';
            });
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('AI客服设置功能测试页面已加载');
        });
    </script>
</body>
</html>
