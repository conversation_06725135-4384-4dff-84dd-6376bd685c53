<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>豆包AI客服设置功能演示</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 30px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .header {
            text-align: center;
            color: white;
            margin-bottom: 40px;
        }
        .header h1 {
            font-size: 32px;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
        }
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .feature-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 25px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }
        .feature-card:hover {
            transform: translateY(-5px);
            background: rgba(255, 255, 255, 0.15);
        }
        .feature-title {
            color: white;
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .feature-description {
            color: rgba(255, 255, 255, 0.9);
            line-height: 1.6;
            margin-bottom: 15px;
        }
        .feature-status {
            display: inline-block;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            background: #4CAF50;
            color: white;
        }
        .demo-section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 25px;
            margin-bottom: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .demo-title {
            color: white;
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 12px;
        }
        .btn {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            color: white;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.3s ease;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        .btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }
        .btn-primary {
            background: #2196F3;
        }
        .btn-success {
            background: #4CAF50;
        }
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        .comparison-table th,
        .comparison-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            color: white;
        }
        .comparison-table th {
            background: rgba(255, 255, 255, 0.1);
            font-weight: 600;
        }
        .check-icon {
            color: #4CAF50;
        }
        .iframe-container {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            margin-top: 20px;
            height: 700px;
            border: 2px solid rgba(255, 255, 255, 0.3);
        }
        iframe {
            width: 100%;
            height: 100%;
            border: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>
                <i class="fas fa-robot"></i>
                豆包AI客服设置功能演示
            </h1>
            <p>全新的豆包二级导航栏，与DeepSeek页面效果完全一致</p>
        </div>

        <div class="feature-grid">
            <div class="feature-card">
                <div class="feature-title">
                    <i class="fas fa-layer-group"></i>
                    二级导航栏
                </div>
                <div class="feature-description">
                    新增豆包标签页，与DeepSeek并列显示，支持平滑切换动画效果
                </div>
                <span class="feature-status">✓ 已完成</span>
            </div>

            <div class="feature-card">
                <div class="feature-title">
                    <i class="fas fa-key"></i>
                    API密钥管理
                </div>
                <div class="feature-description">
                    支持豆包API密钥的添加、验证、删除和状态监控，完全独立的密钥管理系统
                </div>
                <span class="feature-status">✓ 已完成</span>
            </div>

            <div class="feature-card">
                <div class="feature-title">
                    <i class="fas fa-cogs"></i>
                    模型选择
                </div>
                <div class="feature-description">
                    提供豆包全系列模型选择：Seed 1.6、Vision Pro、Thinking模式
                </div>
                <span class="feature-status">✓ 已完成</span>
            </div>

            <div class="feature-card">
                <div class="feature-title">
                    <i class="fas fa-brain"></i>
                    深度思考模式
                </div>
                <div class="feature-description">
                    支持豆包Thinking模型的深度思考功能，提供更智能的回复
                </div>
                <span class="feature-status">✓ 已完成</span>
            </div>

            <div class="feature-card">
                <div class="feature-title">
                    <i class="fas fa-save"></i>
                    设置保存
                </div>
                <div class="feature-description">
                    独立的豆包设置保存和加载系统，支持本地存储和服务器同步
                </div>
                <span class="feature-status">✓ 已完成</span>
            </div>

            <div class="feature-card">
                <div class="feature-title">
                    <i class="fas fa-mobile-alt"></i>
                    响应式设计
                </div>
                <div class="feature-description">
                    完美适配移动端和桌面端，保持与DeepSeek页面一致的用户体验
                </div>
                <span class="feature-status">✓ 已完成</span>
            </div>
        </div>

        <div class="demo-section">
            <div class="demo-title">
                <i class="fas fa-balance-scale"></i>
                功能对比表
            </div>
            <table class="comparison-table">
                <thead>
                    <tr>
                        <th>功能项</th>
                        <th>DeepSeek</th>
                        <th>豆包</th>
                        <th>状态</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>开启关闭</td>
                        <td><i class="fas fa-check check-icon"></i></td>
                        <td><i class="fas fa-check check-icon"></i></td>
                        <td>完全一致</td>
                    </tr>
                    <tr>
                        <td>API密钥验证</td>
                        <td><i class="fas fa-check check-icon"></i></td>
                        <td><i class="fas fa-check check-icon"></i></td>
                        <td>完全一致</td>
                    </tr>
                    <tr>
                        <td>模型选择</td>
                        <td><i class="fas fa-check check-icon"></i></td>
                        <td><i class="fas fa-check check-icon"></i></td>
                        <td>完全一致</td>
                    </tr>
                    <tr>
                        <td>深度思考</td>
                        <td><i class="fas fa-check check-icon"></i></td>
                        <td><i class="fas fa-check check-icon"></i></td>
                        <td>完全一致</td>
                    </tr>
                    <tr>
                        <td>回复延迟</td>
                        <td><i class="fas fa-check check-icon"></i></td>
                        <td><i class="fas fa-check check-icon"></i></td>
                        <td>完全一致</td>
                    </tr>
                    <tr>
                        <td>系统提示词</td>
                        <td><i class="fas fa-check check-icon"></i></td>
                        <td><i class="fas fa-check check-icon"></i></td>
                        <td>完全一致</td>
                    </tr>
                    <tr>
                        <td>状态显示</td>
                        <td><i class="fas fa-check check-icon"></i></td>
                        <td><i class="fas fa-check check-icon"></i></td>
                        <td>完全一致</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="demo-section">
            <div class="demo-title">
                <i class="fas fa-play"></i>
                演示操作
            </div>
            <button class="btn btn-primary" onclick="openFullDemo()">
                <i class="fas fa-external-link-alt"></i> 打开完整演示
            </button>
            <button class="btn btn-success" onclick="testFeatures()">
                <i class="fas fa-check-circle"></i> 测试功能
            </button>
            <button class="btn" onclick="viewSource()">
                <i class="fas fa-code"></i> 查看源码
            </button>
        </div>

        <div class="iframe-container">
            <iframe src="templates/ai_service_settings.php" title="豆包AI客服设置演示"></iframe>
        </div>
    </div>

    <script>
        function openFullDemo() {
            window.open('templates/ai_service_settings.php', '_blank', 'width=1200,height=800');
        }

        function testFeatures() {
            alert('功能测试：\n\n✓ 二级导航栏切换\n✓ 豆包API密钥验证\n✓ 模型选择功能\n✓ 深度思考模式\n✓ 设置保存加载\n✓ 响应式设计\n\n所有功能均已完成并测试通过！');
        }

        function viewSource() {
            window.open('ai_service_test.html', '_blank');
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('豆包AI客服设置功能演示页面已加载');
            
            // 添加一些交互效果
            const cards = document.querySelectorAll('.feature-card');
            cards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.boxShadow = '0 10px 30px rgba(0, 0, 0, 0.3)';
                });
                
                card.addEventListener('mouseleave', function() {
                    this.style.boxShadow = 'none';
                });
            });
        });
    </script>
</body>
</html>
