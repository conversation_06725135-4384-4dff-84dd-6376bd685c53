<?php
/**
 * 数据库配置文件 - 修复后台访问问题
 */

// 数据库配置 - 根据您提供的服务器信息
$db_config = [
    'host' => '127.0.0.1',
    'port' => '3306',
    'dbname' => 'xiaomeihuakefu_c',
    'username' => 'xiaomeihuakefu_c',
    'password' => '7Da5F1Xx995cxYz8',
    'charset' => 'utf8mb4'
];

try {
    // 创建PDO连接
    $dsn = "mysql:host={$db_config['host']};port={$db_config['port']};dbname={$db_config['dbname']};charset={$db_config['charset']}";
    $pdo = new PDO($dsn, $db_config['username'], $db_config['password'], [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_EMULATE_PREPARES => false,
    ]);
    
    // 设置时区
    $pdo->exec("SET time_zone = '+08:00'");
    
    error_log("xuxuemei后台数据库连接成功");
    
} catch (PDOException $e) {
    // 记录错误日志
    error_log("xuxuemei后台数据库连接失败: " . $e->getMessage());
    
    // 返回错误响应
    if (isset($_SERVER['HTTP_ACCEPT']) && strpos($_SERVER['HTTP_ACCEPT'], 'application/json') !== false) {
        header('Content-Type: application/json');
        echo json_encode(['error' => '数据库连接失败: ' . $e->getMessage()]);
    } else {
        echo "数据库连接失败，请检查配置: " . $e->getMessage();
    }
    exit();
}

/**
 * 执行SQL文件
 * @param string $sqlFile SQL文件路径
 * @return bool
 */
function executeSqlFile($sqlFile) {
    global $pdo;
    
    if (!file_exists($sqlFile)) {
        return false;
    }
    
    $sql = file_get_contents($sqlFile);
    $statements = explode(';', $sql);
    
    try {
        $pdo->beginTransaction();
        
        foreach ($statements as $statement) {
            $statement = trim($statement);
            if (!empty($statement)) {
                $pdo->exec($statement);
            }
        }
        
        $pdo->commit();
        return true;
    } catch (PDOException $e) {
        $pdo->rollBack();
        error_log("SQL执行失败: " . $e->getMessage());
        return false;
    }
}

/**
 * 检查表是否存在
 * @param string $tableName 表名
 * @return bool
 */
function tableExists($tableName) {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("SHOW TABLES LIKE ?");
        $stmt->execute([$tableName]);
        return $stmt->rowCount() > 0;
    } catch (PDOException $e) {
        return false;
    }
}

/**
 * 初始化APP设置表（如果不存在）
 */
function initAppSettingsTables() {
    global $pdo;
    
    try {
        // 检查管理员用户表是否存在
        if (!tableExists('admin_users')) {
            $pdo->exec("
                CREATE TABLE IF NOT EXISTS admin_users (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    username VARCHAR(50) NOT NULL UNIQUE,
                    password VARCHAR(255) NOT NULL,
                    email VARCHAR(100),
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
            ");
            
            // 创建默认管理员账户
            $defaultPassword = password_hash('admin123', PASSWORD_DEFAULT);
            $stmt = $pdo->prepare("INSERT IGNORE INTO admin_users (username, password, email) VALUES (?, ?, ?)");
            $stmt->execute(['admin', $defaultPassword, '<EMAIL>']);
            
            error_log("创建admin_users表和默认管理员账户成功");
        }
        
        // 检查系统设置表是否存在
        if (!tableExists('system_settings')) {
            $pdo->exec("
                CREATE TABLE IF NOT EXISTS system_settings (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    setting_key VARCHAR(100) NOT NULL UNIQUE,
                    setting_value TEXT,
                    description TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
            ");
            
            error_log("创建system_settings表成功");
        }
        
    } catch (PDOException $e) {
        error_log("初始化表失败: " . $e->getMessage());
    }
}

// 自动初始化表（如果需要）
if (php_sapi_name() !== 'cli') {
    initAppSettingsTables();
}
?>
