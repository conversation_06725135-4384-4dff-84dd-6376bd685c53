-- 创建安全日志表
CREATE TABLE IF NOT EXISTS `security_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `license_key` varchar(50) DEFAULT NULL COMMENT '卡密（部分）',
  `error_code` varchar(50) DEFAULT NULL COMMENT '错误代码',
  `ip_address` varchar(45) DEFAULT NULL COMMENT 'IP地址',
  `user_agent` text DEFAULT NULL COMMENT '用户代理',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_license_key` (`license_key`),
  KEY `idx_error_code` (`error_code`),
  KEY `idx_ip_address` (`ip_address`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='安全日志表';

-- 为license_keys表添加新字段（如果不存在）
ALTER TABLE `license_keys` 
ADD COLUMN IF NOT EXISTS `last_used_at` timestamp NULL DEFAULT NULL COMMENT '最后使用时间',
ADD COLUMN IF NOT EXISTS `last_heartbeat` timestamp NULL DEFAULT NULL COMMENT '最后心跳时间',
ADD COLUMN IF NOT EXISTS `last_used_ip` varchar(45) DEFAULT NULL COMMENT '最后使用IP';

-- 添加索引
ALTER TABLE `license_keys` 
ADD INDEX IF NOT EXISTS `idx_last_used_at` (`last_used_at`),
ADD INDEX IF NOT EXISTS `idx_last_heartbeat` (`last_heartbeat`),
ADD INDEX IF NOT EXISTS `idx_expiry_status` (`expiry_date`, `status`);
