<?php
/**
 * 卡密生成功能修复脚本
 * 自动检测并修复卡密生成相关的数据库问题
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);

// 包含必要的文件
require_once 'includes/db.php';
require_once 'includes/functions.php';

echo "<h1>卡密生成功能修复脚本</h1>\n";
echo "<style>
body { font-family: Arial, sans-serif; margin: 20px; }
.success { color: green; }
.error { color: red; }
.warning { color: orange; }
.info { color: blue; }
pre { background: #f5f5f5; padding: 10px; border-radius: 5px; }
</style>\n";

function logMessage($message, $type = 'info') {
    $class = $type;
    $icon = '📝';
    
    switch ($type) {
        case 'success':
            $icon = '✅';
            break;
        case 'error':
            $icon = '❌';
            break;
        case 'warning':
            $icon = '⚠️';
            break;
        case 'info':
            $icon = 'ℹ️';
            break;
    }
    
    echo "<p class='$class'>$icon $message</p>\n";
    error_log("[$type] $message");
}

// 检查数据库连接
if (!$pdo) {
    logMessage("数据库连接失败，无法继续修复", 'error');
    exit;
}

logMessage("开始修复卡密生成功能...", 'info');

try {
    // 1. 检查并修复license_keys表结构
    logMessage("步骤1: 检查license_keys表结构", 'info');
    
    // 检查表是否存在
    $stmt = $pdo->query("SHOW TABLES LIKE 'license_keys'");
    if ($stmt->rowCount() == 0) {
        logMessage("license_keys表不存在，正在创建...", 'warning');
        
        $create_table_sql = "
        CREATE TABLE `license_keys` (
          `id` int(11) NOT NULL AUTO_INCREMENT,
          `key_value` varchar(255) NOT NULL COMMENT '卡密值',
          `license_key` varchar(255) DEFAULT NULL COMMENT '卡密值（兼容字段）',
          `type` enum('hourly','daily','monthly','half_yearly','yearly') NOT NULL DEFAULT 'monthly',
          `status` enum('active','disabled','expired','deleted','unused','used','banned') NOT NULL DEFAULT 'active',
          `expiry_date` timestamp NULL DEFAULT NULL,
          `store_name` varchar(200) DEFAULT NULL,
          `wechat_store_id` varchar(100) DEFAULT NULL,
          `has_customer_service` tinyint(1) NOT NULL DEFAULT 1,
          `has_product_listing` tinyint(1) NOT NULL DEFAULT 0,
          `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
          `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          PRIMARY KEY (`id`),
          UNIQUE KEY `key_value` (`key_value`),
          KEY `idx_status` (`status`),
          KEY `idx_type` (`type`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='卡密表'";
        
        $pdo->exec($create_table_sql);
        logMessage("license_keys表创建成功", 'success');
    } else {
        logMessage("license_keys表已存在", 'success');
    }
    
    // 2. 检查并添加必要字段
    logMessage("步骤2: 检查必要字段", 'info');
    
    $required_fields = [
        'key_value' => "varchar(255) NOT NULL COMMENT '卡密值'",
        'type' => "enum('hourly','daily','monthly','half_yearly','yearly') NOT NULL DEFAULT 'monthly'",
        'status' => "enum('active','disabled','expired','deleted','unused','used','banned') NOT NULL DEFAULT 'active'",
        'expiry_date' => "timestamp NULL DEFAULT NULL",
        'has_customer_service' => "tinyint(1) NOT NULL DEFAULT 1",
        'has_product_listing' => "tinyint(1) NOT NULL DEFAULT 0",
        'created_at' => "timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP"
    ];
    
    $stmt = $pdo->query("SHOW COLUMNS FROM license_keys");
    $existing_columns = [];
    while ($row = $stmt->fetch()) {
        $existing_columns[] = $row['Field'];
    }
    
    foreach ($required_fields as $field => $definition) {
        if (!in_array($field, $existing_columns)) {
            logMessage("添加缺失字段: $field", 'warning');
            try {
                $pdo->exec("ALTER TABLE license_keys ADD COLUMN $field $definition");
                logMessage("字段 $field 添加成功", 'success');
            } catch (Exception $e) {
                logMessage("添加字段 $field 失败: " . $e->getMessage(), 'error');
            }
        } else {
            logMessage("字段 $field 已存在", 'info');
        }
    }
    
    // 3. 检查并添加索引
    logMessage("步骤3: 检查索引", 'info');
    
    $required_indexes = [
        'key_value' => 'UNIQUE',
        'idx_status' => 'INDEX',
        'idx_type' => 'INDEX'
    ];
    
    $stmt = $pdo->query("SHOW INDEX FROM license_keys");
    $existing_indexes = [];
    while ($row = $stmt->fetch()) {
        $existing_indexes[] = $row['Key_name'];
    }
    
    foreach ($required_indexes as $index => $type) {
        if (!in_array($index, $existing_indexes)) {
            logMessage("添加缺失索引: $index", 'warning');
            try {
                if ($type === 'UNIQUE') {
                    $pdo->exec("ALTER TABLE license_keys ADD UNIQUE KEY $index ($index)");
                } else {
                    $pdo->exec("ALTER TABLE license_keys ADD INDEX $index ($index)");
                }
                logMessage("索引 $index 添加成功", 'success');
            } catch (Exception $e) {
                logMessage("添加索引 $index 失败: " . $e->getMessage(), 'error');
            }
        } else {
            logMessage("索引 $index 已存在", 'info');
        }
    }
    
    // 4. 测试卡密生成
    logMessage("步骤4: 测试卡密生成功能", 'info');
    
    $test_key = generate_key();
    $expiry_date = (new DateTime())->modify('+1 month')->format('Y-m-d H:i:s');
    
    $stmt = $pdo->prepare(
        "INSERT INTO license_keys (key_value, type, expiry_date, status, has_customer_service, has_product_listing, created_at)
         VALUES (?, ?, ?, 'active', ?, ?, NOW())"
    );
    
    if ($stmt->execute([$test_key, 'monthly', $expiry_date, 1, 0])) {
        $key_id = $pdo->lastInsertId();
        logMessage("测试卡密生成成功: $test_key", 'success');
        
        // 清理测试数据
        $delete_stmt = $pdo->prepare("DELETE FROM license_keys WHERE id = ?");
        $delete_stmt->execute([$key_id]);
        logMessage("测试数据已清理", 'info');
    } else {
        $error_info = $stmt->errorInfo();
        logMessage("测试卡密生成失败: " . $error_info[2], 'error');
    }
    
    // 5. 检查权限
    logMessage("步骤5: 检查数据库权限", 'info');
    
    try {
        // 测试SELECT权限
        $pdo->query("SELECT 1 FROM license_keys LIMIT 1");
        logMessage("SELECT权限正常", 'success');
        
        // 测试INSERT权限（已在上面测试过）
        logMessage("INSERT权限正常", 'success');
        
        // 测试UPDATE权限
        $pdo->exec("UPDATE license_keys SET updated_at = NOW() WHERE 1=0");
        logMessage("UPDATE权限正常", 'success');
        
        // 测试DELETE权限
        $pdo->exec("DELETE FROM license_keys WHERE 1=0");
        logMessage("DELETE权限正常", 'success');
        
    } catch (Exception $e) {
        logMessage("权限检查失败: " . $e->getMessage(), 'error');
    }
    
    logMessage("修复完成！", 'success');
    logMessage("现在可以尝试生成卡密了。", 'info');
    
} catch (Exception $e) {
    logMessage("修复过程中发生错误: " . $e->getMessage(), 'error');
    logMessage("请检查数据库连接和权限设置。", 'warning');
}

echo "<h2>修复完成</h2>\n";
echo "<p><a href='test_key_generation.php'>点击这里测试卡密生成功能</a></p>\n";
echo "<p><a href='deploy/index.php?page=keys'>返回卡密管理页面</a></p>\n";

?>
