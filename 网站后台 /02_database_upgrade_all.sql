-- 小梅花智能AI客服系统 - 全功能升级脚本
-- 版本: v2.0.0 - 优化合并版
-- 日期: 2024-01-20
-- 功能: 升级现有数据库，添加所有新功能模块
-- 兼容: MySQL 5.7+ 和宝塔环境
-- 使用场景: 从旧版本升级到最新版本

SET NAMES utf8mb4;

-- ====================================
-- 商店信息验证功能升级
-- ====================================

-- 为license_keys表添加店铺信息字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'license_keys' 
     AND COLUMN_NAME = 'store_name') = 0,
    'ALTER TABLE license_keys ADD COLUMN store_name varchar(200) DEFAULT NULL COMMENT "绑定的店铺名称"',
    'SELECT "store_name字段已存在" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'license_keys' 
     AND COLUMN_NAME = 'wechat_store_id') = 0,
    'ALTER TABLE license_keys ADD COLUMN wechat_store_id varchar(100) DEFAULT NULL COMMENT "绑定的微信小店ID"',
    'SELECT "wechat_store_id字段已存在" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'license_keys' 
     AND COLUMN_NAME = 'user_wechat') = 0,
    'ALTER TABLE license_keys ADD COLUMN user_wechat varchar(100) DEFAULT NULL COMMENT "用户微信号"',
    'SELECT "user_wechat字段已存在" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加兼容字段（修复卡密生成问题）
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'license_keys' 
     AND COLUMN_NAME = 'license_key') = 0,
    'ALTER TABLE license_keys ADD COLUMN license_key varchar(255) DEFAULT NULL COMMENT "卡密值（兼容字段）"',
    'SELECT "license_key字段已存在" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 同步数据（将 key_value 复制到 license_key）
UPDATE `license_keys` SET `license_key` = `key_value` WHERE `license_key` IS NULL OR `license_key` = '';





-- 为scripts表添加默认AI设置字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'scripts' 
     AND COLUMN_NAME = 'default_ai_enabled') = 0,
    'ALTER TABLE scripts ADD COLUMN default_ai_enabled tinyint(1) DEFAULT 0 COMMENT "默认是否启用AI"',
    'SELECT "default_ai_enabled字段已存在" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- ====================================
-- 安全验证功能升级
-- ====================================

-- 创建SMTP配置表（如果不存在）
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'smtp_config') = 0,
    'CREATE TABLE smtp_config (
        id int(11) NOT NULL AUTO_INCREMENT,
        user_id int(11) NOT NULL,
        smtp_host varchar(255) NOT NULL,
        smtp_port int(11) NOT NULL DEFAULT 465,
        smtp_username varchar(255) NOT NULL,
        smtp_password varchar(255) NOT NULL,
        from_email varchar(255) DEFAULT NULL,
        from_name varchar(255) DEFAULT NULL,
        created_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        UNIQUE KEY user_smtp (user_id),
        KEY idx_user_id (user_id)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT="SMTP配置表"',
    'SELECT "smtp_config表已存在" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 创建验证码表（如果不存在）
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'verification_codes') = 0,
    'CREATE TABLE verification_codes (
        id int(11) NOT NULL AUTO_INCREMENT,
        user_id int(11) NOT NULL,
        code varchar(6) NOT NULL,
        type varchar(50) NOT NULL,
        expires_at timestamp NOT NULL,
        is_used tinyint(1) NOT NULL DEFAULT 0,
        created_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        KEY idx_user_code (user_id, code),
        KEY idx_expires (expires_at),
        KEY idx_type (type)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT="验证码表"',
    'SELECT "verification_codes表已存在" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- ====================================
-- 表结构兼容性升级
-- ====================================

-- 为scripts表添加兼容字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'scripts' 
     AND COLUMN_NAME = 'script_code') = 0,
    'ALTER TABLE scripts ADD COLUMN script_code longtext DEFAULT NULL COMMENT "脚本代码"',
    'SELECT "script_code字段已存在" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'scripts' 
     AND COLUMN_NAME = 'loader_code') = 0,
    'ALTER TABLE scripts ADD COLUMN loader_code longtext DEFAULT NULL COMMENT "加载器代码"',
    'SELECT "loader_code字段已存在" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 同步脚本数据（将 content 复制到 script_code）
UPDATE `scripts` SET `script_code` = `content` WHERE `script_code` IS NULL AND `content` IS NOT NULL;

-- 为license_keys表添加更多兼容字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'license_keys' 
     AND COLUMN_NAME = 'card_number') = 0,
    'ALTER TABLE license_keys ADD COLUMN card_number varchar(50) DEFAULT NULL COMMENT "卡号（兼容字段）"',
    'SELECT "card_number字段已存在" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'license_keys' 
     AND COLUMN_NAME = 'card_type') = 0,
    'ALTER TABLE license_keys ADD COLUMN card_type enum("hour","day","month","year") DEFAULT NULL COMMENT "卡密类型（兼容字段）"',
    'SELECT "card_type字段已存在" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'license_keys' 
     AND COLUMN_NAME = 'expiry_date') = 0,
    'ALTER TABLE license_keys ADD COLUMN expiry_date timestamp NULL DEFAULT NULL COMMENT "过期日期（兼容字段）"',
    'SELECT "expiry_date字段已存在" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 同步兼容数据
UPDATE `license_keys` SET `card_number` = `key_value` WHERE `card_number` IS NULL;
UPDATE `license_keys` SET `card_type` = `type` WHERE `card_type` IS NULL AND `type` IN ('hour','day','month','year');
UPDATE `license_keys` SET `expiry_date` = `expires_at` WHERE `expiry_date` IS NULL AND `expires_at` IS NOT NULL;

-- ====================================
-- 索引优化
-- ====================================

-- 添加索引（如果不存在）
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'license_keys' 
     AND INDEX_NAME = 'idx_store_info') = 0,
    'ALTER TABLE license_keys ADD INDEX idx_store_info (store_name, wechat_store_id)',
    'SELECT "idx_store_info索引已存在" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'license_keys' 
     AND INDEX_NAME = 'idx_license_key') = 0,
    'ALTER TABLE license_keys ADD INDEX idx_license_key (license_key)',
    'SELECT "idx_license_key索引已存在" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;



-- 更新系统设置默认值
INSERT IGNORE INTO `system_settings` (`setting_key`, `setting_value`, `description`) VALUES
('smtp_host', 'smtp.163.com', 'SMTP服务器地址'),
('smtp_port', '465', 'SMTP端口'),
('email_verification_enabled', '0', '是否启用邮箱验证'),
('login_notification_enabled', '0', '是否启用登录通知');

-- ====================================
-- 升级完成信息
-- ====================================

SELECT 
    '数据库升级完成！' as '升级状态',
    '所有功能模块已升级' as '功能状态',
    '新增功能：店铺验证、AI设置、安全验证、兼容性增强' as '升级详情';

-- 显示新增功能统计
SELECT 
    '店铺信息验证' as '功能模块',
    'store_name, wechat_store_id, user_wechat' as '新增字段',
    'license_keys表' as '影响表';

SELECT 
    'AI设置功能' as '功能模块',
    'ai_settings表, default_ai_enabled字段' as '新增内容',
    'scripts, license_keys表' as '影响表';

SELECT 
    '安全验证功能' as '功能模块',
    'smtp_config表, verification_codes表' as '新增表',
    '邮箱验证、登录通知' as '新增功能';