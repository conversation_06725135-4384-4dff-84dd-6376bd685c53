<?php
/**
 * 清理自动创建的弹窗数据
 * 删除所有自动创建的默认弹窗，确保只有后台手动创建的弹窗才会显示
 */

header('Content-Type: text/plain; charset=utf-8');

// 引入数据库配置
require_once __DIR__ . '/api/database_config.php';

echo "=== 清理自动创建的弹窗数据 ===" . PHP_EOL;
echo "执行时间: " . date('Y-m-d H:i:s') . PHP_EOL . PHP_EOL;

try {
    $db = getDatabase();
    $pdo = $db->getConnection();
    
    if (!$pdo) {
        echo "❌ 数据库连接失败" . PHP_EOL;
        exit(1);
    }
    
    echo "✅ 数据库连接成功" . PHP_EOL;
    
    // 1. 检查当前弹窗数据
    echo PHP_EOL . "1. 检查当前弹窗数据..." . PHP_EOL;
    
    $stmt = $pdo->query("SELECT id, title, status, created_at FROM app_popups ORDER BY created_at DESC");
    $popups = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($popups)) {
        echo "📭 没有找到任何弹窗数据" . PHP_EOL;
    } else {
        echo "发现 " . count($popups) . " 个弹窗:" . PHP_EOL;
        foreach ($popups as $popup) {
            echo "  - ID: {$popup['id']}, 标题: {$popup['title']}, 状态: {$popup['status']}, 创建时间: {$popup['created_at']}" . PHP_EOL;
        }
    }
    
    // 2. 识别自动创建的弹窗
    echo PHP_EOL . "2. 识别自动创建的弹窗..." . PHP_EOL;
    
    $autoCreatedTitles = [
        '🚀 欢迎使用小梅花AI智能客服',
        '小梅花AI助手',
        'fallback',
        'default'
    ];
    
    $autoCreatedIds = [];
    foreach ($popups as $popup) {
        if (in_array($popup['title'], $autoCreatedTitles) || 
            strpos($popup['title'], '欢迎使用小梅花') !== false ||
            strpos($popup['title'], 'AI助手') !== false) {
            $autoCreatedIds[] = $popup['id'];
            echo "  🎯 识别为自动创建: ID {$popup['id']} - {$popup['title']}" . PHP_EOL;
        }
    }
    
    if (empty($autoCreatedIds)) {
        echo "✅ 没有发现自动创建的弹窗" . PHP_EOL;
    } else {
        echo "发现 " . count($autoCreatedIds) . " 个自动创建的弹窗" . PHP_EOL;
    }
    
    // 3. 删除自动创建的弹窗
    if (!empty($autoCreatedIds)) {
        echo PHP_EOL . "3. 删除自动创建的弹窗..." . PHP_EOL;
        
        $action = $_GET['action'] ?? '';
        
        if ($action === 'delete') {
            $placeholders = str_repeat('?,', count($autoCreatedIds) - 1) . '?';
            $stmt = $pdo->prepare("DELETE FROM app_popups WHERE id IN ($placeholders)");
            $stmt->execute($autoCreatedIds);
            
            $deletedCount = $stmt->rowCount();
            echo "✅ 已删除 {$deletedCount} 个自动创建的弹窗" . PHP_EOL;
            
            // 验证删除结果
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM app_popups");
            $result = $stmt->fetch();
            echo "📊 剩余弹窗数量: {$result['count']}" . PHP_EOL;
            
        } else {
            echo "⚠️ 预览模式 - 要执行删除，请访问: ?action=delete" . PHP_EOL;
            echo "将要删除的弹窗ID: " . implode(', ', $autoCreatedIds) . PHP_EOL;
        }
    }
    
    // 4. 验证修复效果
    echo PHP_EOL . "4. 验证修复效果..." . PHP_EOL;
    
    // 测试API响应
    $testUrl = 'http://localhost/网站后台/api/popup.php';
    
    $context = stream_context_create([
        'http' => [
            'method' => 'GET',
            'timeout' => 10,
            'header' => 'User-Agent: XiaoMeiHua-App/3.0.0'
        ]
    ]);
    
    $response = file_get_contents($testUrl, false, $context);
    
    if ($response) {
        $data = json_decode($response, true);
        if ($data) {
            echo "API测试结果:" . PHP_EOL;
            echo "  - 成功: " . ($data['success'] ? '是' : '否') . PHP_EOL;
            echo "  - 消息: " . ($data['message'] ?? '无') . PHP_EOL;
            echo "  - 弹窗: " . ($data['popup'] ? '有' : '无') . PHP_EOL;
            
            if (!$data['popup']) {
                echo "✅ 修复成功！API不再返回自动创建的弹窗" . PHP_EOL;
            } else {
                echo "⚠️ API仍然返回弹窗，请检查是否有后台创建的弹窗" . PHP_EOL;
                echo "  弹窗标题: " . ($data['popup']['title'] ?? '未知') . PHP_EOL;
            }
        } else {
            echo "❌ API返回无效JSON" . PHP_EOL;
        }
    } else {
        echo "❌ 无法访问API" . PHP_EOL;
    }
    
    // 5. 总结
    echo PHP_EOL . "=== 修复总结 ===" . PHP_EOL;
    echo "✅ 已修改API逻辑，不再自动创建弹窗" . PHP_EOL;
    echo "✅ 已修改app逻辑，不再显示备用弹窗" . PHP_EOL;
    
    if (!empty($autoCreatedIds) && $action === 'delete') {
        echo "✅ 已删除 " . count($autoCreatedIds) . " 个自动创建的弹窗" . PHP_EOL;
    }
    
    echo "📝 现在只有后台手动创建的弹窗才会在app中显示" . PHP_EOL;
    echo "📝 如需创建弹窗，请使用后台管理界面" . PHP_EOL;
    
    echo PHP_EOL . "完成时间: " . date('Y-m-d H:i:s') . PHP_EOL;
    
} catch (Exception $e) {
    echo "❌ 执行失败: " . $e->getMessage() . PHP_EOL;
    echo "堆栈跟踪: " . $e->getTraceAsString() . PHP_EOL;
}
?>
