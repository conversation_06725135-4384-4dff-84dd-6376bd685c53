<?php
/**
 * 修复下载链接问题
 * 检查并更新有效的下载链接
 */

header('Content-Type: text/plain; charset=utf-8');
require_once 'includes/db.php';

echo "=== 修复下载链接问题 ===" . PHP_EOL;
echo "开始时间: " . date('Y-m-d H:i:s') . PHP_EOL . PHP_EOL;

try {
    // 1. 检查当前版本记录
    echo "1. 检查当前版本记录..." . PHP_EOL;
    $stmt = $pdo->query("SELECT * FROM app_updates WHERE status = 'published' ORDER BY created_at DESC");
    $versions = $stmt->fetchAll();
    
    echo "发现 " . count($versions) . " 个已发布版本:" . PHP_EOL;
    foreach ($versions as $version) {
        echo "  - ID: {$version['id']}, 版本: {$version['version']}, 强制更新: " . ($version['force_update'] ? '是' : '否') . PHP_EOL;
        echo "    macOS M1链接: " . ($version['dmg_m1_download_url'] ?: '无') . PHP_EOL;
        echo "    macOS Intel链接: " . ($version['dmg_intel_download_url'] ?: '无') . PHP_EOL;
        echo "    Windows链接: " . ($version['exe_download_url'] ?: '无') . PHP_EOL;
    }
    echo PHP_EOL;
    
    // 2. 测试蓝奏云链接
    echo "2. 测试蓝奏云代理..." . PHP_EOL;
    $testUrl = 'https://xiaomeihuakefu.cn/lanzou_api.php?url=https%3A%2F%2Fwwke.lanzoue.com%2FiLkyG32kxuhg&type=parse';
    
    $context = stream_context_create([
        'http' => [
            'method' => 'GET',
            'timeout' => 10,
            'header' => 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36'
        ]
    ]);
    
    $response = file_get_contents($testUrl, false, $context);
    if ($response) {
        $data = json_decode($response, true);
        if ($data && isset($data['success']) && $data['success']) {
            echo "✅ 蓝奏云代理工作正常" . PHP_EOL;
            echo "真实下载链接: " . ($data['data']['download_url'] ?? '未获取到') . PHP_EOL;
        } else {
            echo "⚠️ 蓝奏云代理返回错误: " . ($data['error'] ?? '未知错误') . PHP_EOL;
        }
    } else {
        echo "❌ 蓝奏云代理无法访问" . PHP_EOL;
    }
    echo PHP_EOL;
    
    // 3. 提供解决方案
    echo "3. 解决方案建议..." . PHP_EOL;
    
    // 方案1：更新版本号匹配
    echo "方案1：更新app版本号匹配" . PHP_EOL;
    echo "  - 将服务器版本5.0.0改为3.0.0，或者" . PHP_EOL;
    echo "  - 将app版本3.0.0升级到5.0.0" . PHP_EOL;
    echo PHP_EOL;
    
    // 方案2：修复下载链接
    echo "方案2：修复下载链接" . PHP_EOL;
    echo "  - 上传新的安装包到蓝奏云" . PHP_EOL;
    echo "  - 更新数据库中的下载链接" . PHP_EOL;
    echo "  - 或使用直链下载" . PHP_EOL;
    echo PHP_EOL;
    
    // 方案3：临时禁用强制更新
    echo "方案3：临时禁用强制更新" . PHP_EOL;
    echo "  - 将force_update设置为0" . PHP_EOL;
    echo "  - 允许用户跳过更新" . PHP_EOL;
    echo PHP_EOL;
    
    // 4. 执行修复操作
    echo "4. 执行修复操作..." . PHP_EOL;
    
    $action = $_GET['action'] ?? '';
    
    switch ($action) {
        case 'disable_force_update':
            echo "禁用强制更新..." . PHP_EOL;
            $stmt = $pdo->prepare("UPDATE app_updates SET force_update = 0 WHERE status = 'published'");
            $stmt->execute();
            echo "✅ 已禁用所有版本的强制更新" . PHP_EOL;
            break;
            
        case 'match_version':
            echo "匹配版本号（将5.0.0改为3.0.0）..." . PHP_EOL;
            $stmt = $pdo->prepare("UPDATE app_updates SET version = '3.0.0' WHERE version = '5.0.0' AND status = 'published'");
            $stmt->execute();
            echo "✅ 已将版本5.0.0更新为3.0.0" . PHP_EOL;
            break;
            
        case 'create_valid_version':
            echo "创建有效的3.0.0版本..." . PHP_EOL;
            
            // 先删除现有的5.0.0版本
            $stmt = $pdo->prepare("DELETE FROM app_updates WHERE version = '5.0.0'");
            $stmt->execute();
            
            // 创建3.0.0版本，无需更新
            $stmt = $pdo->prepare("
                INSERT INTO app_updates 
                (version, title, description, status, platform, force_update, created_at) 
                VALUES (?, ?, ?, ?, ?, ?, NOW())
            ");
            
            $stmt->execute([
                '3.0.0',
                '小梅花AI智能客服 v3.0.0',
                '当前稳定版本，无需更新',
                'published',
                'all',
                0
            ]);
            
            echo "✅ 已创建3.0.0版本记录，用户将不再收到更新提示" . PHP_EOL;
            break;
            
        default:
            echo "请选择修复操作：" . PHP_EOL;
            echo "  - ?action=disable_force_update  禁用强制更新" . PHP_EOL;
            echo "  - ?action=match_version         匹配版本号" . PHP_EOL;
            echo "  - ?action=create_valid_version  创建有效版本" . PHP_EOL;
    }
    
    echo PHP_EOL . "完成时间: " . date('Y-m-d H:i:s') . PHP_EOL;
    
} catch (Exception $e) {
    echo '❌ 错误: ' . $e->getMessage() . PHP_EOL;
    echo '堆栈跟踪: ' . $e->getTraceAsString() . PHP_EOL;
}
?>
