-- macOS架构支持功能 - 安全版本 (修复版)
-- 使用最简单的语法，避免复杂操作
-- 修复字符编码和兼容性问题

SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci;
SET FOREIGN_KEY_CHECKS = 0;

-- 1. 创建架构检测日志表
CREATE TABLE IF NOT EXISTS `macos_architecture_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_agent` text,
  `detected_architecture` varchar(20) DEFAULT 'unknown',
  `detection_method` varchar(50) DEFAULT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 2. 创建下载统计表
CREATE TABLE IF NOT EXISTS `download_stats_by_architecture` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `update_id` int(11) NOT NULL,
  `platform` varchar(50) NOT NULL,
  `download_count` int(11) DEFAULT 0,
  `last_download_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_update_platform` (`update_id`, `platform`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 3. 创建配置表
CREATE TABLE IF NOT EXISTS `macos_architecture_config` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `config_key` varchar(100) NOT NULL,
  `config_value` text,
  `description` text,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_config_key` (`config_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 4. 插入配置数据
INSERT IGNORE INTO `macos_architecture_config` (`config_key`, `config_value`, `description`) 
VALUES 
('default_architecture_preference', 'm1', '默认架构偏好'),
('enable_architecture_detection', '1', '是否启用架构检测'),
('architecture_detection_methods', 'user_agent', '架构检测方法'),
('download_url_format', 'separate', '下载链接格式');

-- 恢复外键检查
SET FOREIGN_KEY_CHECKS = 1;

-- 完成
SELECT 'macOS架构支持功能安装完成' as message;
