<?php
/**
 * 删除默认的1.0.1版本记录
 * 解决网站部署后APP更新中出现默认1.0.1版本的问题
 */

// 引入数据库连接
require_once __DIR__ . '/includes/db.php';

echo "开始清理默认的1.0.1版本记录...\n";

try {
    // 连接数据库
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "数据库连接成功\n";
    
    // 查询是否存在1.0.1版本记录
    $stmt = $pdo->prepare("SELECT * FROM app_updates WHERE version = '1.0.1'");
    $stmt->execute();
    $records = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($records)) {
        echo "没有找到1.0.1版本记录，无需清理\n";
    } else {
        echo "找到 " . count($records) . " 条1.0.1版本记录：\n";
        
        foreach ($records as $record) {
            echo "- ID: {$record['id']}, 标题: {$record['title']}, 状态: {$record['status']}, 创建时间: {$record['created_at']}\n";
        }
        
        // 询问是否删除
        echo "\n是否删除这些1.0.1版本记录？(y/n): ";
        $handle = fopen("php://stdin", "r");
        $line = fgets($handle);
        fclose($handle);
        
        if (trim(strtolower($line)) === 'y' || trim(strtolower($line)) === 'yes') {
            // 删除1.0.1版本记录
            $stmt = $pdo->prepare("DELETE FROM app_updates WHERE version = '1.0.1'");
            $result = $stmt->execute();
            
            if ($result) {
                $deletedCount = $stmt->rowCount();
                echo "成功删除 {$deletedCount} 条1.0.1版本记录\n";
            } else {
                echo "删除失败\n";
            }
        } else {
            echo "取消删除操作\n";
        }
    }
    
    // 显示当前所有版本记录
    echo "\n当前数据库中的版本记录：\n";
    $stmt = $pdo->query("SELECT id, version, title, status, created_at FROM app_updates ORDER BY created_at DESC");
    $allRecords = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($allRecords)) {
        echo "数据库中没有任何版本记录\n";
    } else {
        echo "ID\t版本号\t\t标题\t\t\t状态\t\t创建时间\n";
        echo "--------------------------------------------------------------------\n";
        foreach ($allRecords as $record) {
            $title = mb_strlen($record['title']) > 15 ? mb_substr($record['title'], 0, 15) . '...' : $record['title'];
            echo "{$record['id']}\t{$record['version']}\t\t{$title}\t\t{$record['status']}\t\t{$record['created_at']}\n";
        }
    }
    
    echo "\n清理完成！\n";
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
    exit(1);
}
