{"name": "dot-prop", "version": "9.0.0", "description": "Get, set, or delete a property from a nested object using a dot path", "license": "MIT", "repository": "sindresorhus/dot-prop", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": {"types": "./index.d.ts", "default": "./index.js"}, "sideEffects": false, "engines": {"node": ">=18"}, "scripts": {"test": "xo && ava && tsc", "bench": "node benchmark.js"}, "files": ["index.js", "index.d.ts"], "keywords": ["object", "prop", "property", "dot", "path", "get", "set", "delete", "access", "notation", "dotty"], "dependencies": {"type-fest": "^4.18.2"}, "devDependencies": {"ava": "^6.1.3", "benchmark": "^2.1.4", "expect-type": "^0.19.0", "typescript": "^5.4.5", "xo": "^0.58.0"}}