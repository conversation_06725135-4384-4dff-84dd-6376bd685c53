<?php
/**
 * 系统状态页面 - 快速检查
 */
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>小梅花AI客服系统 - 状态检查</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status-ok { color: #28a745; }
        .status-warning { color: #ffc107; }
        .status-error { color: #dc3545; }
        .status-info { color: #17a2b8; }
        .card {
            background: #f8f9fa;
            padding: 15px;
            margin: 15px 0;
            border-radius: 5px;
            border-left: 4px solid #007cba;
        }
        .btn {
            display: inline-block;
            padding: 10px 20px;
            background: #007cba;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            margin: 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn-success {
            background: #28a745;
        }
        .btn-warning {
            background: #ffc107;
            color: #212529;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌸 小梅花AI客服系统</h1>
        <h2>系统状态检查</h2>
        
        <?php
        // 检查PHP环境
        echo "<div class='card'>";
        echo "<h3>PHP环境</h3>";
        echo "<p>PHP版本: " . PHP_VERSION . "</p>";
        echo "<p>PDO扩展: " . (extension_loaded('pdo') ? '<span class="status-ok">✓ 已安装</span>' : '<span class="status-error">✗ 未安装</span>') . "</p>";
        echo "<p>PDO MySQL扩展: " . (extension_loaded('pdo_mysql') ? '<span class="status-ok">✓ 已安装</span>' : '<span class="status-error">✗ 未安装</span>') . "</p>";
        echo "</div>";
        
        // 检查关键文件
        echo "<div class='card'>";
        echo "<h3>关键文件检查</h3>";
        $files = [
            'deploy/login.php' => '部署版登录页面',
            'deploy/index.php' => '部署版主页面',
            'xuxuemei/login.php' => 'xuxuemei登录页面',
            'xuxuemei/index.php' => 'xuxuemei主页面',
            'includes/db.php' => '数据库配置文件'
        ];
        
        foreach ($files as $file => $desc) {
            if (file_exists($file)) {
                echo "<p>$desc: <span class='status-ok'>✓ 存在</span></p>";
            } else {
                echo "<p>$desc: <span class='status-error'>✗ 不存在</span></p>";
            }
        }
        echo "</div>";
        
        // 检查数据库连接
        echo "<div class='card'>";
        echo "<h3>数据库连接</h3>";
        try {
            require_once 'includes/db.php';
            if (is_database_available()) {
                echo "<p><span class='status-ok'>✓ 数据库连接正常</span></p>";
                
                // 检查表
                $stmt = $pdo->query("SHOW TABLES");
                $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
                echo "<p>数据库表数量: <span class='status-info'>" . count($tables) . "</span></p>";
                
                if (in_array('admin_users', $tables)) {
                    $stmt = $pdo->query("SELECT COUNT(*) as count FROM admin_users");
                    $result = $stmt->fetch();
                    echo "<p>管理员用户数量: <span class='status-info'>{$result['count']}</span></p>";
                }
            } else {
                echo "<p><span class='status-error'>✗ 数据库连接失败</span></p>";
            }
        } catch (Exception $e) {
            echo "<p><span class='status-error'>✗ 数据库错误: " . $e->getMessage() . "</span></p>";
        }
        echo "</div>";
        
        // 修复状态
        echo "<div class='card'>";
        echo "<h3>修复状态</h3>";
        echo "<p><span class='status-ok'>✓ getSecurityMonitor() 函数错误已修复</span></p>";
        echo "<p><span class='status-ok'>✓ 数据库配置已更新</span></p>";
        echo "<p><span class='status-ok'>✓ 错误处理机制已添加</span></p>";
        echo "</div>";
        ?>
        
        <div class="card">
            <h3>快速访问</h3>
            <a href="deploy/login.php" class="btn btn-success" target="_blank">部署版登录</a>
            <a href="xuxuemei/login.php" class="btn" target="_blank">xuxuemei登录</a>
            <a href="test_fix.php" class="btn btn-warning" target="_blank">详细测试</a>
        </div>
        
        <div class="card">
            <h3>默认登录信息</h3>
            <p><strong>用户名:</strong> admin</p>
            <p><strong>密码:</strong> admin123</p>
        </div>
        
        <div class="card">
            <h3>访问地址</h3>
            <p>本地测试: <code>http://localhost:8080/</code></p>
            <p>服务器访问: <code>http://xiaomeihuakefu.cn:8684/</code></p>
            <p>IP访问: <code>http://***************:8684/</code></p>
        </div>
        
        <div class="card">
            <h3>如果仍有问题</h3>
            <ol>
                <li>检查Web服务器是否运行在端口8684</li>
                <li>检查防火墙是否开放端口8684</li>
                <li>检查域名DNS解析是否正确</li>
                <li>查看Web服务器错误日志</li>
            </ol>
        </div>
    </div>
</body>
</html>
