<?php
/**
 * 卡密生成功能测试脚本
 * 用于诊断和修复卡密生成问题
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);

// 包含必要的文件
require_once 'includes/db.php';
require_once 'includes/functions.php';

echo "<h1>卡密生成功能测试</h1>\n";
echo "<style>
body { font-family: Arial, sans-serif; margin: 20px; }
.success { color: green; }
.error { color: red; }
.warning { color: orange; }
.info { color: blue; }
pre { background: #f5f5f5; padding: 10px; border-radius: 5px; }
</style>\n";

// 1. 测试数据库连接
echo "<h2>1. 数据库连接测试</h2>\n";
if ($pdo) {
    echo "<p class='success'>✅ 数据库连接成功</p>\n";
    
    // 检查数据库版本
    try {
        $stmt = $pdo->query("SELECT VERSION() as version");
        $version = $stmt->fetch();
        echo "<p class='info'>📊 数据库版本: " . htmlspecialchars($version['version']) . "</p>\n";
    } catch (Exception $e) {
        echo "<p class='warning'>⚠️ 无法获取数据库版本: " . htmlspecialchars($e->getMessage()) . "</p>\n";
    }
} else {
    echo "<p class='error'>❌ 数据库连接失败</p>\n";
    exit;
}

// 2. 检查license_keys表结构
echo "<h2>2. license_keys表结构检查</h2>\n";
try {
    $stmt = $pdo->query("SHOW COLUMNS FROM license_keys");
    $columns = $stmt->fetchAll();
    
    echo "<p class='success'>✅ license_keys表存在，包含 " . count($columns) . " 个字段</p>\n";
    
    // 检查关键字段
    $required_fields = ['id', 'key_value', 'type', 'status', 'expiry_date', 'has_customer_service', 'has_product_listing'];
    $existing_fields = array_column($columns, 'Field');
    
    echo "<h3>关键字段检查:</h3>\n";
    foreach ($required_fields as $field) {
        if (in_array($field, $existing_fields)) {
            echo "<p class='success'>✅ $field 字段存在</p>\n";
        } else {
            echo "<p class='error'>❌ $field 字段缺失</p>\n";
        }
    }
    
    // 检查可选字段
    $optional_fields = ['store_name', 'wechat_store_id', 'douyin_store_name', 'douyin_store_id', 'is_multi_store'];
    echo "<h3>可选字段检查:</h3>\n";
    foreach ($optional_fields as $field) {
        if (in_array($field, $existing_fields)) {
            echo "<p class='info'>ℹ️ $field 字段存在</p>\n";
        } else {
            echo "<p class='warning'>⚠️ $field 字段不存在（可选）</p>\n";
        }
    }
    
} catch (Exception $e) {
    echo "<p class='error'>❌ 检查表结构失败: " . htmlspecialchars($e->getMessage()) . "</p>\n";
}

// 3. 测试generate_key函数
echo "<h2>3. generate_key函数测试</h2>\n";
try {
    $test_key = generate_key();
    echo "<p class='success'>✅ generate_key函数正常工作</p>\n";
    echo "<p class='info'>📝 生成的测试卡密: <code>" . htmlspecialchars($test_key) . "</code></p>\n";
    
    // 检查卡密格式
    if (strpos($test_key, 'XMHS-') === 0) {
        echo "<p class='success'>✅ 卡密格式正确（以XMHS-开头）</p>\n";
    } else {
        echo "<p class='warning'>⚠️ 卡密格式异常</p>\n";
    }
    
} catch (Exception $e) {
    echo "<p class='error'>❌ generate_key函数测试失败: " . htmlspecialchars($e->getMessage()) . "</p>\n";
}

// 4. 测试卡密插入
echo "<h2>4. 卡密插入测试</h2>\n";
try {
    $test_key = generate_key();
    $expiry_date = (new DateTime())->modify('+1 month')->format('Y-m-d H:i:s');
    
    // 检查数据库是否支持抖店字段
    $has_douyin_fields = false;
    try {
        $check_stmt = $pdo->query("SHOW COLUMNS FROM license_keys LIKE 'douyin_store_name'");
        $has_douyin_fields = $check_stmt->rowCount() > 0;
    } catch (Exception $e) {
        // 忽略检查错误
    }
    
    if ($has_douyin_fields) {
        echo "<p class='info'>ℹ️ 数据库支持抖店字段，使用完整插入</p>\n";
        $stmt = $pdo->prepare(
            "INSERT INTO license_keys (key_value, type, store_name, wechat_store_id, douyin_store_name, douyin_store_id, expiry_date, status, has_customer_service, has_product_listing, is_multi_store, created_at)
             VALUES (?, ?, ?, ?, ?, ?, ?, 'active', ?, ?, ?, NOW())"
        );
        $result = $stmt->execute([$test_key, 'monthly', '测试店铺', 'test123', '测试抖店', 'dy123', $expiry_date, 1, 0, 0]);
    } else {
        echo "<p class='info'>ℹ️ 数据库不支持抖店字段，使用基础插入</p>\n";
        $stmt = $pdo->prepare(
            "INSERT INTO license_keys (key_value, type, store_name, wechat_store_id, expiry_date, status, has_customer_service, has_product_listing, created_at)
             VALUES (?, ?, ?, ?, ?, 'active', ?, ?, NOW())"
        );
        $result = $stmt->execute([$test_key, 'monthly', '测试店铺', 'test123', $expiry_date, 1, 0]);
    }
    
    if ($result) {
        $key_id = $pdo->lastInsertId();
        echo "<p class='success'>✅ 卡密插入成功，ID: $key_id</p>\n";
        echo "<p class='info'>📝 测试卡密: <code>" . htmlspecialchars($test_key) . "</code></p>\n";
        
        // 清理测试数据
        $delete_stmt = $pdo->prepare("DELETE FROM license_keys WHERE id = ?");
        $delete_stmt->execute([$key_id]);
        echo "<p class='info'>🧹 测试数据已清理</p>\n";
        
    } else {
        $error_info = $stmt->errorInfo();
        echo "<p class='error'>❌ 卡密插入失败: " . htmlspecialchars($error_info[2]) . "</p>\n";
    }
    
} catch (Exception $e) {
    echo "<p class='error'>❌ 卡密插入测试失败: " . htmlspecialchars($e->getMessage()) . "</p>\n";
}

// 5. 检查现有卡密数量
echo "<h2>5. 现有卡密统计</h2>\n";
try {
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM license_keys");
    $total = $stmt->fetch();
    echo "<p class='info'>📊 当前卡密总数: " . $total['total'] . "</p>\n";
    
    $stmt = $pdo->query("SELECT status, COUNT(*) as count FROM license_keys GROUP BY status");
    $status_counts = $stmt->fetchAll();
    
    if (!empty($status_counts)) {
        echo "<h3>按状态分组:</h3>\n";
        foreach ($status_counts as $status) {
            echo "<p class='info'>• " . htmlspecialchars($status['status']) . ": " . $status['count'] . " 个</p>\n";
        }
    }
    
} catch (Exception $e) {
    echo "<p class='error'>❌ 统计现有卡密失败: " . htmlspecialchars($e->getMessage()) . "</p>\n";
}

// 6. 权限检查
echo "<h2>6. 权限检查</h2>\n";
try {
    // 检查INSERT权限
    $stmt = $pdo->query("SHOW GRANTS");
    $grants = $stmt->fetchAll();
    
    $has_insert = false;
    foreach ($grants as $grant) {
        $grant_text = $grant['Grants for ' . array_keys($grant)[0]];
        if (strpos($grant_text, 'INSERT') !== false || strpos($grant_text, 'ALL PRIVILEGES') !== false) {
            $has_insert = true;
            break;
        }
    }
    
    if ($has_insert) {
        echo "<p class='success'>✅ 数据库用户具有INSERT权限</p>\n";
    } else {
        echo "<p class='error'>❌ 数据库用户可能缺少INSERT权限</p>\n";
    }
    
} catch (Exception $e) {
    echo "<p class='warning'>⚠️ 无法检查权限: " . htmlspecialchars($e->getMessage()) . "</p>\n";
}

echo "<h2>测试完成</h2>\n";
echo "<p>如果所有测试都通过，卡密生成功能应该正常工作。</p>\n";
echo "<p>如果仍有问题，请检查错误日志或联系技术支持。</p>\n";

?>
