-- macOS架构支持 - 只创建表结构

CREATE TABLE IF NOT EXISTS `macos_architecture_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_agent` text,
  `detected_architecture` varchar(20) DEFAULT 'unknown',
  `detection_method` varchar(50) DEFAULT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE IF NOT EXISTS `download_stats_by_architecture` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `update_id` int(11) NOT NULL,
  `platform` varchar(50) NOT NULL,
  `download_count` int(11) DEFAULT 0,
  `last_download_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
