-- 数据库升级脚本：为scripts表添加权限字段
-- 执行日期：2024年
-- 用途：支持脚本权限管理功能

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- 检查并添加has_wechat_store字段（如果不存在）
SET @sql = (SELECT IF((SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE table_name = 'scripts' AND table_schema = DATABASE() AND column_name = 'has_wechat_store') > 0, 'SELECT "has_wechat_store字段已存在"', 'ALTER TABLE `scripts` ADD COLUMN `has_wechat_store` tinyint(1) NOT NULL DEFAULT 0 COMMENT "支持微信小店功能"'));
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

-- 检查并添加has_douyin_store字段（如果不存在）
SET @sql = (SELECT IF((SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE table_name = 'scripts' AND table_schema = DATABASE() AND column_name = 'has_douyin_store') > 0, 'SELECT "has_douyin_store字段已存在"', 'ALTER TABLE `scripts` ADD COLUMN `has_douyin_store` tinyint(1) NOT NULL DEFAULT 0 COMMENT "支持抖店功能"'));
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

-- 恢复外键检查
SET FOREIGN_KEY_CHECKS = 1;

-- 显示升级完成信息
SELECT 
    '脚本权限字段升级完成！' as '升级状态',
    'has_wechat_store: 支持微信小店功能' as '新增字段1',
    'has_douyin_store: 支持抖店功能' as '新增字段2',
    '所有现有脚本默认权限为关闭状态' as '注意事项';
