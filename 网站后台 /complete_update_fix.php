<?php
/**
 * 完整的app更新问题修复方案
 * 一键解决版本不匹配和下载链接问题
 */

header('Content-Type: text/plain; charset=utf-8');
require_once 'includes/db.php';

echo "=== 完整的app更新问题修复 ===" . PHP_EOL;
echo "修复时间: " . date('Y-m-d H:i:s') . PHP_EOL . PHP_EOL;

try {
    // 1. 分析当前问题
    echo "1. 问题分析..." . PHP_EOL;
    
    $stmt = $pdo->query("SELECT * FROM app_updates WHERE status = 'published' ORDER BY created_at DESC");
    $publishedVersions = $stmt->fetchAll();
    
    echo "当前已发布版本:" . PHP_EOL;
    foreach ($publishedVersions as $version) {
        echo "  - 版本: {$version['version']}, 强制更新: " . ($version['force_update'] ? '是' : '否') . PHP_EOL;
        echo "    创建时间: {$version['created_at']}" . PHP_EOL;
        
        // 检查下载链接
        $hasValidLinks = false;
        if (!empty($version['dmg_m1_download_url'])) {
            echo "    macOS M1链接: 有" . PHP_EOL;
            $hasValidLinks = true;
        }
        if (!empty($version['dmg_intel_download_url'])) {
            echo "    macOS Intel链接: 有" . PHP_EOL;
            $hasValidLinks = true;
        }
        if (!empty($version['exe_download_url'])) {
            echo "    Windows链接: 有" . PHP_EOL;
            $hasValidLinks = true;
        }
        
        if (!$hasValidLinks) {
            echo "    ⚠️ 警告: 没有有效的下载链接" . PHP_EOL;
        }
    }
    echo PHP_EOL;
    
    // 2. 执行修复
    echo "2. 执行修复操作..." . PHP_EOL;
    
    // 删除所有现有的已发布版本
    echo "  - 清理现有版本..." . PHP_EOL;
    $stmt = $pdo->prepare("DELETE FROM app_updates WHERE status = 'published'");
    $stmt->execute();
    $deletedCount = $stmt->rowCount();
    echo "    已删除 {$deletedCount} 个版本" . PHP_EOL;
    
    // 创建与app当前版本匹配的记录
    echo "  - 创建匹配版本记录..." . PHP_EOL;
    $stmt = $pdo->prepare("
        INSERT INTO app_updates 
        (version, title, description, status, platform, force_update, created_at) 
        VALUES (?, ?, ?, ?, ?, ?, NOW())
    ");
    
    $stmt->execute([
        '3.0.0',
        '小梅花AI智能客服 v3.0.0',
        '当前稳定版本',
        'published',
        'all',
        0  // 非强制更新
    ]);
    
    echo "    ✅ 已创建版本3.0.0记录" . PHP_EOL;
    echo PHP_EOL;
    
    // 3. 验证修复结果
    echo "3. 验证修复结果..." . PHP_EOL;
    
    // 模拟app请求
    $testUrls = [
        'https://xiaomeihuakefu.cn/api/app_update_new.php?action=check&version=3.0.0&platform=macos',
        'https://xiaomeihuakefu.cn/api/app_update_macos_m1.php?action=check&version=3.0.0&platform=macos&arch=arm64'
    ];
    
    foreach ($testUrls as $url) {
        echo "测试: " . basename(parse_url($url, PHP_URL_PATH)) . PHP_EOL;
        
        $context = stream_context_create([
            'http' => [
                'method' => 'GET',
                'header' => 'User-Agent: XiaoMeiHua-App/3.0.0 (macOS; arm64)',
                'timeout' => 10
            ]
        ]);
        
        $response = file_get_contents($url, false, $context);
        if ($response) {
            $data = json_decode($response, true);
            if ($data && $data['success']) {
                $hasUpdate = $data['data']['has_update'] ?? false;
                echo "  结果: " . ($hasUpdate ? '有更新' : '无更新') . PHP_EOL;
                if (!$hasUpdate) {
                    echo "  ✅ 修复成功！app将不再提示更新" . PHP_EOL;
                } else {
                    echo "  ⚠️ 仍有更新提示，需要进一步检查" . PHP_EOL;
                }
            } else {
                echo "  ❌ API返回错误: " . ($data['message'] ?? '未知错误') . PHP_EOL;
            }
        } else {
            echo "  ❌ 无法访问API" . PHP_EOL;
        }
        echo PHP_EOL;
    }
    
    // 4. 提供后续建议
    echo "4. 后续建议..." . PHP_EOL;
    echo "  ✅ 立即生效：用户重启app后将不再收到更新提示" . PHP_EOL;
    echo "  📝 版本管理：如需发布新版本，请确保：" . PHP_EOL;
    echo "     - 版本号大于3.0.0（如3.0.1、3.1.0等）" . PHP_EOL;
    echo "     - 提供有效的下载链接" . PHP_EOL;
    echo "     - 测试下载链接可访问性" . PHP_EOL;
    echo "  🔧 app端：建议在下次版本中增强下载链接验证" . PHP_EOL;
    echo PHP_EOL;
    
    // 5. 生成测试报告
    echo "5. 生成测试报告..." . PHP_EOL;
    
    $reportData = [
        'fix_time' => date('Y-m-d H:i:s'),
        'deleted_versions' => $deletedCount,
        'created_version' => '3.0.0',
        'status' => 'success',
        'next_steps' => [
            '用户重启app验证',
            '监控更新日志',
            '准备下个版本发布'
        ]
    ];
    
    $reportFile = 'update_fix_report_' . date('Ymd_His') . '.json';
    file_put_contents($reportFile, json_encode($reportData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
    echo "  📄 测试报告已保存: {$reportFile}" . PHP_EOL;
    
    echo PHP_EOL . "🎉 修复完成！" . PHP_EOL;
    echo "用户现在重启app应该不会再看到更新提示了。" . PHP_EOL;
    
} catch (Exception $e) {
    echo '❌ 修复失败: ' . $e->getMessage() . PHP_EOL;
    echo '堆栈跟踪: ' . $e->getTraceAsString() . PHP_EOL;
}
?>
