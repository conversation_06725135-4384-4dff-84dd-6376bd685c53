-- 添加多店铺支持的数据库升级脚本
-- 创建时间: 2025-01-18

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- 创建卡密-店铺关联表
CREATE TABLE IF NOT EXISTS `license_key_stores` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `license_key_id` int(11) NOT NULL COMMENT '关联的卡密ID',
  `store_name` varchar(200) NOT NULL COMMENT '店铺名称',
  `wechat_store_id` varchar(100) NOT NULL COMMENT '微信小店ID',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_license_key_id` (`license_key_id`),
  KEY `idx_store_info` (`store_name`, `wechat_store_id`),
  CONSTRAINT `fk_license_key_stores` FOREIGN KEY (`license_key_id`) REFERENCES `license_keys` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='卡密-店铺关联表';

-- 修改license_keys表，添加is_multi_store字段
ALTER TABLE `license_keys` 
ADD COLUMN `is_multi_store` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否为多店铺卡密' AFTER `has_product_listing`;

-- 确保store_name和wechat_store_id字段存在
ALTER TABLE `license_keys` 
MODIFY COLUMN `store_name` varchar(200) NOT NULL COMMENT '绑定的店铺名称',
MODIFY COLUMN `wechat_store_id` varchar(100) NOT NULL COMMENT '绑定的微信小店ID';

SET FOREIGN_KEY_CHECKS = 1;

SELECT 'Database upgrade completed successfully!' AS message; 


-- 功能升级完成
SELECT "功能升级脚本执行完成" as message;