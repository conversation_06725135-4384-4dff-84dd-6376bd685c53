# 小梅花系统安全配置
# 保护敏感文件和目录

# 启用重写引擎
<IfModule mod_rewrite.c>
    RewriteEngine On
</IfModule>

# 保护敏感文件
<FilesMatch "^(\.env|.*\.json|.*\.md|.*\.sql)$">
    Order allow,deny
    Deny from all
</FilesMatch>

# 保护config目录
<DirectoryMatch "^/config/">
    Order allow,deny
    Deny from all
</DirectoryMatch>

# 保护backup_sql目录
<DirectoryMatch "^/backup_sql/">
    Order allow,deny
    Deny from all
</DirectoryMatch>

# 禁止访问备份文件
<FilesMatch "\.(bak|old|backup|orig|log|sql)$">
    Order allow,deny
    Deny from all
</FilesMatch>

# 允许访问特定的.json文件（如需要）
<Files "manifest.json">
    Order deny,allow
    Allow from all
</Files>

# 禁止列出目录
Options -Indexes

# 设置默认字符集
AddDefaultCharset UTF-8

# 启用PHP错误日志但不显示在页面上
php_flag display_errors off
php_flag log_errors on
php_value error_log logs/php_error.log

# 禁止访问.git目录
RedirectMatch 404 /\.git

# AI知识库访问控制 - 优化APP访问
<Files "ai-knowledge.html">
    # 设置环境变量来检查User-Agent（更宽松的检查）
    SetEnvIf User-Agent "xiaomeihua-app" allowed_app
    SetEnvIf User-Agent "Electron" allowed_app
    SetEnvIf User-Agent "Chrome.*Electron" allowed_app
    SetEnvIf User-Agent "Chrome" allowed_app

    # 暂时允许所有访问，由页面内的JavaScript进行更细粒度的控制
    Order Allow,Deny
    Allow from all

    # 设置特殊的安全头
    <IfModule mod_headers.c>
        Header always set X-Frame-Options "SAMEORIGIN"
        Header always set X-Content-Type-Options "nosniff"
        Header always set X-XSS-Protection "1; mode=block"
        Header always set Referrer-Policy "strict-origin-when-cross-origin"
        Header always set Content-Security-Policy "default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; frame-ancestors 'self';"
    </IfModule>
</Files>

# 保护AI知识库开发目录
<Files "AI知识库开发">
    Order Deny,Allow
    Deny from all
</Files>

# 设置安全标头
<IfModule mod_headers.c>
    Header set X-Content-Type-Options "nosniff"
    Header set X-Frame-Options "SAMEORIGIN"
    Header set X-XSS-Protection "1; mode=block"
    Header set Strict-Transport-Security "max-age=31536000; includeSubDomains; preload"
    Header set Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data:; font-src 'self' data:; connect-src 'self'"
</IfModule>
